<?xml version="1.0" encoding="utf-8" ?>
  
<Action>

  <Component type="Vanilla">
    <![CDATA[{
      templateInEvent: "html/card.dot",
      css: "styles/card.css",
      orientation: "vertical",
      header: "!{}",
      valign: false,
      animate: "slideInUp",
      content: {
        img_embed: { 
          materialboxed: false, borders: false, frame: "", nopadding: false,
          src: "!{SIM_CaseStudy_Img_C5}",  alt: "!{SIM_CaseStudy_Title}" ,
          position: "right", style: "",
          src_vert: "!{}"
        },
        _img: { 
          materialboxed: false, borders: false, frame: "", nopadding: false,
          src: "!{SIM_CaseStudy_Img}",  alt: "!{SIM_CaseStudy_Title}" ,
          src_vert: "!{}"
        },
        position: "up",
        title: "!{SIM_CaseStudy7_Title}",
        body: "!{SIM_CaseStudy7_Text}"
      }      
    }]]>
  </Component>
  
  

</Action>