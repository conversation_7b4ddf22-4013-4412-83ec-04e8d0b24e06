<?xml version="1.0" encoding="utf-8" ?>
<Action disableDynamicVotes="false" mainAreaLayout="../../../layout/mainLayoutFAC" layout="../../../layout/tabsLayout">
  <Include name="HeaderFAC_R2"></Include>

  <!-- Tabs component -->
  <Component type="Tabs" customJS="true"><![CDATA[{
      templateInEvent: "html/tabs.dot",
      css: "styles/tabs.css",
      header: "!{SIM_R1_FAC_dashboard_Header_Part2}",
      instructions: "!{}",
      swipeable: true,
      tabs: [
          "!{SIM_R2_FAC_dashboard_control}",
          "!{SIM_Scenario_Tab1}",
          "!{SIM_R2_FAC_dashboard_roleplay}",
          "!{SIM_R2_FAC_dashboard_ranking}"
      ],
      scope: null
  }]]></Component>


<!-- ********* -->
<!-- TAB SCREENS -->
<!-- ********* -->

  <Component type="ActionMenu" customJS="true"><![CDATA[{
      templateInEvent: "html/collection.dot",
      css: "styles/collection.css",
      _header: "!{}",
      intro: "!{Logins_Table_Screen}: ",
      withContainer: true,
      bgColor: "white _margin-right halfsize",
      highlightActiveScreen: true,
      highlightByQuestion: "Q_FAC_Navigation_Screen",
      trackHighlight: true,
      items: [
        {
          gdActionEmbed: "SIM_AGG",
          gdActionTrack: "GD",
          gdActionSection: "SIM_R2_Scenario1",
          targetSection: "SIM_R2_FAC_dashboard",
          label: "!{SIM_BreadCrumbs_3}",
          icon: "looks_3"
        },
        {
          gdActionEmbed: "SIM_R2_Scenario1_AGG",
          gdActionTrack: "GD",
          gdActionSection: "SIM_R2_Scenario1_FB",
          targetSection: "SIM_R2_FAC_dashboard",
          label: "!{SIM_BreadCrumbs_3} !{Navigation_feedback}",
          icon: "question_answer"
        },
        {
          gdActionEmbed: "SIM_AGG",
          gdActionTrack: "GD",
          gdActionSection: "SIM_R2_Scenario2",
          targetSection: "SIM_R2_FAC_dashboard",
          label: "!{SIM_BreadCrumbs_4} (!{SIM_R2_FAC_dashboard_roleplay})",
          icon: "looks_4"
        },
        {
          gdActionEmbed: "SIM_AGG_FULL",
          gdActionTrack: "GD",
          classes: "highlight",
          targetSection: "SIM_R2_FAC_dashboard",
          label: "!{GD_Agg}",
          icon: "speed"
        }
      ],
      scope: [ "Q_FAC_Navigation_Screen" ]
  }]]></Component>


<!-- ***** -->
<!-- TAB 1 -->
<!-- ***** -->
    
  <Component type="IndividualResults" customJS="true"><![CDATA[{
    templateInEvent: "html/rankingTable.dot",
    css: "styles/rankingTable.css",
    header: "!{}",
    instructions: "!{CheckboxesInstructionsAnswers}",   
    position: "",
    userHeader: "!{Ranking_User}",
    _me: "!{Me}",
    boundName: "Q_My_Name",
    showOriginalName: false,
    avatar: " ",
    boundAvatar: "Q_My_Avatar",
    defaultAvatar: "!{defaultAvatar}",
    isDataTables: false,
    class: "_responsive-table_ myresponsive-table _verticalMode_ subheaders noBold fixed",
    headerMultiLines: true,
    headerEllipsis: true,
    questions: [
      {
        show: true,
        title: "!{SIM_R2_Scenario2_Header}: !{SIM_R2_Scenario1_Title}",
        binding: "Q_SIM_R2_Scenario1"
      }
    ],
    answervalidation: true,
    subheaders: [
      "",
      "!{}",
      "!{SIM_R2_Scenario1_FB_Solution}"
    ],
    trackQuestion: "",
    rankQuestion: "",
    sortByQuestion: "",
    sortOrder: "asc",
    listSkip: 0,
    listLength:100,
    showNear: 5,
    liveUpdate: true,
    markUpdate: true
  }]]></Component>
  
<!-- ***** -->
<!-- TAB 2 -->
<!-- ***** -->

<Component type="AssessForm" customJS="true"><![CDATA[{
  templateInEvent: "html/assessForm.dot",
  css: "styles/assessForm.css",
  header: "!{}",
  animate: "fadeIn",
  id: "assessForm1",
  class: "responsive-table _bigHeaders_ _firstColumnBold_ centered",
  trackQuestion: "GD",
  filterQuestion: "Filter_Agg",
  teamNameQuestion: "Q_My_Name",
  dynamic: true,
  table: {
    title: "!{SIM_Roleplay_R2_table_Title}",
    body: "!{Header_Modal_Assess_Text}",
    questions: [
      {
        name: "Score_SIM_R2_Roleplay",
        _label: ""
      }
    ]
  },
  submitBtn: {
    label: "!{Header_Modal_Assess_Submit}",
    response: {
      message: "!{SIM_Roleplay_R2_Submit_Success}",
      showParticipantEmail: true,
      showVote: true
    },
    gdActionEmbed: "SIM_AGG"
  }   
}]]></Component>

<!-- ******* -->
<!-- RANKING -->
<!-- ******* -->

  <Component type="IndividualResults" customJS="true"><![CDATA[{
    templateInEvent: "html/rankingTable.dot",
    css: "styles/rankingTable.css",
    class: "",
    position: "#",
    header: "!{}",
    userHeader: "!{Ranking_User}",
    boundName: "Q_My_Name",
    showOriginalName: true,
    avatar: " ",
    boundAvatar: "Q_My_Avatar",
    defaultAvatar: "!{defaultAvatar}",
    isDataTables: false,
    class: "_verticalMode_ myresponsive-table _noBold _fixed",
    questions: [
      {
        show: true,
        title: "!{KPI_Metric1}",
        binding: "Score_SIM_Total_R2_KPI1",
				format: "0"
      },
      {
        show: true,
        title: "!{KPI_Metric2}",
        binding: "Score_SIM_Total_R2_KPI2",
				format: "0"
      },
      {
        show: true,
        title: "!{KPI_Metric3}",
        binding: "Score_SIM_Total_R2_KPI3",
				format: "0"
      },
      {
        show: true,
        title: "!{KPI_Metric4}",
        binding: "Score_SIM_Total_R2_KPI4",
				format: "0"
      },
      {
        show: true,
        title: "!{SIM_R2_FAC_debrief_roleplay}",
        binding: "Score_SIM_R2_Roleplay_Impact",
				format: "0.0"
      },
      {
        show: true,
        title: "!{KPI_TOTAL}",
        binding: "Score_SIM_Total_R2",
				format: "0.0"
      }
    ],
    trackQuestion: "GD",
    rankQuestion: "",
    sortByQuestion: "Score_SIM_Total_R2",
    sortOrder: "desc",
    listSkip: 0,
    listLength:100,
    showNear: 5,
    liveUpdate: true,
    markUpdate: true
  }]]></Component>
  






  <!-- Navigation component
       parameters:
        - aligned:
        - animate: kind of animation
        - id: to be located and shown when is hidden
        - isHidden: attribute to hide
        - buttons: array of buttons (previous, next or target)
   -->
  <Component type="Navigation" customJS="true">
    <![CDATA[{
      templateInEvent: "html/navigation.dot",
      css: "styles/navigation.css",
      aligned: "right",
      animate: "fadeInLeft animate__delay-2s",
      id: "navButton",
      isHidden: false, showDelay: "",
      hasModals: true,
      buttons: [
        {
          type: "modal",
          pulse: false,
          popupID: "Modal_menu",
          popup: "FAC_Navigation",
          close: "!{Header_LinkClose}",
          label: "!{Navigation_menu}",
          icon: "list"
        },
        {
          type: "target",
          pulse: false,
          isLarge: false,
          gdActionEmbed: "SIM_AGG",
          gdActionTrack: "GD",
          gdActionSection: "R2_DebriefPage",
          targetSection: "R2_DebriefPage_FAC",
          label: "!{GD_SIM_R1_Finish_Part2}",
          icon: "cancel"
        }
      ],
      
      isFollower: "Follower",
      scope: ["Follower"]
    }]]>
  </Component>


  <Voting autoNext="false">
    <Score type="Vote" result="Q_FAC_Navigation_Tab" response="0"/> 
  </Voting>



</Action>