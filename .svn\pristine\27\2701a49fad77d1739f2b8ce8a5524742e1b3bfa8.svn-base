<?xml version="1.0" encoding="utf-8" ?>
<Action autoNext="true">

  <!-- ************************************************************ -->
  <!-- *********************** AGGREGATION  *********************** -->
  <!-- ************************************************************ -->

  <Aggregator doneText="">

    <ParticipantFilter>
      <Question>Filter_Agg</Question>
    </ParticipantFilter>

    <!-- KPI1 -->
    <Score result="Score_SIM_R1_Scenario4_KPI1" type="Choice"> 
      <Question name="Q_SIM_R1_Scenario4">
          <Choice value="1" Response="!{SIM_R1_Scenario4_Opt1_KPI1}"></Choice>
          <Choice value="2" Response="!{SIM_R1_Scenario4_Opt2_KPI1}"></Choice>
          <Choice value="3" Response="!{SIM_R1_Scenario4_Opt3_KPI1}"></Choice>
          <Choice value="4" Response="!{SIM_R1_Scenario4_Opt4_KPI1}"></Choice>
      </Question>
    </Score> 
    
    <!-- KPI2 -->
    <Score result="Score_SIM_R1_Scenario4_KPI2" type="Choice"> 
      <Question name="Q_SIM_R1_Scenario4">
          <Choice value="1" Response="!{SIM_R1_Scenario4_Opt1_KPI2}"></Choice>
          <Choice value="2" Response="!{SIM_R1_Scenario4_Opt2_KPI2}"></Choice>
          <Choice value="3" Response="!{SIM_R1_Scenario4_Opt3_KPI2}"></Choice>
          <Choice value="4" Response="!{SIM_R1_Scenario4_Opt4_KPI2}"></Choice>
      </Question>
    </Score> 

    <!-- KPI3 -->
    <Score result="Score_SIM_R1_Scenario4_KPI3" type="Choice"> 
      <Question name="Q_SIM_R1_Scenario4">
          <Choice value="1" Response="!{SIM_R1_Scenario4_Opt1_KPI3}"></Choice>
          <Choice value="2" Response="!{SIM_R1_Scenario4_Opt2_KPI3}"></Choice>
          <Choice value="3" Response="!{SIM_R1_Scenario4_Opt3_KPI3}"></Choice>
          <Choice value="4" Response="!{SIM_R1_Scenario4_Opt4_KPI3}"></Choice>
      </Question>
    </Score>

    <!-- KPI4 -->
    <Score result="Score_SIM_R1_Scenario4_KPI4" type="Choice"> 
      <Question name="Q_SIM_R1_Scenario4">
          <Choice value="1" Response="!{SIM_R1_Scenario4_Opt1_KPI4}"></Choice>
          <Choice value="2" Response="!{SIM_R1_Scenario4_Opt2_KPI4}"></Choice>
          <Choice value="3" Response="!{SIM_R1_Scenario4_Opt3_KPI4}"></Choice>
          <Choice value="4" Response="!{SIM_R1_Scenario4_Opt4_KPI4}"></Choice>
      </Question>
    </Score>

    <!-- KPI5 -->
    <Score result="Score_SIM_R1_Scenario4_KPI5" type="Choice"> 
      <Question name="Q_SIM_R1_Scenario4">
          <Choice value="1" Response="!{SIM_R1_Scenario4_Opt1_KPI5}"></Choice>
          <Choice value="2" Response="!{SIM_R1_Scenario4_Opt2_KPI5}"></Choice>
          <Choice value="3" Response="!{SIM_R1_Scenario4_Opt3_KPI5}"></Choice>
          <Choice value="4" Response="!{SIM_R1_Scenario4_Opt4_KPI5}"></Choice>
      </Question>
    </Score>

    <!-- KPI6 -->
    <Score result="Score_SIM_R1_Scenario4_KPI6" type="Choice"> 
      <Question name="Q_SIM_R1_Scenario4">
          <Choice value="1" Response="!{SIM_R1_Scenario4_Opt1_KPI6}"></Choice>
          <Choice value="2" Response="!{SIM_R1_Scenario4_Opt2_KPI6}"></Choice>
          <Choice value="3" Response="!{SIM_R1_Scenario4_Opt3_KPI6}"></Choice>
          <Choice value="4" Response="!{SIM_R1_Scenario4_Opt4_KPI6}"></Choice>
      </Question>
    </Score>

    <!-- KPI8 -->
    <Score result="Score_SIM_R1_Scenario4_KPI8" type="Choice"> 
      <Question name="Q_SIM_R1_Scenario4">
          <Choice value="1" Response="!{SIM_R1_Scenario4_Opt1_KPI8}"></Choice>
          <Choice value="2" Response="!{SIM_R1_Scenario4_Opt2_KPI8}"></Choice>
          <Choice value="3" Response="!{SIM_R1_Scenario4_Opt3_KPI8}"></Choice>
          <Choice value="4" Response="!{SIM_R1_Scenario4_Opt4_KPI8}"></Choice>
      </Question>
    </Score>

    <!-- KPI9 -->
    <Score result="Score_SIM_R1_Scenario4_KPI9" type="Choice"> 
      <Question name="Q_SIM_R1_Scenario4">
          <Choice value="1" Response="!{SIM_R1_Scenario4_Opt1_KPI9}"></Choice>
          <Choice value="2" Response="!{SIM_R1_Scenario4_Opt2_KPI9}"></Choice>
          <Choice value="3" Response="!{SIM_R1_Scenario4_Opt3_KPI9}"></Choice>
          <Choice value="4" Response="!{SIM_R1_Scenario4_Opt4_KPI9}"></Choice>
      </Question>
    </Score>

    <!-- KPI10 -->
    <Score result="Score_SIM_R1_Scenario4_KPI10" type="Choice"> 
      <Question name="Q_SIM_R1_Scenario4">
          <Choice value="1" Response="!{SIM_R1_Scenario4_Opt1_KPI10}"></Choice>
          <Choice value="2" Response="!{SIM_R1_Scenario4_Opt2_KPI10}"></Choice>
          <Choice value="3" Response="!{SIM_R1_Scenario4_Opt3_KPI10}"></Choice>
          <Choice value="4" Response="!{SIM_R1_Scenario4_Opt4_KPI10}"></Choice>
      </Question>
    </Score>

    <!-- KPI11 -->
    <Score result="Score_SIM_R1_Scenario4_KPI11" type="Choice"> 
      <Question name="Q_SIM_R1_Scenario4">
          <Choice value="1" Response="!{SIM_R1_Scenario4_Opt1_KPI11}"></Choice>
          <Choice value="2" Response="!{SIM_R1_Scenario4_Opt2_KPI11}"></Choice>
          <Choice value="3" Response="!{SIM_R1_Scenario4_Opt3_KPI11}"></Choice>
          <Choice value="4" Response="!{SIM_R1_Scenario4_Opt4_KPI11}"></Choice>
      </Question>
    </Score>

    <!-- KPI12 -->
    <Score result="Score_SIM_R1_Scenario4_KPI12" type="Choice"> 
      <Question name="Q_SIM_R1_Scenario4">
          <Choice value="1" Response="!{SIM_R1_Scenario4_Opt1_KPI12}"></Choice>
          <Choice value="2" Response="!{SIM_R1_Scenario4_Opt2_KPI12}"></Choice>
          <Choice value="3" Response="!{SIM_R1_Scenario4_Opt3_KPI12}"></Choice>
          <Choice value="4" Response="!{SIM_R1_Scenario4_Opt4_KPI12}"></Choice>
      </Question>
    </Score>
    
    <!-- KPI13 -->
    <Score result="Score_SIM_R1_Scenario4_KPI13" type="Choice"> 
      <Question name="Q_SIM_R1_Scenario4">
          <Choice value="1" Response="!{SIM_R1_Scenario4_Opt1_KPI13}"></Choice>
          <Choice value="2" Response="!{SIM_R1_Scenario4_Opt2_KPI13}"></Choice>
          <Choice value="3" Response="!{SIM_R1_Scenario4_Opt3_KPI13}"></Choice>
          <Choice value="4" Response="!{SIM_R1_Scenario4_Opt4_KPI13}"></Choice>
      </Question>
    </Score>
    

    <!-- ******************************************************* -->
    <!-- *********************** TOTALS  *********************** -->
    <!-- ******************************************************* -->

    <!-- TOTAL R1 Base KPIs -->
    
    <Total result="Score_SIM_Total_R1_KPI1" method="sum">
      <Question validate="false">Score_SIM_Init_KPI1</Question>
      <Question validate="false">Score_SIM_R1_Scenario1_KPI1</Question>
      <Question validate="false">Score_SIM_R1_Scenario2_KPI1</Question>
      <Question validate="false">Score_SIM_R1_Scenario3_KPI1</Question>
      <Question validate="false">Score_SIM_R1_Scenario4_KPI1</Question>
    </Total>
    <Total result="Score_SIM_Total_R1_KPI2" method="sum">
      <Question validate="false">Score_SIM_Init_KPI2</Question>
      <Question validate="false">Score_SIM_R1_Scenario1_KPI2</Question>
      <Question validate="false">Score_SIM_R1_Scenario2_KPI2</Question>
      <Question validate="false">Score_SIM_R1_Scenario3_KPI2</Question>
      <Question validate="false">Score_SIM_R1_Scenario4_KPI2</Question>
    </Total>
    <Total result="Score_SIM_Total_R1_KPI3" method="sum">
      <Question validate="false">Score_SIM_Init_KPI3</Question>
      <Question validate="false">Score_SIM_R1_Scenario1_KPI3</Question>
      <Question validate="false">Score_SIM_R1_Scenario2_KPI3</Question>
      <Question validate="false">Score_SIM_R1_Scenario3_KPI3</Question>
      <Question validate="false">Score_SIM_R1_Scenario4_KPI3</Question>
    </Total>
    <Total result="Score_SIM_Total_R1_KPI4" method="sum">
      <Question validate="false">Score_SIM_Init_KPI4</Question>
      <Question validate="false">Score_SIM_R1_Scenario1_KPI4</Question>
      <Question validate="false">Score_SIM_R1_Scenario2_KPI4</Question>
      <Question validate="false">Score_SIM_R1_Scenario3_KPI4</Question>
      <Question validate="false">Score_SIM_R1_Scenario4_KPI4</Question>
    </Total>
    <Total result="Score_SIM_Total_R1_KPI5" method="sum">
      <Question validate="false">Score_SIM_Init_KPI5</Question>
      <Question validate="false">Score_SIM_R1_Scenario1_KPI5</Question>
      <Question validate="false">Score_SIM_R1_Scenario2_KPI5</Question>
      <Question validate="false">Score_SIM_R1_Scenario3_KPI5</Question>
      <Question validate="false">Score_SIM_R1_Scenario4_KPI5</Question>
    </Total>
    <Total result="Score_SIM_Total_R1_KPI6" method="sum">
      <Question validate="false">Score_SIM_Init_KPI6</Question>
      <Question validate="false">Score_SIM_R1_Scenario1_KPI6</Question>
      <Question validate="false">Score_SIM_R1_Scenario2_KPI6</Question>
      <Question validate="false">Score_SIM_R1_Scenario3_KPI6</Question>
      <Question validate="false">Score_SIM_R1_Scenario4_KPI6</Question>
    </Total>
    <Total result="Score_SIM_Total_R1_KPI9" method="sum">
      <Question validate="false">Score_SIM_Init_KPI9</Question>
      <Question validate="false">Score_SIM_R1_Scenario1_KPI9</Question>
      <Question validate="false">Score_SIM_R1_Scenario2_KPI9</Question>
      <Question validate="false">Score_SIM_R1_Scenario3_KPI9</Question>
      <Question validate="false">Score_SIM_R1_Scenario4_KPI9</Question>
    </Total>
    <Total result="Score_SIM_Total_R1_KPI10" method="sum">
      <Question validate="false">Score_SIM_Init_KPI10</Question>
      <Question validate="false">Score_SIM_R1_Scenario1_KPI10</Question>
      <Question validate="false">Score_SIM_R1_Scenario2_KPI10</Question>
      <Question validate="false">Score_SIM_R1_Scenario3_KPI10</Question>
      <Question validate="false">Score_SIM_R1_Scenario4_KPI10</Question>
    </Total>
    <Total result="Score_SIM_Total_R1_KPI11" method="sum">
      <Question validate="false">Score_SIM_Init_KPI11</Question>
      <Question validate="false">Score_SIM_R1_Scenario1_KPI11</Question>
      <Question validate="false">Score_SIM_R1_Scenario2_KPI11</Question>
      <Question validate="false">Score_SIM_R1_Scenario3_KPI11</Question>
      <Question validate="false">Score_SIM_R1_Scenario4_KPI11</Question>
    </Total>
    <Total result="Score_SIM_Total_R1_KPI12" method="sum">
      <Question validate="false">Score_SIM_Init_KPI12</Question>
      <Question validate="false">Score_SIM_R1_Scenario1_KPI12</Question>
      <Question validate="false">Score_SIM_R1_Scenario2_KPI12</Question>
      <Question validate="false">Score_SIM_R1_Scenario3_KPI12</Question>
      <Question validate="false">Score_SIM_R1_Scenario4_KPI12</Question>
    </Total>
    <Total result="Score_SIM_Total_R1_KPI13" method="sum">
      <Question validate="false">Score_SIM_Init_KPI13</Question>
      <Question validate="false">Score_SIM_R1_Scenario1_KPI13</Question>
      <Question validate="false">Score_SIM_R1_Scenario2_KPI13</Question>
      <Question validate="false">Score_SIM_R1_Scenario3_KPI13</Question>
      <Question validate="false">Score_SIM_R1_Scenario4_KPI13</Question>
    </Total>


    <!-- TOTAL R1 KPIs Impacts -->
    <!-- If KPIs are negative, it will result in 0.99 impact multiplier -->

    <Score type="Range" result="Score_SIM_Total_R1_KPI1_Impacts_Rng">
      <Question>Score_SIM_Total_R1_KPI1</Question>
      <Boundary>0</Boundary>
    </Score>
    <Total result="Score_SIM_Total_R1_KPI1_Impacts_Pre" method="sum">
      <Question validate="false">Score_SIM_KPI_Offset</Question>
      <Question validate="false">Score_SIM_Total_R1_KPI1_Impacts_Rng</Question>
    </Total>
    <Total result="Score_SIM_Total_R1_KPI1_Impacts" method="divide" numerator="Score_SIM_KPI_Factor">
      <Question validate="false">Score_SIM_Total_R1_KPI1_Impacts_Pre</Question>
    </Total>

    <Score type="Range" result="Score_SIM_Total_R1_KPI2_Impacts_Rng">
      <Question>Score_SIM_Total_R1_KPI2</Question>
      <Boundary>0</Boundary>
    </Score>
    <Total result="Score_SIM_Total_R1_KPI2_Impacts_Pre" method="sum">
      <Question validate="false">Score_SIM_KPI_Offset</Question>
      <Question validate="false">Score_SIM_Total_R1_KPI2_Impacts_Rng</Question>
    </Total>
    <Total result="Score_SIM_Total_R1_KPI2_Impacts" method="divide" numerator="Score_SIM_KPI_Factor">
      <Question validate="false">Score_SIM_Total_R1_KPI2_Impacts_Pre</Question>
    </Total>

    <Score type="Range" result="Score_SIM_Total_R1_KPI3_Impacts_Rng">
      <Question>Score_SIM_Total_R1_KPI3</Question>
      <Boundary>0</Boundary>
    </Score>
    <Total result="Score_SIM_Total_R1_KPI3_Impacts_Pre" method="sum">
      <Question validate="false">Score_SIM_KPI_Offset</Question>
      <Question validate="false">Score_SIM_Total_R1_KPI3_Impacts_Rng</Question>
    </Total>
    <Total result="Score_SIM_Total_R1_KPI3_Impacts" method="divide" numerator="Score_SIM_KPI_Factor">
      <Question validate="false">Score_SIM_Total_R1_KPI3_Impacts_Pre</Question>
    </Total>

    <Score type="Range" result="Score_SIM_Total_R1_KPI4_Impacts_Rng">
      <Question>Score_SIM_Total_R1_KPI4</Question>
      <Boundary>0</Boundary>
    </Score>
    <Total result="Score_SIM_Total_R1_KPI4_Impacts_Pre" method="sum">
      <Question validate="false">Score_SIM_KPI_Offset</Question>
      <Question validate="false">Score_SIM_Total_R1_KPI4_Impacts_Rng</Question>
    </Total>
    <Total result="Score_SIM_Total_R1_KPI4_Impacts" method="divide" numerator="Score_SIM_KPI_Factor">
      <Question validate="false">Score_SIM_Total_R1_KPI4_Impacts_Pre</Question>
    </Total>

    <Score type="Range" result="Score_SIM_Total_R1_KPI5_Impacts_Rng">
      <Question>Score_SIM_Total_R1_KPI5</Question>
      <Boundary>0</Boundary>
    </Score>
    <Total result="Score_SIM_Total_R1_KPI5_Impacts_Pre" method="sum">
      <Question validate="false">Score_SIM_KPI_Offset</Question>
      <Question validate="false">Score_SIM_Total_R1_KPI5_Impacts_Rng</Question>
    </Total>
    <Total result="Score_SIM_Total_R1_KPI5_Impacts" method="divide" numerator="Score_SIM_KPI_Factor">
      <Question validate="false">Score_SIM_Total_R1_KPI5_Impacts_Pre</Question>
    </Total>

    <Score type="Range" result="Score_SIM_Total_R1_KPI6_Impacts_Rng">
      <Question>Score_SIM_Total_R1_KPI6</Question>
      <Boundary>0</Boundary>
    </Score>
    <Total result="Score_SIM_Total_R1_KPI6_Impacts_Pre" method="sum">
      <Question validate="false">Score_SIM_KPI_Offset</Question>
      <Question validate="false">Score_SIM_Total_R1_KPI6_Impacts_Rng</Question>
    </Total>
    <Total result="Score_SIM_Total_R1_KPI6_Impacts" method="divide" numerator="Score_SIM_KPI_Factor">
      <Question validate="false">Score_SIM_Total_R1_KPI6_Impacts_Pre</Question>
    </Total>
    

    <!-- TOTAL R1 Calculated KPIs -->

    <Total result="Score_SIM_Total_R1_KPI7" method="sum">
      <Question validate="false">Score_SIM_Total_R1_KPI1</Question>
      <Question validate="false">Score_SIM_Total_R1_KPI2</Question>
      <Question validate="false">Score_SIM_Total_R1_KPI3</Question>
      <Question validate="false">Score_SIM_Total_R1_KPI4</Question>
      <Question validate="false">Score_SIM_Total_R1_KPI5</Question>
      <Question validate="false">Score_SIM_Total_R1_KPI6</Question>
    </Total>

    <Total result="Score_SIM_Total_R1_KPI8_Pre" method="sum">
      <Question validate="false">Score_SIM_Init_KPI8</Question>
      <Question validate="false">Score_SIM_R1_Scenario1_KPI8</Question>
      <Question validate="false">Score_SIM_R1_Scenario2_KPI8</Question>
      <Question validate="false">Score_SIM_R1_Scenario3_KPI8</Question>
      <Question validate="false">Score_SIM_R1_Scenario4_KPI8</Question>
    </Total>
    <Total result="Score_SIM_Total_R1_KPI8" method="multiply">
      <Question validate="false">Score_SIM_Total_R1_KPI8_Pre</Question>
      <Question validate="false">Score_SIM_Total_R1_KPI6_Impacts</Question>
    </Total>
    
    <Total result="Score_SIM_Total_R1_KPI14_Pre1" method="multiply">
      <Question validate="false">Score_SIM_Total_R1_KPI9</Question>
      <Question validate="false">Score_SIM_Total_R1_KPI1_Impacts</Question>
    </Total>
    <Total result="Score_SIM_Total_R1_KPI14_Pre2" method="multiply">
      <Question validate="false">Score_SIM_Total_R1_KPI10</Question>
      <Question validate="false">Score_SIM_Total_R1_KPI2_Impacts</Question>
    </Total>
    <Total result="Score_SIM_Total_R1_KPI14_Pre3" method="multiply">
      <Question validate="false">Score_SIM_Total_R1_KPI11</Question>
      <Question validate="false">Score_SIM_Total_R1_KPI3_Impacts</Question>
    </Total>
    <Total result="Score_SIM_Total_R1_KPI14_Pre4" method="multiply">
      <Question validate="false">Score_SIM_Total_R1_KPI12</Question>
      <Question validate="false">Score_SIM_Total_R1_KPI4_Impacts</Question>
    </Total>
    <Total result="Score_SIM_Total_R1_KPI14_Pre5" method="multiply">
      <Question validate="false">Score_SIM_Total_R1_KPI13</Question>
      <Question validate="false">Score_SIM_Total_R1_KPI5_Impacts</Question>
    </Total>
    <Total result="Score_SIM_Total_R1_KPI14" method="avg">
      <Question validate="false">Score_SIM_Total_R1_KPI14_Pre1</Question>
      <Question validate="false">Score_SIM_Total_R1_KPI14_Pre2</Question>
      <Question validate="false">Score_SIM_Total_R1_KPI14_Pre3</Question>
      <Question validate="false">Score_SIM_Total_R1_KPI14_Pre4</Question>
      <Question validate="false">Score_SIM_Total_R1_KPI14_Pre5</Question>
    </Total>

    <Total result="Score_SIM_Total_R1_KPI14_Impacts" method="divide" numerator="Score_SIM_KPI_Factor">
      <Question validate="false">Score_SIM_Total_R1_KPI14</Question>
    </Total>
    <Total result="Score_SIM_Total_R1" method="multiply">
      <Question validate="false">Score_SIM_Total_R1_KPI8</Question>
      <Question validate="false">Score_SIM_Total_R1_KPI14_Impacts</Question>
    </Total>
    <Total result="Score_SIM_Total_R1_Rank" method="samerank">
      <Question validate="false">Score_SIM_Total_R1</Question>
    </Total>
    

    <!-- TOTAL R2 Base KPIs -->
    
    <Total result="Score_SIM_Total_R2_KPI1" method="sum">
      <Question validate="false">Score_SIM_Init_KPI1</Question>
      <Question validate="false">Score_SIM_R2_Scenario1_KPI1</Question>
      <Question validate="false">Score_SIM_R2_Scenario2_KPI1</Question>
      <Question validate="false">Score_SIM_R2_Scenario3_KPI1</Question>
    </Total>
    <Total result="Score_SIM_Total_R2_KPI2" method="sum">
      <Question validate="false">Score_SIM_Init_KPI2</Question>
      <Question validate="false">Score_SIM_R2_Scenario1_KPI2</Question>
      <Question validate="false">Score_SIM_R2_Scenario2_KPI2</Question>
      <Question validate="false">Score_SIM_R2_Scenario3_KPI2</Question>
    </Total>
    <Total result="Score_SIM_Total_R2_KPI3" method="sum">
      <Question validate="false">Score_SIM_Init_KPI3</Question>
      <Question validate="false">Score_SIM_R2_Scenario1_KPI3</Question>
      <Question validate="false">Score_SIM_R2_Scenario2_KPI3</Question>
      <Question validate="false">Score_SIM_R2_Scenario3_KPI3</Question>
    </Total>
    <Total result="Score_SIM_Total_R2_KPI4" method="sum">
      <Question validate="false">Score_SIM_Init_KPI4</Question>
      <Question validate="false">Score_SIM_R2_Scenario1_KPI4</Question>
      <Question validate="false">Score_SIM_R2_Scenario2_KPI4</Question>
      <Question validate="false">Score_SIM_R2_Scenario3_KPI4</Question>
    </Total>
    <Total result="Score_SIM_Total_R2_KPI5" method="sum">
      <Question validate="false">Score_SIM_Init_KPI5</Question>
      <Question validate="false">Score_SIM_R2_Scenario1_KPI5</Question>
      <Question validate="false">Score_SIM_R2_Scenario2_KPI5</Question>
      <Question validate="false">Score_SIM_R2_Scenario3_KPI5</Question>
    </Total>
    <Total result="Score_SIM_Total_R2_KPI6" method="sum">
      <Question validate="false">Score_SIM_Init_KPI6</Question>
      <Question validate="false">Score_SIM_R2_Scenario1_KPI6</Question>
      <Question validate="false">Score_SIM_R2_Scenario2_KPI6</Question>
      <Question validate="false">Score_SIM_R2_Scenario3_KPI6</Question>
    </Total>
    <Total result="Score_SIM_Total_R2_KPI15" method="sum">
      <Question validate="false">Score_SIM_Init_KPI15</Question>
      <Question validate="false">Score_SIM_R2_Scenario1_KPI15</Question>
      <Question validate="false">Score_SIM_R2_Scenario2_KPI15</Question>
      <Question validate="false">Score_SIM_R2_Scenario3_KPI15</Question>
    </Total>
    <Total result="Score_SIM_Total_R2_KPI16" method="sum">
      <Question validate="false">Score_SIM_Init_KPI16</Question>
      <Question validate="false">Score_SIM_R2_Scenario1_KPI16</Question>
      <Question validate="false">Score_SIM_R2_Scenario2_KPI16</Question>
      <Question validate="false">Score_SIM_R2_Scenario3_KPI16</Question>
    </Total>
    <Total result="Score_SIM_Total_R2_KPI17" method="sum">
      <Question validate="false">Score_SIM_Init_KPI17</Question>
      <Question validate="false">Score_SIM_R2_Scenario1_KPI17</Question>
      <Question validate="false">Score_SIM_R2_Scenario2_KPI17</Question>
      <Question validate="false">Score_SIM_R2_Scenario3_KPI17</Question>
    </Total>


    <!-- TOTAL R2 KPIs Impacts -->
    <!-- If KPIs are negative, it will result in 0.95 ~ 1.00 impact multiplier -->

    <Score type="Range" result="Score_SIM_Total_R2_KPI1_Impacts_Rng">
      <Question>Score_SIM_Total_R2_KPI1</Question>
      <Boundary>0</Boundary>
    </Score>
    <Score type="Range" result="Score_SIM_Total_R2_KPI2_Impacts_Rng">
      <Question>Score_SIM_Total_R2_KPI2</Question>
      <Boundary>0</Boundary>
    </Score>
    <Score type="Range" result="Score_SIM_Total_R2_KPI3_Impacts_Rng">
      <Question>Score_SIM_Total_R2_KPI3</Question>
      <Boundary>0</Boundary>
    </Score>
    <Score type="Range" result="Score_SIM_Total_R2_KPI4_Impacts_Rng">
      <Question>Score_SIM_Total_R2_KPI4</Question>
      <Boundary>0</Boundary>
    </Score>
    <Score type="Range" result="Score_SIM_Total_R2_KPI5_Impacts_Rng">
      <Question>Score_SIM_Total_R2_KPI5</Question>
      <Boundary>0</Boundary>
    </Score>
    <Total result="Score_SIM_Total_R2_KPIs_Impacts_Multi_Pre" method="sum">
      <Question validate="false">Score_SIM_KPI_Offset_Multi</Question>
      <Question validate="false">Score_SIM_Total_R2_KPI1_Impacts_Rng</Question>
      <Question validate="false">Score_SIM_Total_R2_KPI2_Impacts_Rng</Question>
      <Question validate="false">Score_SIM_Total_R2_KPI3_Impacts_Rng</Question>
      <Question validate="false">Score_SIM_Total_R2_KPI4_Impacts_Rng</Question>
      <Question validate="false">Score_SIM_Total_R2_KPI5_Impacts_Rng</Question>
    </Total>
    <Total result="Score_SIM_Total_R2_KPIs_Impacts_Multi" method="divide" numerator="Score_SIM_KPI_Factor">
      <Question validate="false">Score_SIM_Total_R2_KPIs_Impacts_Multi_Pre</Question>
    </Total>

    <Score type="Range" result="Score_SIM_Total_R2_KPI6_Impacts_Rng">
      <Question>Score_SIM_Total_R2_KPI6</Question>
      <Boundary>0</Boundary>
    </Score>
    <Total result="Score_SIM_Total_R2_KPI6_Impacts_Pre" method="sum">
      <Question validate="false">Score_SIM_KPI_Offset</Question>
      <Question validate="false">Score_SIM_Total_R2_KPI6_Impacts_Rng</Question>
    </Total>
    <Total result="Score_SIM_Total_R2_KPI6_Impacts" method="divide" numerator="Score_SIM_KPI_Factor">
      <Question validate="false">Score_SIM_Total_R2_KPI6_Impacts_Pre</Question>
    </Total>
    

    <!-- TOTAL R2 Calculated KPIs -->

    <Total result="Score_SIM_Total_R2_KPI7" method="sum">
      <Question validate="false">Score_SIM_Total_R2_KPI1</Question>
      <Question validate="false">Score_SIM_Total_R2_KPI2</Question>
      <Question validate="false">Score_SIM_Total_R2_KPI3</Question>
      <Question validate="false">Score_SIM_Total_R2_KPI4</Question>
      <Question validate="false">Score_SIM_Total_R2_KPI5</Question>
      <Question validate="false">Score_SIM_Total_R2_KPI6</Question>
    </Total>

    <Total result="Score_SIM_Total_R2_KPI8_Pre" method="sum">
      <Question validate="false">Score_SIM_Total_R1_KPI8</Question>
      <Question validate="false">Score_SIM_R2_Scenario1_KPI8</Question>
      <Question validate="false">Score_SIM_R2_Scenario2_KPI8</Question>
      <Question validate="false">Score_SIM_R2_Scenario3_KPI8</Question>
    </Total>
    <Total result="Score_SIM_Total_R2_KPI8" method="multiply">
      <Question validate="false">Score_SIM_Total_R2_KPI8_Pre</Question>
      <Question validate="false">Score_SIM_Total_R2_KPI6_Impacts</Question>
    </Total>
    
    <Total result="Score_SIM_Total_R2_KPI18_Pre" method="avg">
      <Question validate="false">Score_SIM_Total_R2_KPI15</Question>
      <Question validate="false">Score_SIM_Total_R2_KPI16</Question>
      <Question validate="false">Score_SIM_Total_R2_KPI17</Question>
    </Total>
    <Total result="Score_SIM_Total_R2_KPI18" method="multiply">
      <Question validate="false">Score_SIM_Total_R2_KPI18_Pre</Question>
      <Question validate="false">Score_SIM_Total_R2_KPIs_Impacts_Multi</Question>
    </Total>

    <Total result="Score_SIM_Total_R2_KPI18_Impacts" method="divide" numerator="Score_SIM_KPI_Factor">
      <Question validate="false">Score_SIM_Total_R2_KPI18</Question>
    </Total>
    <Total result="Score_SIM_Total_R2" method="multiply">
      <Question validate="false">Score_SIM_Total_R2_KPI8</Question>
      <Question validate="false">Score_SIM_Total_R2_KPI18_Impacts</Question>
    </Total>
    <Total result="Score_SIM_Total_R2_Rank" method="samerank">
      <Question validate="false">Score_SIM_Total_R2</Question>
    </Total>
    
    
    <!-- RANK -->

    <Total result="Score_SIM_Total" method="avg">
      <Question validate="false">Score_SIM_Total_R1</Question>
      <Question validate="false">Score_SIM_Total_R2</Question>
    </Total>
    <Total result="Score_SIM_Total_Rank" method="samerank">
      <Question validate="false">Score_SIM_Total</Question>
    </Total>

  </Aggregator>

</Action>