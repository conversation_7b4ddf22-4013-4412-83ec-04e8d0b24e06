.wizlet.wizletRollingDice .card .card-content .row{margin:0}.wizlet.wizletRollingDice .card .card-content.borders{border-width:10px;border-color:#00b2a9;border-radius:10px;float:none !important}.wizlet.wizletRollingDice .card .card-content.borders.top{border-top-style:outset}.wizlet.wizletRollingDice .card .card-content.borders.right{border-right-style:outset}.wizlet.wizletRollingDice .card .card-content.borders.left{border-left-style:outset}.wizlet.wizletRollingDice .card .card-content.borders.bottom{border-bottom-style:outset}.wizlet.wizletRollingDice #dice{-webkit-box-align:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center;display:grid;grid-gap:2rem;grid-template-columns:repeat(auto-fit, minmax(8rem, 1fr));grid-template-rows:auto;justify-items:center;padding:2rem 0 6rem 0;-webkit-perspective:600px;perspective:600px}@media(min-width: 900px){.wizlet.wizletRollingDice #dice{-webkit-perspective:1300px;perspective:1300px}}.wizlet.wizletRollingDice #dice .die-list{display:grid;grid-template-columns:1fr;grid-template-rows:1fr;height:6rem;list-style-type:none;-webkit-transform-style:preserve-3d;transform-style:preserve-3d;width:6rem;margin-left:-2rem}.wizlet.wizletRollingDice #dice .even-roll{-webkit-transition:-webkit-transform 1.5s ease-out;transition:-webkit-transform 1.5s ease-out;-o-transition:-o-transform 1.5s ease-out;transition:transform 1.5s ease-out;transition:transform 1.5s ease-out, -webkit-transform 1.5s ease-out, -o-transform 1.5s ease-out}.wizlet.wizletRollingDice #dice .odd-roll{-webkit-transition:-webkit-transform 1.25s ease-out;transition:-webkit-transform 1.25s ease-out;-o-transition:-o-transform 1.25s ease-out;transition:transform 1.25s ease-out;transition:transform 1.25s ease-out, -webkit-transform 1.25s ease-out, -o-transform 1.25s ease-out}.wizlet.wizletRollingDice #dice .die-item{background-color:#fefefe;-webkit-box-shadow:inset -0.35rem .35rem .75rem rgba(0,0,0,.3),inset .5rem -0.25rem .5rem rgba(0,0,0,.15);box-shadow:inset -0.35rem .35rem .75rem rgba(0,0,0,.3),inset .5rem -0.25rem .5rem rgba(0,0,0,.15);display:grid;grid-column:1;grid-row:1;grid-template-areas:"one two three" "four five six" "seven eight nine";grid-template-columns:repeat(3, 1fr);grid-template-rows:repeat(3, 1fr);height:100%;padding:1rem;width:100%}.wizlet.wizletRollingDice #dice .dot{-webkit-align-self:center;-ms-flex-item-align:center;align-self:center;background-color:#676767;border-radius:50%;-webkit-box-shadow:inset -0.15rem .15rem .25rem rgba(0,0,0,.5);box-shadow:inset -0.15rem .15rem .25rem rgba(0,0,0,.5);display:block;height:1.25rem;justify-self:center;width:1.25rem}.wizlet.wizletRollingDice #dice .even-roll[data-roll="1"]{-webkit-transform:rotateX(360deg) rotateY(720deg) rotateZ(360deg);transform:rotateX(360deg) rotateY(720deg) rotateZ(360deg)}.wizlet.wizletRollingDice #dice .even-roll[data-roll="2"]{-webkit-transform:rotateX(450deg) rotateY(720deg) rotateZ(360deg);transform:rotateX(450deg) rotateY(720deg) rotateZ(360deg)}.wizlet.wizletRollingDice #dice .even-roll[data-roll="3"]{-webkit-transform:rotateX(360deg) rotateY(630deg) rotateZ(360deg);transform:rotateX(360deg) rotateY(630deg) rotateZ(360deg)}.wizlet.wizletRollingDice #dice .even-roll[data-roll="4"]{-webkit-transform:rotateX(360deg) rotateY(810deg) rotateZ(360deg);transform:rotateX(360deg) rotateY(810deg) rotateZ(360deg)}.wizlet.wizletRollingDice #dice .even-roll[data-roll="5"]{-webkit-transform:rotateX(270deg) rotateY(720deg) rotateZ(360deg);transform:rotateX(270deg) rotateY(720deg) rotateZ(360deg)}.wizlet.wizletRollingDice #dice .even-roll[data-roll="6"]{-webkit-transform:rotateX(360deg) rotateY(900deg) rotateZ(360deg);transform:rotateX(360deg) rotateY(900deg) rotateZ(360deg)}.wizlet.wizletRollingDice #dice .odd-roll[data-roll="1"]{-webkit-transform:rotateX(-360deg) rotateY(-720deg) rotateZ(-360deg);transform:rotateX(-360deg) rotateY(-720deg) rotateZ(-360deg)}.wizlet.wizletRollingDice #dice .odd-roll[data-roll="2"]{-webkit-transform:rotateX(-270deg) rotateY(-720deg) rotateZ(-360deg);transform:rotateX(-270deg) rotateY(-720deg) rotateZ(-360deg)}.wizlet.wizletRollingDice #dice .odd-roll[data-roll="3"]{-webkit-transform:rotateX(-360deg) rotateY(-810deg) rotateZ(-360deg);transform:rotateX(-360deg) rotateY(-810deg) rotateZ(-360deg)}.wizlet.wizletRollingDice #dice .odd-roll[data-roll="4"]{-webkit-transform:rotateX(-360deg) rotateY(-630deg) rotateZ(-360deg);transform:rotateX(-360deg) rotateY(-630deg) rotateZ(-360deg)}.wizlet.wizletRollingDice #dice .odd-roll[data-roll="5"]{-webkit-transform:rotateX(-450deg) rotateY(-720deg) rotateZ(-360deg);transform:rotateX(-450deg) rotateY(-720deg) rotateZ(-360deg)}.wizlet.wizletRollingDice #dice .odd-roll[data-roll="6"]{-webkit-transform:rotateX(-360deg) rotateY(-900deg) rotateZ(-360deg);transform:rotateX(-360deg) rotateY(-900deg) rotateZ(-360deg)}.wizlet.wizletRollingDice #dice [data-side="1"]{-webkit-transform:rotate3d(0, 0, 0, 90deg) translateZ(4rem);transform:rotate3d(0, 0, 0, 90deg) translateZ(4rem)}.wizlet.wizletRollingDice #dice [data-side="2"]{-webkit-transform:rotate3d(-1, 0, 0, 90deg) translateZ(4rem);transform:rotate3d(-1, 0, 0, 90deg) translateZ(4rem)}.wizlet.wizletRollingDice #dice [data-side="3"]{-webkit-transform:rotate3d(0, 1, 0, 90deg) translateZ(4rem);transform:rotate3d(0, 1, 0, 90deg) translateZ(4rem)}.wizlet.wizletRollingDice #dice [data-side="4"]{-webkit-transform:rotate3d(0, -1, 0, 90deg) translateZ(4rem);transform:rotate3d(0, -1, 0, 90deg) translateZ(4rem)}.wizlet.wizletRollingDice #dice [data-side="5"]{-webkit-transform:rotate3d(1, 0, 0, 90deg) translateZ(4rem);transform:rotate3d(1, 0, 0, 90deg) translateZ(4rem)}.wizlet.wizletRollingDice #dice [data-side="6"]{-webkit-transform:rotate3d(1, 0, 0, 180deg) translateZ(4rem);transform:rotate3d(1, 0, 0, 180deg) translateZ(4rem)}.wizlet.wizletRollingDice #dice [data-side="1"] .dot:nth-of-type(1){grid-area:five}.wizlet.wizletRollingDice #dice [data-side="2"] .dot:nth-of-type(1){grid-area:one}.wizlet.wizletRollingDice #dice [data-side="2"] .dot:nth-of-type(2){grid-area:nine}.wizlet.wizletRollingDice #dice [data-side="3"] .dot:nth-of-type(1){grid-area:one}.wizlet.wizletRollingDice #dice [data-side="3"] .dot:nth-of-type(2){grid-area:five}.wizlet.wizletRollingDice #dice [data-side="3"] .dot:nth-of-type(3){grid-area:nine}.wizlet.wizletRollingDice #dice [data-side="4"] .dot:nth-of-type(1){grid-area:one}.wizlet.wizletRollingDice #dice [data-side="4"] .dot:nth-of-type(2){grid-area:three}.wizlet.wizletRollingDice #dice [data-side="4"] .dot:nth-of-type(3){grid-area:seven}.wizlet.wizletRollingDice #dice [data-side="4"] .dot:nth-of-type(4){grid-area:nine}.wizlet.wizletRollingDice #dice [data-side="5"] .dot:nth-of-type(1){grid-area:one}.wizlet.wizletRollingDice #dice [data-side="5"] .dot:nth-of-type(2){grid-area:three}.wizlet.wizletRollingDice #dice [data-side="5"] .dot:nth-of-type(3){grid-area:five}.wizlet.wizletRollingDice #dice [data-side="5"] .dot:nth-of-type(4){grid-area:seven}.wizlet.wizletRollingDice #dice [data-side="5"] .dot:nth-of-type(5){grid-area:nine}.wizlet.wizletRollingDice #dice [data-side="6"] .dot:nth-of-type(1){grid-area:one}.wizlet.wizletRollingDice #dice [data-side="6"] .dot:nth-of-type(2){grid-area:three}.wizlet.wizletRollingDice #dice [data-side="6"] .dot:nth-of-type(3){grid-area:four}.wizlet.wizletRollingDice #dice [data-side="6"] .dot:nth-of-type(4){grid-area:six}.wizlet.wizletRollingDice #dice [data-side="6"] .dot:nth-of-type(5){grid-area:seven}.wizlet.wizletRollingDice #dice [data-side="6"] .dot:nth-of-type(6){grid-area:nine}.wizlet.wizletRollingDice #scoreBox{width:80%;margin:auto;padding:12px}