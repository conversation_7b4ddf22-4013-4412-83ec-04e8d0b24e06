<?xml version="1.0" encoding="utf-8" ?>

<Action mainAreaLayout="../../../layout/mainLayout_SIM">
  <Include name="Header_SIM"></Include>
  <Include name="KPIgauges"></Include>

  
    
  <Component type="Vanilla">
    <![CDATA[{
      templateInEvent: "html/card.dot",
      css: "styles/card.css",
      orientation: "vertical",
      header: "!{}",
      valign: false,
      animate: "fadeInUp",
      content: {
        _img_embed: { 
          materialboxed: false, borders: false, frame: "", nopadding: false,
          src: "!{SIM_R1_Finish_Img}",  alt: "!{SIM_R1_Finish_Title}" ,
          position: "right large", style: ""
        },
        img: { 
          materialboxed: false, borders: false, frame: "", nopadding: false,
          src: "!{SIM_R1_Finish_Img}",  alt: "!{SIM_R1_Finish_Title}" ,
          src_vert: "!{}"
        },
        position: "up",
        title: "!{SIM_R1_Finish_Title}",
        body: "!{SIM_R1_Finish_Text_Part1}"
      }      
    }]]>
  </Component>




</Action>