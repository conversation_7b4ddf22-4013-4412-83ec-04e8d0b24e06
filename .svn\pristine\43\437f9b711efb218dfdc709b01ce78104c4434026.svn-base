﻿
<div class="container table{{?it.animate}} animated {{=it.animate}}{{?}}{{?it.isHiddenWhenSmall}} hide-on-med-and-down{{?}}{{?it.isHiddenWhenBig}} hide-on-large-only{{?}}"> 
    <div>
        
        {{?it.header}}<h4 class="header">{{=it.header}}</h4>{{?}}
        {{?it.subheader}}<h5 class="header subtitle">{{=it.subheader}}</h5>{{?}}
        {{?it.instructions}}<h6 class="instructions flow-text">{{=it.instructions}}</h6>{{?}}
        
        <div class="card">
            
            <div class="card-content {{?it.dataTablesHideControls}}hideControls{{?}} ">
                {{?it.table.title}}<span class="card-title">{{=it.table.title}}</span>{{?}}
                {{?it.table.body}}<p class="flow-text">{{=it.table.body}}<br><br></p>{{?}}
   
                {{?it.imageUp}}
                <div class="card-image row">
                    <img class="responsive-img col {{?it.imageUp.grid}}{{=it.imageUp.grid}}{{??}}s12{{?}}" 
                        src="/Wizer/Pages/Events/{{=wizerApi.eventName()}}{{=it.imageUp.src}}" alt="{{=it.imageUp.alt}}"/>
                            
                </div>
                {{?}}

                <table class="striped highlight {{?it.class}}{{=it.class}}{{?}}">
                    
                    {{?it.table.colgroup}}
                    <colgroup>
                        {{~it.table.colgroup :col}}
                            <col span="{{=col.span}}" style="width: {{=col.width}}">
                        {{~}}          
                     </colgroup>
                     {{?}}


                    <thead {{?it.table.headerColor}}class="headerColor {{=it.table.headerColor}}"{{?}}>
                        <tr>
                            {{~it.table.headers :header}}
                            <th>{{=header}}</th>
                            {{~}}               
                        </tr>
                    </thead>
            
                    <tbody>
                        {{~it.table.rows :row:r}}
                        <tr>                            
                            {{~row :cell:c}}
                                <td {{?cell.class}}class="{{=cell.class}}"{{?}} {{?cell.colspan}}colspan="{{=cell.colspan}}"{{?}}>

                                {{?cell.type == "text"}}
                                    <span class="text">{{=cell.text}}</span>
                                {{?}}
                                {{?cell.type == "textImage"}}
                                    <div class="textImage">
                                        <img src="/Wizer/Pages/Events/{{=wizerApi.eventName()}}{{=cell.img}}"/>
                                        <span class="text">{{=cell.text}}</span>
                                    </div>
                                {{?}}
                                {{?cell.type == "tloutput"}}
                                    <span class="output" data-binding="{{=cell.tloutput}}"> </span>
                                {{?}}
                                {{?cell.type == "tlinput-text"}}
                                    <input type="text" data-binding="{{=cell.tlinput}}" {{?cell.maxlength}}maxlength="{{=cell.maxlength}}"{{?}}/>
                                {{?}}
                                {{?cell.type == "tlinput-number"}}
                                    <div class="input-cell">
                                        <div class="input-field">
                                            <i class="material-icons _prefix">edit</i>
                                            <input type="number" data-binding="{{=cell.tlinput}}" {{?cell.max}}max="{{=cell.max}}"{{?}} {{?cell.min}}min="{{=cell.min}}"{{?}} {{?cell.step}}step="{{=cell.step}}"{{?}}/>
                                        </div>
                                        {{?cell.suffix}}<span class="suffix">{{=cell.suffix}}</span>{{?}}
                                    </div>
                                {{?}}

                                </td>
                            {{~}}
                        </tr>
                        {{~}}
                    </tbody>
                    {{?it.table.note}}<caption>{{=it.table.note}}</caption>{{?}}
                </table>
   
                {{?it.imageDown}}
                <div class="card-image col {{?it.imageDown.grid}}{{=it.imageDown.grid}}{{??}}s12{{?}}">
                    <img class="responsive-img" 
                        src="/Wizer/Pages/Events/{{=wizerApi.eventName()}}{{=it.imageDown.src}}" alt="{{=it.imageDown.alt}}"/>
                            
                </div>
                {{?}}

                {{?it.table.body2}}<p class="flow-text">{{=it.table.body2}}<br><br></p>{{?}}

            </div>

        </div>  
        
        
    </div>
</div>
    