
<div class="hiddenPanel {{?it.animateButton}}animate__animated animate__{{=it.animateButton}}{{?}}"
    {{?it.id}}id="{{=it.id}}"{{?}} 
    {{?it.isHidden}}
        {{?it.isFollower}} {{?it.DB[it.isFollower]!=2}}hidden{{?}} {{??}}hidden{{?}}
    {{?}}
    {{?it.isFollower}} {{?it.DB[it.isFollower]==1}}disabled hidden{{?}}{{?}}>

    <div class="panelButton">
        <a id="panelButton" class="btn-floating btn-large {{?it.pulse}}pulse{{?}} client-colors {{?it.color}}{{=it.color}}{{??}}button{{?}} client-colors-text text-font2" >
            <i class="medium material-icons right">{{=it.icon_open}}</i>
        </a>
    </div>

    <div id="panel" class="hiddenPanel animate__animated">

        <div class="page-loader"></div>

        <div class="hideButton">
            <a id="hideButton" class="btn-floating btn-large client-colors {{?it.color}}{{=it.color}}{{??}}button{{?}} client-colors-text text-font2" >
                <i class="medium material-icons right">{{=it.icon_close}}</i>
            </a>
        </div>

    </div>
</div>

