﻿Customer:;BTS;BTS,,,,,,,,,,,
Event:;Mubadala_MBS_2025;;;;;Event;;Mubadala Facilitated MBS 2025;

Questions:;

GD;Individual,Protected;GD;
GD_Foreman;Individual,Protected;GD_Foreman;
GD_Fac;Individual,Protected;GD_Fac;
GD_Fac_Foreman;Individual,Protected;GD_Fac_Foreman;
Follower;Individual,Protected;Follower;
Team;Individual,Protected;Team;
Team_Foreman;Individual,Protected;Team_Foreman;
Filter_Agg;Individual,Protected;Filter_Agg;

Q_LOGIN_DATE_R1;Individual;Q_LOGIN_DATE_R1;
Q_LOGIN_DATE_R2;Individual;Q_LOGIN_DATE_R2;
Q_LOGIN_DATE_R3;Individual;Q_LOGIN_DATE_R3;
Q_LOGIN_DATE_R4;Individual;Q_LOGIN_DATE_R4;
Q_FINISH_DATE_R1;Individual;Q_FINISH_DATE_R1;
Q_FINISH_DATE_R2;Individual;Q_FINISH_DATE_R2;
Q_FINISH_DATE_R3;Individual;Q_FINISH_DATE_R3;
Q_FINISH_DATE_R4;Individual;Q_FINISH_DATE_R4;

Q_My_Name;Individual;Q_My_Name;
Q_My_Avatar;Individual;Q_My_Avatar;

Q_FAC_Navigation_Tab;Individual;Q_FAC_Navigation_Tab;
Q_FAC_Navigation_Screen;Individual;Q_FAC_Navigation_Screen;

Score_SIM_MTUs_Offset;Individual;Score_SIM_MTUs_Offset;
Score_SIM_MTUs_Multiplier;Individual;Score_SIM_MTUs_Multiplier;
Score_SIM_Roleplay_Multiplier;Individual;Score_SIM_Roleplay_Multiplier;

Score_SIM_Init_KPI1;Individual;Score_SIM_Init_KPI1;
Score_SIM_Init_KPI2;Individual;Score_SIM_Init_KPI2;
Score_SIM_Init_KPI3;Individual;Score_SIM_Init_KPI3;
Score_SIM_Init_KPI4;Individual;Score_SIM_Init_KPI4;
Score_SIM_Init_MTUs_1;Individual;Score_SIM_Init_MTUs_1;
Score_SIM_Init_MTUs_2;Individual;Score_SIM_Init_MTUs_2;
Score_SIM_Init_MTUs_3;Individual;Score_SIM_Init_MTUs_3;
Score_SIM_Init_MTUs_4;Individual;Score_SIM_Init_MTUs_4;
Score_SIM_Init_MTUs_5;Individual;Score_SIM_Init_MTUs_5;
Score_SIM_Init_MTUs_6;Individual;Score_SIM_Init_MTUs_6;
Score_SIM_Init_MTUs_7;Individual;Score_SIM_Init_MTUs_7;
Score_SIM_Init_MTUs_8;Individual;Score_SIM_Init_MTUs_8;

Score_SIM_Total_KPI1;Individual;Score_SIM_Total_KPI1;
Score_SIM_Total_KPI2;Individual;Score_SIM_Total_KPI2;
Score_SIM_Total_KPI3;Individual;Score_SIM_Total_KPI3;
Score_SIM_Total_KPI4;Individual;Score_SIM_Total_KPI4;
Score_SIM_Total_MTUs_1;Individual;Score_SIM_Total_MTUs_1;
Score_SIM_Total_MTUs_2;Individual;Score_SIM_Total_MTUs_2;
Score_SIM_Total_MTUs_3;Individual;Score_SIM_Total_MTUs_3;
Score_SIM_Total_MTUs_4;Individual;Score_SIM_Total_MTUs_4;
Score_SIM_Total_MTUs_5;Individual;Score_SIM_Total_MTUs_5;
Score_SIM_Total_MTUs_6;Individual;Score_SIM_Total_MTUs_6;
Score_SIM_Total_MTUs_7;Individual;Score_SIM_Total_MTUs_7;
Score_SIM_Total_MTUs_8;Individual;Score_SIM_Total_MTUs_8;

Score_SIM_Total;Individual;Score_SIM_Total;

Q_SIM_R1_Scenario1;Individual;Q_SIM_R1_Scenario1;1:!{Choice_Opt1};2:!{Choice_Opt2};3:!{Choice_Opt3};4:!{Choice_Opt4};

Score_SIM_R1_Scenario1_KPI1;Individual;Score_SIM_R1_Scenario1_KPI1;
Score_SIM_R1_Scenario1_KPI2;Individual;Score_SIM_R1_Scenario1_KPI2;
Score_SIM_R1_Scenario1_KPI3;Individual;Score_SIM_R1_Scenario1_KPI3;
Score_SIM_R1_Scenario1_KPI4;Individual;Score_SIM_R1_Scenario1_KPI4;
Score_SIM_R1_Scenario1_MTUs_1;Individual;Score_SIM_R1_Scenario1_MTUs_1;
Score_SIM_R1_Scenario1_MTUs_2;Individual;Score_SIM_R1_Scenario1_MTUs_2;
Score_SIM_R1_Scenario1_MTUs_3;Individual;Score_SIM_R1_Scenario1_MTUs_3;
Score_SIM_R1_Scenario1_MTUs_4;Individual;Score_SIM_R1_Scenario1_MTUs_4;
Score_SIM_R1_Scenario1_MTUs_5;Individual;Score_SIM_R1_Scenario1_MTUs_5;
Score_SIM_R1_Scenario1_MTUs_6;Individual;Score_SIM_R1_Scenario1_MTUs_6;
Score_SIM_R1_Scenario1_MTUs_7;Individual;Score_SIM_R1_Scenario1_MTUs_7;
Score_SIM_R1_Scenario1_MTUs_8;Individual;Score_SIM_R1_Scenario1_MTUs_8;

Score_SIM_R1_Roleplay;Individual;Score_SIM_R1_Roleplay;
Score_SIM_R1_Roleplay_Impact;Individual;Score_SIM_R1_Roleplay_Impact;

Score_SIM_Total_R1_KPI1;Individual;Score_SIM_Total_R1_KPI1;
Score_SIM_Total_R1_KPI2;Individual;Score_SIM_Total_R1_KPI2;
Score_SIM_Total_R1_KPI3;Individual;Score_SIM_Total_R1_KPI3;
Score_SIM_Total_R1_KPI4;Individual;Score_SIM_Total_R1_KPI4;
Score_SIM_Total_R1_MTUs_1;Individual;Score_SIM_Total_R1_MTUs_1;
Score_SIM_Total_R1_MTUs_1_Rng;Individual;Score_SIM_Total_R1_MTUs_1_Rng;
Score_SIM_Total_R1_MTUs_2;Individual;Score_SIM_Total_R1_MTUs_2;
Score_SIM_Total_R1_MTUs_2_Rng;Individual;Score_SIM_Total_R1_MTUs_2_Rng;
Score_SIM_Total_R1_MTUs_3;Individual;Score_SIM_Total_R1_MTUs_3;
Score_SIM_Total_R1_MTUs_3_Rng;Individual;Score_SIM_Total_R1_MTUs_3_Rng;
Score_SIM_Total_R1_MTUs_4;Individual;Score_SIM_Total_R1_MTUs_4;
Score_SIM_Total_R1_MTUs_4_Rng;Individual;Score_SIM_Total_R1_MTUs_4_Rng;
Score_SIM_Total_R1_MTUs_5;Individual;Score_SIM_Total_R1_MTUs_5;
Score_SIM_Total_R1_MTUs_5_Rng;Individual;Score_SIM_Total_R1_MTUs_5_Rng;
Score_SIM_Total_R1_MTUs_6;Individual;Score_SIM_Total_R1_MTUs_6;
Score_SIM_Total_R1_MTUs_6_Rng;Individual;Score_SIM_Total_R1_MTUs_6_Rng;
Score_SIM_Total_R1_MTUs_7;Individual;Score_SIM_Total_R1_MTUs_7;
Score_SIM_Total_R1_MTUs_7_Rng;Individual;Score_SIM_Total_R1_MTUs_7_Rng;
Score_SIM_Total_R1_MTUs_8;Individual;Score_SIM_Total_R1_MTUs_8;
Score_SIM_Total_R1_MTUs_8_Rng;Individual;Score_SIM_Total_R1_MTUs_8_Rng;

Score_SIM_Total_R1_KPI1_Impacts;Individual;Score_SIM_Total_R1_KPI1_Impacts;
Score_SIM_Total_R1_KPI1_MTUs_Impact;Individual;Score_SIM_Total_R1_KPI1_MTUs_Impact;
Score_SIM_Total_R1_KPI1_MTUs_Impact_Pre;Individual;Score_SIM_Total_R1_KPI1_MTUs_Impact_Pre;
Score_SIM_Total_R1_KPI1_Base;Individual;Score_SIM_Total_R1_KPI1_Base;
Score_SIM_Total_R1_KPI1_Offset;Individual;Score_SIM_Total_R1_KPI1_Offset;

Score_SIM_Total_R1;Individual;Score_SIM_Total_R1;

Q_SIM_R2_Scenario1;Individual;Q_SIM_R2_Scenario1;1:!{Choice_Opt1};2:!{Choice_Opt2};3:!{Choice_Opt3};4:!{Choice_Opt4};

Score_SIM_R2_Scenario1_KPI1;Individual;Score_SIM_R2_Scenario1_KPI1;
Score_SIM_R2_Scenario1_KPI2;Individual;Score_SIM_R2_Scenario1_KPI2;
Score_SIM_R2_Scenario1_KPI3;Individual;Score_SIM_R2_Scenario1_KPI3;
Score_SIM_R2_Scenario1_KPI4;Individual;Score_SIM_R2_Scenario1_KPI4;
Score_SIM_R2_Scenario1_MTUs_1;Individual;Score_SIM_R2_Scenario1_MTUs_1;
Score_SIM_R2_Scenario1_MTUs_2;Individual;Score_SIM_R2_Scenario1_MTUs_2;
Score_SIM_R2_Scenario1_MTUs_3;Individual;Score_SIM_R2_Scenario1_MTUs_3;
Score_SIM_R2_Scenario1_MTUs_4;Individual;Score_SIM_R2_Scenario1_MTUs_4;
Score_SIM_R2_Scenario1_MTUs_5;Individual;Score_SIM_R2_Scenario1_MTUs_5;
Score_SIM_R2_Scenario1_MTUs_6;Individual;Score_SIM_R2_Scenario1_MTUs_6;
Score_SIM_R2_Scenario1_MTUs_7;Individual;Score_SIM_R2_Scenario1_MTUs_7;
Score_SIM_R2_Scenario1_MTUs_8;Individual;Score_SIM_R2_Scenario1_MTUs_8;

Score_SIM_R2_Roleplay;Individual;Score_SIM_R2_Roleplay;
Score_SIM_R2_Roleplay_Impact;Individual;Score_SIM_R2_Roleplay_Impact;

Score_SIM_Total_R2_KPI1;Individual;Score_SIM_Total_R2_KPI1;
Score_SIM_Total_R2_KPI2;Individual;Score_SIM_Total_R2_KPI2;
Score_SIM_Total_R2_KPI3;Individual;Score_SIM_Total_R2_KPI3;
Score_SIM_Total_R2_KPI4;Individual;Score_SIM_Total_R2_KPI4;
Score_SIM_Total_R2_MTUs_1;Individual;Score_SIM_Total_R2_MTUs_1;
Score_SIM_Total_R2_MTUs_1_Rng;Individual;Score_SIM_Total_R2_MTUs_1_Rng;
Score_SIM_Total_R2_MTUs_2;Individual;Score_SIM_Total_R2_MTUs_2;
Score_SIM_Total_R2_MTUs_2_Rng;Individual;Score_SIM_Total_R2_MTUs_2_Rng;
Score_SIM_Total_R2_MTUs_3;Individual;Score_SIM_Total_R2_MTUs_3;
Score_SIM_Total_R2_MTUs_3_Rng;Individual;Score_SIM_Total_R2_MTUs_3_Rng;
Score_SIM_Total_R2_MTUs_4;Individual;Score_SIM_Total_R2_MTUs_4;
Score_SIM_Total_R2_MTUs_4_Rng;Individual;Score_SIM_Total_R2_MTUs_4_Rng;
Score_SIM_Total_R2_MTUs_5;Individual;Score_SIM_Total_R2_MTUs_5;
Score_SIM_Total_R2_MTUs_5_Rng;Individual;Score_SIM_Total_R2_MTUs_5_Rng;
Score_SIM_Total_R2_MTUs_6;Individual;Score_SIM_Total_R2_MTUs_6;
Score_SIM_Total_R2_MTUs_6_Rng;Individual;Score_SIM_Total_R2_MTUs_6_Rng;
Score_SIM_Total_R2_MTUs_7;Individual;Score_SIM_Total_R2_MTUs_7;
Score_SIM_Total_R2_MTUs_7_Rng;Individual;Score_SIM_Total_R2_MTUs_7_Rng;
Score_SIM_Total_R2_MTUs_8;Individual;Score_SIM_Total_R2_MTUs_8;
Score_SIM_Total_R2_MTUs_8_Rng;Individual;Score_SIM_Total_R2_MTUs_8_Rng;

Score_SIM_Total_R2_KPI1_Impacts;Individual;Score_SIM_Total_R2_KPI1_Impacts;
Score_SIM_Total_R2_KPI1_MTUs_Impact;Individual;Score_SIM_Total_R2_KPI1_MTUs_Impact;
Score_SIM_Total_R2_KPI1_MTUs_Impact_Pre;Individual;Score_SIM_Total_R2_KPI1_MTUs_Impact_Pre;
Score_SIM_Total_R2_KPI1_Base;Individual;Score_SIM_Total_R2_KPI1_Base;
Score_SIM_Total_R2_KPI1_Offset;Individual;Score_SIM_Total_R2_KPI1_Offset;

Score_SIM_Total_R2;Individual;Score_SIM_Total_R2;

Q_SIM_R3_Scenario1;Individual;Q_SIM_R3_Scenario1;1:!{Choice_Opt1};2:!{Choice_Opt2};3:!{Choice_Opt3};4:!{Choice_Opt4};

Score_SIM_R3_Scenario1_KPI1;Individual;Score_SIM_R3_Scenario1_KPI1;
Score_SIM_R3_Scenario1_KPI2;Individual;Score_SIM_R3_Scenario1_KPI2;
Score_SIM_R3_Scenario1_KPI3;Individual;Score_SIM_R3_Scenario1_KPI3;
Score_SIM_R3_Scenario1_KPI4;Individual;Score_SIM_R3_Scenario1_KPI4;
Score_SIM_R3_Scenario1_MTUs_1;Individual;Score_SIM_R3_Scenario1_MTUs_1;
Score_SIM_R3_Scenario1_MTUs_2;Individual;Score_SIM_R3_Scenario1_MTUs_2;
Score_SIM_R3_Scenario1_MTUs_3;Individual;Score_SIM_R3_Scenario1_MTUs_3;
Score_SIM_R3_Scenario1_MTUs_4;Individual;Score_SIM_R3_Scenario1_MTUs_4;
Score_SIM_R3_Scenario1_MTUs_5;Individual;Score_SIM_R3_Scenario1_MTUs_5;
Score_SIM_R3_Scenario1_MTUs_6;Individual;Score_SIM_R3_Scenario1_MTUs_6;
Score_SIM_R3_Scenario1_MTUs_7;Individual;Score_SIM_R3_Scenario1_MTUs_7;
Score_SIM_R3_Scenario1_MTUs_8;Individual;Score_SIM_R3_Scenario1_MTUs_8;

Score_SIM_R3_Roleplay;Individual;Score_SIM_R3_Roleplay;
Score_SIM_R3_Roleplay_Impact;Individual;Score_SIM_R3_Roleplay_Impact;

Score_SIM_Total_R3_KPI1;Individual;Score_SIM_Total_R3_KPI1;
Score_SIM_Total_R3_KPI2;Individual;Score_SIM_Total_R3_KPI2;
Score_SIM_Total_R3_KPI3;Individual;Score_SIM_Total_R3_KPI3;
Score_SIM_Total_R3_KPI4;Individual;Score_SIM_Total_R3_KPI4;
Score_SIM_Total_R3_MTUs_1;Individual;Score_SIM_Total_R3_MTUs_1;
Score_SIM_Total_R3_MTUs_1_Rng;Individual;Score_SIM_Total_R3_MTUs_1_Rng;
Score_SIM_Total_R3_MTUs_2;Individual;Score_SIM_Total_R3_MTUs_2;
Score_SIM_Total_R3_MTUs_2_Rng;Individual;Score_SIM_Total_R3_MTUs_2_Rng;
Score_SIM_Total_R3_MTUs_3;Individual;Score_SIM_Total_R3_MTUs_3;
Score_SIM_Total_R3_MTUs_3_Rng;Individual;Score_SIM_Total_R3_MTUs_3_Rng;
Score_SIM_Total_R3_MTUs_4;Individual;Score_SIM_Total_R3_MTUs_4;
Score_SIM_Total_R3_MTUs_4_Rng;Individual;Score_SIM_Total_R3_MTUs_4_Rng;
Score_SIM_Total_R3_MTUs_5;Individual;Score_SIM_Total_R3_MTUs_5;
Score_SIM_Total_R3_MTUs_5_Rng;Individual;Score_SIM_Total_R3_MTUs_5_Rng;
Score_SIM_Total_R3_MTUs_6;Individual;Score_SIM_Total_R3_MTUs_6;
Score_SIM_Total_R3_MTUs_6_Rng;Individual;Score_SIM_Total_R3_MTUs_6_Rng;
Score_SIM_Total_R3_MTUs_7;Individual;Score_SIM_Total_R3_MTUs_7;
Score_SIM_Total_R3_MTUs_7_Rng;Individual;Score_SIM_Total_R3_MTUs_7_Rng;
Score_SIM_Total_R3_MTUs_8;Individual;Score_SIM_Total_R3_MTUs_8;
Score_SIM_Total_R3_MTUs_8_Rng;Individual;Score_SIM_Total_R3_MTUs_8_Rng;

Score_SIM_Total_R3_KPI1_Impacts;Individual;Score_SIM_Total_R3_KPI1_Impacts;
Score_SIM_Total_R3_KPI1_MTUs_Impact;Individual;Score_SIM_Total_R3_KPI1_MTUs_Impact;
Score_SIM_Total_R3_KPI1_MTUs_Impact_Pre;Individual;Score_SIM_Total_R3_KPI1_MTUs_Impact_Pre;
Score_SIM_Total_R3_KPI1_Base;Individual;Score_SIM_Total_R3_KPI1_Base;
Score_SIM_Total_R3_KPI1_Offset;Individual;Score_SIM_Total_R3_KPI1_Offset;

Score_SIM_Total_R3;Individual;Score_SIM_Total_R3;

Q_SIM_R4_Scenario1;Individual;Q_SIM_R4_Scenario1;1:!{Choice_Opt1};2:!{Choice_Opt2};3:!{Choice_Opt3};4:!{Choice_Opt4};

Score_SIM_R4_Scenario1_KPI1;Individual;Score_SIM_R4_Scenario1_KPI1;
Score_SIM_R4_Scenario1_KPI2;Individual;Score_SIM_R4_Scenario1_KPI2;
Score_SIM_R4_Scenario1_KPI3;Individual;Score_SIM_R4_Scenario1_KPI3;
Score_SIM_R4_Scenario1_KPI4;Individual;Score_SIM_R4_Scenario1_KPI4;
Score_SIM_R4_Scenario1_MTUs_1;Individual;Score_SIM_R4_Scenario1_MTUs_1;
Score_SIM_R4_Scenario1_MTUs_2;Individual;Score_SIM_R4_Scenario1_MTUs_2;
Score_SIM_R4_Scenario1_MTUs_3;Individual;Score_SIM_R4_Scenario1_MTUs_3;
Score_SIM_R4_Scenario1_MTUs_4;Individual;Score_SIM_R4_Scenario1_MTUs_4;
Score_SIM_R4_Scenario1_MTUs_5;Individual;Score_SIM_R4_Scenario1_MTUs_5;
Score_SIM_R4_Scenario1_MTUs_6;Individual;Score_SIM_R4_Scenario1_MTUs_6;
Score_SIM_R4_Scenario1_MTUs_7;Individual;Score_SIM_R4_Scenario1_MTUs_7;
Score_SIM_R4_Scenario1_MTUs_8;Individual;Score_SIM_R4_Scenario1_MTUs_8;

Score_SIM_R4_Roleplay;Individual;Score_SIM_R4_Roleplay;
Score_SIM_R4_Roleplay_Impact;Individual;Score_SIM_R4_Roleplay_Impact;

Score_SIM_Total_R4_KPI1;Individual;Score_SIM_Total_R4_KPI1;
Score_SIM_Total_R4_KPI2;Individual;Score_SIM_Total_R4_KPI2;
Score_SIM_Total_R4_KPI3;Individual;Score_SIM_Total_R4_KPI3;
Score_SIM_Total_R4_KPI4;Individual;Score_SIM_Total_R4_KPI4;
Score_SIM_Total_R4_MTUs_1;Individual;Score_SIM_Total_R4_MTUs_1;
Score_SIM_Total_R4_MTUs_1_Rng;Individual;Score_SIM_Total_R4_MTUs_1_Rng;
Score_SIM_Total_R4_MTUs_2;Individual;Score_SIM_Total_R4_MTUs_2;
Score_SIM_Total_R4_MTUs_2_Rng;Individual;Score_SIM_Total_R4_MTUs_2_Rng;
Score_SIM_Total_R4_MTUs_3;Individual;Score_SIM_Total_R4_MTUs_3;
Score_SIM_Total_R4_MTUs_3_Rng;Individual;Score_SIM_Total_R4_MTUs_3_Rng;
Score_SIM_Total_R4_MTUs_4;Individual;Score_SIM_Total_R4_MTUs_4;
Score_SIM_Total_R4_MTUs_4_Rng;Individual;Score_SIM_Total_R4_MTUs_4_Rng;
Score_SIM_Total_R4_MTUs_5;Individual;Score_SIM_Total_R4_MTUs_5;
Score_SIM_Total_R4_MTUs_5_Rng;Individual;Score_SIM_Total_R4_MTUs_5_Rng;
Score_SIM_Total_R4_MTUs_6;Individual;Score_SIM_Total_R4_MTUs_6;
Score_SIM_Total_R4_MTUs_6_Rng;Individual;Score_SIM_Total_R4_MTUs_6_Rng;
Score_SIM_Total_R4_MTUs_7;Individual;Score_SIM_Total_R4_MTUs_7;
Score_SIM_Total_R4_MTUs_7_Rng;Individual;Score_SIM_Total_R4_MTUs_7_Rng;
Score_SIM_Total_R4_MTUs_8;Individual;Score_SIM_Total_R4_MTUs_8;
Score_SIM_Total_R4_MTUs_8_Rng;Individual;Score_SIM_Total_R4_MTUs_8_Rng;

Score_SIM_Total_R4_KPI1_Impacts;Individual;Score_SIM_Total_R4_KPI1_Impacts;
Score_SIM_Total_R4_KPI1_MTUs_Impact;Individual;Score_SIM_Total_R4_KPI1_MTUs_Impact;
Score_SIM_Total_R4_KPI1_MTUs_Impact_Pre;Individual;Score_SIM_Total_R4_KPI1_MTUs_Impact_Pre;
Score_SIM_Total_R4_KPI1_Base;Individual;Score_SIM_Total_R4_KPI1_Base;
Score_SIM_Total_R4_KPI1_Offset;Individual;Score_SIM_Total_R4_KPI1_Offset;

Score_SIM_Total_R4;Individual;Score_SIM_Total_R4;


;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;
;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;


Sections:;Actions;Template;Related Question;Custom Parameter;

LandingPage_Pause;;10;
;ShowSlide;Show;;LandingPage_Pause;

R1_LandingPage;;20;
;ShowSlide;Show;;R1_LandingPage;
SIM_R1_TeamName;;30;
;ShowSlide;Show;;SIM_R1_TeamName;
SIM_CaseStudy;;40;
;ShowSlide;Show;;SIM_CaseStudy;
SIM_R1_Scenario1;;50;
;ShowSlide;Show;;SIM_R1_Scenario1;
SIM_R1_Scenario1_AGG;;60;
;ShowSlide;Show;;SIM_R1_Scenario1_AGG;
SIM_R1_Scenario1_FB;;70;
;ShowSlide;Show;;SIM_R1_Scenario1_FB;
SIM_R1_Scenario2;;80;
;ShowSlide;Show;;SIM_R1_Scenario2;
R1_DebriefPage;;90;
;ShowSlide;Show;;R1_DebriefPage;

R2_LandingPage;;100;
;ShowSlide;Show;;R2_LandingPage;
SIM_R2_Scenario1;;110;
;ShowSlide;Show;;SIM_R2_Scenario1;
SIM_R2_Scenario1_AGG;;120;
;ShowSlide;Show;;SIM_R2_Scenario1_AGG;
SIM_R2_Scenario1_FB;;130;
;ShowSlide;Show;;SIM_R2_Scenario1_FB;
SIM_R2_Scenario2;;140;
;ShowSlide;Show;;SIM_R2_Scenario2;
R2_DebriefPage;;150;
;ShowSlide;Show;;R2_DebriefPage;

R3_LandingPage;;160;
;ShowSlide;Show;;R3_LandingPage;
SIM_R3_Scenario1;;170;
;ShowSlide;Show;;SIM_R3_Scenario1;
SIM_R3_Scenario1_AGG;;180;
;ShowSlide;Show;;SIM_R3_Scenario1_AGG;
SIM_R3_Scenario1_FB;;190;
;ShowSlide;Show;;SIM_R3_Scenario1_FB;
SIM_R3_Scenario2;;200;
;ShowSlide;Show;;SIM_R3_Scenario2;
R3_DebriefPage;;210;
;ShowSlide;Show;;R3_DebriefPage;

R4_LandingPage;;220;
;ShowSlide;Show;;R4_LandingPage;
SIM_R4_Scenario1;;230;
;ShowSlide;Show;;SIM_R4_Scenario1;
SIM_R4_Scenario1_AGG;;240;
;ShowSlide;Show;;SIM_R4_Scenario1_AGG;
SIM_R4_Scenario1_FB;;250;
;ShowSlide;Show;;SIM_R4_Scenario1_FB;
SIM_R4_Scenario2;;260;
;ShowSlide;Show;;SIM_R4_Scenario2;
R4_DebriefPage;;270;
;ShowSlide;Show;;R4_DebriefPage;

R5_LandingPage;;;
;ShowSlide;Show;;R5_LandingPage;
SIM_R5_Scenario1;;;
;ShowSlide;Show;;SIM_R5_Scenario1;
SIM_R5_Scenario1_AGG;;;
;ShowSlide;Show;;SIM_R5_Scenario1_AGG;
SIM_R5_Scenario1_FB;;;
;ShowSlide;Show;;SIM_R5_Scenario1_FB;
SIM_R5_Scenario2;;;
;ShowSlide;Show;;SIM_R5_Scenario2;
R5_DebriefPage;;;
;ShowSlide;Show;;R5_DebriefPage;

R6_LandingPage;;;
;ShowSlide;Show;;R6_LandingPage;
SIM_R6_Scenario1;;;
;ShowSlide;Show;;SIM_R6_Scenario1;
SIM_R6_Scenario1_AGG;;;
;ShowSlide;Show;;SIM_R6_Scenario1_AGG;
SIM_R6_Scenario1_FB;;;
;ShowSlide;Show;;SIM_R6_Scenario1_FB;
SIM_R6_Scenario2;;;
;ShowSlide;Show;;SIM_R6_Scenario2;
R6_DebriefPage;;;
;ShowSlide;Show;;R6_DebriefPage;

LandingPage_FAC;;289;
;ShowSlide;Show;;LandingPage_FAC;

R1_LandingPage_FAC;;290;
;ShowSlide;Show;;R1_LandingPage_FAC;
SIM_R1_Init_FAC;;300;
;ShowSlide;Show;;SIM_R1_Init_FAC;
SIM_R1_FAC_dashboard;;310;
;ShowSlide;Show;;SIM_R1_FAC_dashboard;
R1_DebriefPage_FAC;;320;
;ShowSlide;Show;;R1_DebriefPage_FAC;

R2_LandingPage_FAC;;330;
;ShowSlide;Show;;R2_LandingPage_FAC;
SIM_R2_Init_FAC;;340;
;ShowSlide;Show;;SIM_R2_Init_FAC;
SIM_R2_FAC_dashboard;;350;
;ShowSlide;Show;;SIM_R2_FAC_dashboard;
R2_DebriefPage_FAC;;360;
;ShowSlide;Show;;R2_DebriefPage_FAC;

R3_LandingPage_FAC;;370;
;ShowSlide;Show;;R3_LandingPage_FAC;
SIM_R3_Init_FAC;;380;
;ShowSlide;Show;;SIM_R3_Init_FAC;
SIM_R3_FAC_dashboard;;390;
;ShowSlide;Show;;SIM_R3_FAC_dashboard;
R3_DebriefPage_FAC;;400;
;ShowSlide;Show;;R3_DebriefPage_FAC;

R4_LandingPage_FAC;;410;
;ShowSlide;Show;;R4_LandingPage_FAC;
SIM_R4_Init_FAC;;420;
;ShowSlide;Show;;SIM_R4_Init_FAC;
SIM_R4_FAC_dashboard;;430;
;ShowSlide;Show;;SIM_R4_FAC_dashboard;
R4_DebriefPage_FAC;;440;
;ShowSlide;Show;;R4_DebriefPage_FAC;


;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;
;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;


TextContent:;en:English:False;
Header_logo;"/images/client/clientLogo.png";
Header_logo_menu;"/images/client/clientLogo.png";
Header_LinkSection_Info;"Information";
Header_LinkSection_Feedbacks;"Feedbacks";
Header_LinkSection_Control;"Control";
Header_Link_ControlStart;"Re-Start";
Header_Link_ControlFinish;"Finish";
Header_LinkLogins;"Accesses";
Header_LinkCase;"Case Study";
Header_LinkAssess;"Assess Role-Play";
Header_LinkSummary;"Summary";
Header_LinkStrategy;"Your strategy";
Header_LinkSwot;"SWOT Analysis";
Header_LinkTeams;"Teams";
Header_Reset;"Remove my data";
Header_LinkRefresh;"Refresh";
Header_LinkHome;"Home";
Header_LinkExit;"Logout";
Header_LinkClose;"Close";
Header_Follower;"Follower";
Header_LinkRanking;"Leaderboard";
Header_LinkLeague;"League";
Header_LinkResults;"Results";
Header_LinkChoices;"Answers";
Header_LinkOrder;"Order";
Header_LinkChart;"Chart";
Header_LinkTable;"Table";
Header_LinkGlossary;"Glossary";
Header_LinkSolution;"Solution";
Header_LinkScenario;"Moment";
Header_LinkDecision;"Decision";
Header_LinkSection_Language;"Language";
Header_LinkLanguageEn;"English";
Header_LinkLanguageEs;"Español";
Header_LinkHelp;"Help";
Header_Modal_Help_Text;"<h3>Contact</h3><img class=""embed left small"" src=""/Wizer/Pages/Events/Mubadala_MBS_2025/images/bts.png"" style=""border-radius:10px"">This is a standard Scenarios Simulation.<br>If you have any question or technical problems, you can contact us at this email address: <b><a href=""mailto:<EMAIL>&subject=Mubadala_MBS_2025""><i><EMAIL></i><i class=""tiny material-icons"">mail_outline</i></a></b>";
Header_Modal_ResetHeader;"Remove my data";
Header_Modal_ResetText;"<span class=""strong""> Are you sure you want to delete all your answers and stored score? <span> <br> If you are, all your information will be removed for you to start again.";
Header_Modal_ResetTitle;"Enter password:";
Header_Modal_Password;"Password";
Header_Modal_Code;"287";
Header_Modal_PasswordRight;"Correct password";
Header_Modal_PasswordWrong;"Incorrect password";
Header_Modal_ResetBtn;"Delete information";
Header_Modal_ResetToast;"All data deleted";
Header_Modal_Logout_Title;"Quit the application";
Header_Modal_Logout_Text;"Please confirm that you want to log out.";
Header_Modal_Logout_Close;"Cancel";
Header_Modal_Logout_Logout;"Yes, exit";
Header_Modal_Assess_Title;"Assess score";
Header_Modal_Assess_Text;"Below, you can assign values for the roleplay for each user (0-100):";
Header_Modal_Assess_Head1;"User";
Header_Modal_Assess_Head2;"Score";
Header_Modal_Assess_Head3;"Value";
Header_Modal_Assess_Submit;"Save";
Landing_Title;"Event Template";
Landing_Subtitle;"Welcome";
Footer_Title;"Mubadala";
Footer_Subtitle;"Leadership Excellence & Acceleration Program";
Footer_Copyright;"BTS";
Footer_Link;"https://www.bts.com";
Footer_Img;"/images/footer-color-bar.svg";
Navigation_Header;"Jump to screen...";
Navigation_menu;"Other screens";
Navigation_menu_tt;"go to screen";
Navigation_previous;"Previous";
Navigation_previous_tt;"go to previous";
Navigation_next;"Next";
Navigation_next_tt;"go to next";
Navigation_nextTab;"Next Tab";
Navigation_nextTab_tt;"go to next tab";
Navigation_start;"Start";
Navigation_start_tt;"start the SIM";
Navigation_westart;"Start";
Navigation_westart_tt;"start";
Navigation_restart;"Restart";
Navigation_restart_tt;"restart session";
Navigation_feedback;"Feedback";
Navigation_feedback_tt;"decision feedback";
Navigation_moreInfo;"More info";
Navigation_moreInfo_tt;"see further details";
Navigation_readEmail;"Read email";
Navigation_readEmail_tt;"Read text";
Navigation_summary;"Summary";
Navigation_summary_tt;"Open summary";
Navigation_submit;"Submit";
Navigation_submit_tt;"Submit answers";
Navigation_submitAll;"Submit All";
Navigation_submitModalHeader;"Submit";
Navigation_submitModalText;"Are you sure you want to confirm your decision? Once confirmed, you will not be able to change it anymore.";
Navigation_submitModalClose;"Cancel";
Navigation_submitAllModalHeader;"Submit All";
Navigation_submitAllModalText;"Are you sure you want to confirm all your decisions? Once confirmed, you will not be able to change them anymore.";
Navigation_submitAllModalClose;"Cancel";
Navigation_finish;"Finish";
Navigation_finish_tt;"finish the SIM";
Navigation_SIM_Start_Header;"Re-Start round";
Navigation_SIM_Finish_Header;"Finish round";
Navigation_SIM_Start_Text;"<span class=""highlight"">Do you want to re-start the round and get the control back? (any saved data will be removed)</span>";
Navigation_SIM_Finish_Text;"<span class=""highlight"">Do you want to finish the round and give the control back to the facilitator? (any saved data will be removed)</span>";
Navigation_SIM_Title;"Input password:";
Navigation_SIM_Password;"Password:";
Navigation_SIM_Code;"bts";
Navigation_SIM_PasswordRight;"Password correct";
Navigation_SIM_PasswordWrong;"Password incorrect";
Navigation_SIM_Start_Btn;"Re-Start";
Navigation_SIM_Finish_Btn;"Finish";
GroupDirector_TitleGD;"Session Director";
GroupDirector_Results_TitleGD;"Show results";
GroupDirector_VoteLabel;"Decisions made";
GroupDirector_TitleFAC;"Results screen";
GroupDirector_ExportBtn;"Export data";
GroupDirector_Export_Modal_Title;"Export session data";
GroupDirector_Export_Modal_Text;"Please, confirm you want to export information of the session participants (decisions, responses, impacts, scores ...) in a CSV file that will be automatically downloaded to your computer";
GroupDirector_Export_Modal_Close;"No, cancel";
GroupDirector_Export_Modal_Action;"Yes, download";
GroupDirector_ResetBtn;"Remove answers";
GroupDirector_ResetModalHeader;"Remove answers";
GroupDirector_ResetModalText;"<span class=""strong""> Are you sure you want to delete all participant responses and saved scores? </span> <br> If you agree, all information will be erased. Do this if you wish start a new course. Do not do this if the course has already started.";
GroupDirector_ResetModalQuizText;"<span class=""strong""> Are you sure you want to delete all participants' saved answers and scores? </span> <br> If you accept, the answers for this test will be deleted. Do this if you want to launch the test again";
GroupDirector_ResetModalClose;"No, cancel";
GroupDirector_ResetModalReset;"Yes, delete";
Badge_Opt1;"1";
Badge_Opt2;"2";
Badge_Opt3;"3";
Badge_Opt4;"4";
Badge_Opt5;"5";
Badge_Opt6;"6";
Badge_Opt7;"7";
Badge_Opt8;"8";
Badge_Opt9;"9";
Badge_Opt10;"10";
Badge_Opt11;"11";
Badge_Opt12;"12";
Badge_Opt13;"13";
Badge_Opt14;"14";
Badge_Opt15;"15";
Badge_Opt16;"16";
Badge_Opt17;"17";
Badge_Opt18;"18";
Badge_Opt19;"19";
Badge_Opt20;"20";
Badge_Opt21;"21";
Badge_Opt22;"22";
Badge_Opt23;"23";
Badge_Opt24;"24";
Badge_Opt25;"25";
Badge_Opt26;"26";
Badge_Opt27;"27";
Badge_Opt28;"28";
Badge_Opt29;"29";
Badge_Opt30;"30";
Choice_Submitted;"Submitted";
Choice_OptYES;"YES";
Choice_OptNO;"NO";
Choice_OptNone;"-";
Choice_MoreInfo;"Show description";
Choice_LessInfo;"Description";
Choice_Opt0;"-";
Choice_Opt1;"A";
Choice_Opt2;"B";
Choice_Opt3;"C";
Choice_Opt4;"D";
Choice_Opt5;"E";
Choice_Opt6;"F";
Choice_Opt7;"G";
Choice_Opt8;"H";
Choice_Correct;"Correct";
Choice_btnCheck;"Confirm";
Choice_btnReset;"Reset";
Choice_btnResults;"Results";
Choice_btnFeedback;"Feedback";
Choice_toastCorrect;"You are right!";
Choice_toastIncorrect;"That's not right...";
Quiz_pointsIfAllCorrect;"3";
Quiz_pointsIfSomeCorrect;"1";
Quiz_pointsIfCorrect;"1";
Quiz_pointsIfIncorrect;"0";
Quiz_btnPlay;"Play";
Quiz_Dice_Instructions;"Congratulations! Now roll the dice. The value obtained will be your score for this question";
Quiz_Dice_Score_pre;"You got";
Quiz_Dice_Score_post;"points";
Quiz_Timer_leadText;"Remaining time:";
Quiz_Timer_time;"1:00";
Quiz_Timer_timeUpText;"Time's over!";
Quiz_Timer_gongImg;"/media/playGong.gif";
Quiz_Timer_gongAudio;"/media/playGong.mp3";
Quiz_Timer_gongText;"There's no time to answer any more questions. You can see your score on the next screen...";
Scenario_ChallengeTab;"Event";
Scenario_ChoiceTab;"Decission";
Scenario_ChoicesTab;"Decissions";
Scenario_pointsIfCorrect;"3";
Scenario_pointsIfIncorrect;"0";
Feedback_Correct;"Correct!";
Feedback_Incorrect;"That's not correct";
Feedback_Almost;"Almost!";
Feedback_Opt;"Option";
Feedback_MyOpt;"My answer";
Feedback_LottieOK;"/content/lottie_right.json";
Feedback_LottieKO;"/content/lottie_wrong.json";
Feedback_GifOK;"/content/4964-check-mark-success-animation.gif";
Feedback_GifKO;"/content/4970-unapproved-cross.gif";
ZoomInstructions;"Click on the image to zoom in";
Input_GIF;"/content/25241-chat-typing.gif";
Input_Lottie;"/content/25241-chat-typing.json";
InputInstructions;"Write your answer and when you're done, press “Save” to send.";
InputInstructionsMulti;"Write your answer and when you're done, press “Save” to send. You can send as many as you want.";
InputInstructionsAnswers;"Below, all participants' answers list:";
InputYourAnswers;"My answers:";
InputAllAnswers;"Everybody answers:";
InputInstructionsYourAnswers;"Below, your answers list:";
InputRemaining;"maximum characters";
InputRight;"Correct field";
InputWrong;"Error, validate field";
InputClear;"Clear";
InputCleared;"Text cleared";
InputSend;"Send";
InputForm;"Confirm";
InputForm_PopUp;"Update";
InputForm_OK;"Registered successfully";
InputForm_OK_PopUp;"Updated successfully";
InputForm_KO;"Registration failed, validates the fields entered";
InputName;"Name saved";
InputSent;"Answer sent";
InputSubmit;"Save";
InputSubmited;"Answer saved";
InputRemoved;"Answer removed";
InputCheck;"Check";
InputSaved;"Saved";
Me;"Me";
Results_VotesLabel;"Answers:";
Results_TeamsLabel;"Participants:";
RadioInstructions;"Select one of the following options and press “Confirm” to save your answer:";
RadioInstructionsCascade;"Select one of the following options and press “Confirm” to save it and to show the next one:";
RadioInstructionsAnswers;"Below, all participants' answers distribution:";
CheckboxesInstructions;"Select all the options you consider and press “Confirm” to save your answer:";
CheckboxesInstructionsAnswers;"Below, all participants' answers distribution:";
AllInitiativesInstructions;"Below, all participants' total chosen initiatives:";
AllStrategiesInstructions;"Below, all participants' total chosen strategies.";
Checkboxes_maxOptions;"Maximum number of options selected";
Checkboxes_allOptions;"All options selected";
SlidersInstructionsAnswers;"Below, the average of the evaluations given by the participants";
Prioritize_Instructions;"Please, prioritize the options below:";
IndividualResults_User;"Participant";
IndividualResults_Choice;"Choice";
IndividualResults_Header;"All the answers";
URL_media_server;"https://media.btspulse.com/products/pulse_events/clients/XXX";
Ranking_Header;"Ranking";
Ranking_Position;"Position";
Ranking_User;"Participant";
Ranking_Score;"Points";
Ranking_Score_Test;"Test score";
Ranking_TotalScore;"Total points";
Ranking_Trophy_Lottie;"/content/lottie_trophy.json";
KPI_Title;"KPIs impact";
KPI_Trivia;"Trivia";
KPI_Points;"Points";
KPI_Score;"SCORE";
KPI_TOTAL;"TOTAL";
KPI_Metric1_Init;"5";
KPI_Metric2_Init;"5";
KPI_Metric3_Init;"5";
KPI_Metric4_Init;"5";
KPI_MTUs_1_Init;"10";
KPI_MTUs_2_Init;"5";
KPI_MTUs_3_Init;"5";
KPI_MTUs_4_Init;"5";
KPI_MTUs_5_Init;"5";
KPI_MTUs_6_Init;"5";
KPI_MTUs_7_Init;"5";
KPI_MTUs_8_Init;"5";
KPI_Metric1_Min;"0";
KPI_Metric2_Min;"0";
KPI_Metric3_Min;"0";
KPI_Metric4_Min;"0";
KPI_MTUs_1_Min;"0";
KPI_MTUs_2_Min;"0";
KPI_MTUs_3_Min;"0";
KPI_MTUs_4_Min;"0";
KPI_MTUs_5_Min;"0";
KPI_MTUs_6_Min;"0";
KPI_MTUs_7_Min;"0";
KPI_MTUs_8_Min;"0";
KPI_Metric1_Max;"10";
KPI_Metric2_Max;"10";
KPI_Metric3_Max;"10";
KPI_Metric4_Max;"10";
KPI_MTUs_1_Max;"10";
KPI_MTUs_2_Max;"5";
KPI_MTUs_3_Max;"5";
KPI_MTUs_4_Max;"5";
KPI_MTUs_5_Max;"5";
KPI_MTUs_6_Max;"5";
KPI_MTUs_7_Max;"5";
KPI_MTUs_8_Max;"5";
KPI_Total_Min;"0";
KPI_Total_Max;"100";
Score_MTUs_Multiplier;"0.1";
Score_Roleplay_Multiplier;"0.05";
Score_MTUs_Offset;"-16";
GroupDirector_Tab9;"Extras";
GroupDirector_Text9;"Extra questions";
GD_LandingPage;"Landing Page";
GD_Agg;"Re-Run All Calculations";
LandingPage_Title;"Welcome!";
GD_DebriefPage;"Round debrief";
GD_SummaryPage;"Round summary (*backup model*)";
DebriefPage_Title;"Thank you!";
LandingPage_ImgFac;"/images/client/clientLogoSplash.png";
LandingPage_Img0;"/images/client/clientLogoSplash.png";
LandingPage_Img1;"/images/client/clientLogoSplash.png";
LandingPage_Img2;"/images/client/clientLogoSplash.png";
LandingPage_Img3;"/images/client/clientLogoSplash.png";
LandingPage_Img4;"/images/client/clientLogoSplash.png";
LandingPage_Img5;"/images/client/clientLogoSplash.png";
LandingPage_Img6;"/images/client/clientLogoSplash.png";
LandingPage_Img7;"/images/client/clientLogoSplash.png";
LandingPage_Img8;"/images/client/clientLogoSplash.png";
LandingPage_Img9;"/images/client/clientLogoSplash.png";
LandingPage_Img10;"/images/client/clientLogoSplash.png";
GD_TeamName;"Team name";
GD_TeamName_Table;"Participants table";
TeamName_Header;"Name and avatar for your team";
TeamName_Title;"Your name and character";
TeamName_Body;"Choose a name and a character for your team. Press “Confirm” to save it.";
TeamName_Placeholder;"New name...";
TeamName_length;"50";
TeamName_Avatar;"Avatar";
TeamName_Avatar_select;"Choose a character";
defaultAvatar;"Avatar0.png";
TeamName_Table_InfoTitle;"Session participants";
TeamName_Table_Position;"Participant";
TeamName_Table_User;"Team number";
TeamName_Table_Name;"Team name";
TeamName_Table_Records;"Total participants:";
Logins_Table_InfoTitle;"Access register";
Logins_Table_Position;"Participant";
Logins_Table_User;"Participant";
Logins_Table_Name;"Time";
Logins_Table_Login;"Beginning time";
Logins_Table_Logout;"Ending time";
Logins_Table_Records;"Total users:";
Logins_Table_Screen;"Current screen";
TeamName_ImgHeader;"Let's start...";
TeamName_Img;"/images/intro.webp";
SIM_BreadCrumbs_1;"Moment 1";
SIM_BreadCrumbs_2;"Moment 2";
SIM_BreadCrumbs_3;"Moment 3";
SIM_BreadCrumbs_4;"Moment 4";
SIM_Scenario_Tab1;"Moment";
SIM_Scenario_Tab2;"Decision";
GroupDirector_Tab1;"Round 1";
GroupDirector_Tab2;"Round 2";
GroupDirector_Tab3;"Round 3";
GroupDirector_Tab4;"Round 4";
Header_Modal_Assess_Tab1;"Round 1";
Header_Modal_Assess_Tab2;"Round 2";
Header_Modal_Assess_Tab3;"Round 3";
Header_Modal_Assess_Tab4;"Round 4";
GroupDirector_Text1;"Round 1";
GroupDirector_Text2;"Round 2";
GroupDirector_Text3;"Round 3";
GroupDirector_Text4;"Round 4";
Header_Link_Feedback1;"Feedbacks Round1";
Header_Link_Feedback2;"Feedbacks Round2";
Header_Link_Feedback3;"Feedbacks Round3";
Header_Link_Feedback4;"Feedbacks Round4";
Header_LinkSummary1;"Summary Round1";
Header_LinkSummary2;"Summary Round2";
Header_LinkSummary3;"Summary Round3";
Header_LinkSummary4;"Summary Round4";
KPI_LTU;"MTUs";
KPI_Intelligence;"Intelligence Contribution";
KPI_Metric1;"Financial Results";
KPI_Metric2;"Engaging Others";
KPI_Metric3;"Building Relationships";
KPI_Metric4;"Effective Communication";
KPI_Metric_MTUs_1;"Your MTUs";
KPI_Metric_MTUs_2;"Leila's MTUs";
KPI_Metric_MTUs_3;"Omar's MTUs";
KPI_Metric_MTUs_4;"Fatima's MTUs";
KPI_Metric_MTUs_5;"Daniel's MTUs";
KPI_Metric_MTUs_6;"Maya's MTUs";
KPI_Metric_MTUs_7;"Adel's MTUs";
KPI_Metric_MTUs_8;"Dima's MTUs";
SIM_CaseStudy_Header;"Simulation Case";
SIM_CaseStudy1_Title;"Welcome to the Mubadira LEAP Empowering Growth Simulation!";
SIM_CaseStudy1_Text;"<span class=""highlight"">Dear Participant,</span><br><br>You have recently been promoted to <b>Team Lead</b> within <b>Mubadira’s Private Equity Platform</b>, previously known as Direct Investments. This marks an important moment for your development as a people leader and for Mubadira's organizational transformation.<br><br>This simulation places you in the middle of everyday leadership moments. You will coach, give feedback, navigate ambiguity, and guide your team through uncertainty. Your time and energy will be limited. Every decision you make will influence how your team evolves and how you grow as a leader.<br><br>As you prepare, consider the following:<br><br><ul class=""circle-list""><li>What is changing at Mubadira that will shape how you lead?</li><li>What are the strengths and development needs of your team?</li><li>What kind of leader do you want to be, and how will you earn your team’s trust?</li></ul>";
SIM_CaseStudy1_Instructions;"Click down to continue reading...";
SIM_CaseStudy2_Title;"Organizational Context: Mubadira Today";
SIM_CaseStudy2_Text;"You are re-entering Mubadira at a time of strategic acceleration. Over the past year, the company has doubled down on its mission to drive national resilience and global relevance. Guided by an updated investment thesis, Mubadira is shifting capital toward breakthrough innovation, sustainable infrastructure, and scalable platforms that enable long-term impact. The emphasis is on growing not only high-performing assets but also high-conviction partnerships in priority sectors like life sciences, AI, climate tech, and financial inclusion.<br><br>Internally, this means faster cycles, more pressure to deliver multi-dimensional value, and an increasing need for talent that can stretch across commercial, strategic, and operational boundaries. Mubadira is still grounded in long-term fundamentals, but its operating tempo has increased. Cross-platform collaboration is no longer optional — it is the default. Execution excellence remains critical, but leadership expectations are evolving. Managers are being asked to lead with clarity, grow others intentionally, and hold space for learning amid performance.<br><br>This is especially true within the <b>Private Equity Platform</b>, where you are now based. As Mubadira seeks to position Abu Dahab as a global hub for innovation, your platform is tasked with both advancing investment performance and shaping the ecosystem through strategic influence and talent development.<br><br>Within this broader context, your team is finding its footing. Following a reorganization, you now lead a small but high-potential portfolio support unit composed of direct reports and matrixed collaborators. You are stepping into the role after Maryam, a respected and steady presence, and the team is still adjusting to your leadership style.<br><br>Engagement is uneven. Trust is forming but fragile. Feedback tends to stay surface-level. Some individuals are unsure of what growth looks like — for themselves or the team. Others are moving fast but without full alignment. In this environment, leadership is not about having control. It is about activating potential, guiding with intention, and creating momentum through conversation, clarity, and coaching.<br><br>This simulation invites you to practice exactly that.";
SIM_CaseStudy2_Instructions;"Click down to continue reading...";
SIM_CaseStudy3_Title;"Your Role";
SIM_CaseStudy3_Text;"You are a <b>Senior Associate</b>, recently promoted to <b>Team Lead</b> of a small portfolio support unit within Mubadira’s <b>Private Equity Platform</b>. You report to a <b>Principal-level Investment Director</b> and work in a dynamic, cross-functional environment that includes <b>junior direct reports</b>, <b>matrixed contributors</b>, and <b>external collaborators</b>. Alongside your leadership responsibilities, you continue to contribute to investment initiatives across Mubadira’s priority sectors, <b>including life sciences</b>, <b>fintech</b>, and <b>sustainability</b>.<br><br>This transition from high-performing individual contributor to people leader comes at a moment when Mubadira is placing increased emphasis on leadership behaviors that accelerate business impact. You are now expected to:<br><br><ul class=""circle-list""><li>Communicate with clarity and purpose, using conversations to connect strategic intent to daily actions</li><li>Engage your team by creating space for ownership, recognizing potential, and encouraging contribution</li><li>Build relationships across levels and functions, fostering collaboration in a matrixed environment</li><li>Deliver financial and operational results while developing others and shaping culture</li></ul><br>You are stepping into this role after <b>Maryam</b>, a long-tenured and trusted leader whose leadership style set a clear tone for the team. While the team respects your track record, they are still adjusting to your presence and way of leading. Some are looking for direction. Others are waiting to see how you handle difficult moments.<br><br>This is your opportunity to shape what happens next — not only through what you deliver, but through how you listen, coach, align, and lead others through complexity.";
SIM_CaseStudy3_Instructions;"Click down to continue reading...";
SIM_CaseStudy4_Title;"Your Team";
SIM_CaseStudy4_Text;"This team came together during a recent restructuring, as Mubadira sharpened its focus on platform-specific value creation. While the structure has settled, the culture and collaboration patterns are still taking shape. Trust is emerging but not yet solid. Expectations are uneven, and the path to growth is unclear for many.<br><br>You’ve noticed the following:<br><br><ul class=""circle-list""><li>A few team members are starting to disengage quietly, withdrawing from broader discussions</li><li>Feedback tends to be cautious or delayed, with difficult topics left unspoken</li><li>Conversations often stick to tasks and execution, rather than direction and development</li><li>There is real potential in the group, but it is not being consistently activated</li></ul><br>The team is capable, but they need more than delegation. They are looking for someone who can create clarity, open up meaningful dialogue, and model a different way of leading. They need a leader who can invite participation, offer structure, and support them in owning both their performance and their development.<br><br>In this simulation, you will face two critical leadership moments. How you choose to engage will either strengthen trust and ownership or reinforce hesitation and misalignment. Your communication, presence, and ability to connect will shape what happens next.<br><br><span class=""highlight"">Who you will engage with in this Sprint:</span>";
SIM_CaseStudy4_Instructions;"Click down to continue reading...";
SIM_CaseStudy5_Title;"Leila Hamdan – Investment Officer (Direct Report)";
SIM_CaseStudy5_Text;"Leila has been with Mubadira for several years and brings a broad view of the organization, having rotated across multiple platforms before joining the Private Equity team. She is known for her professionalism, steady execution, and ability to navigate complex deliverables without fanfare. Colleagues appreciate her calm presence and her knack for anticipating what needs to be done. While she rarely seeks the spotlight, she holds a quiet influence within the team and is often the person others turn to for sound judgment and continuity.<br><br>She values stability and thoughtful work, and tends to take pride in being dependable. At the same time, she does not readily surface her aspirations or challenges, and can sometimes default to familiar tasks even when new opportunities are available. Her mindset is practical, and she appreciates clarity and meaningful engagement from those around her.";
SIM_CaseStudy5_Instructions;"Click down to continue reading...";
SIM_CaseStudy6_Title;"Omar Al Meeri – Analyst (Direct Report)";
SIM_CaseStudy6_Text;"Omar joined Mubadira through its graduate program and brings a strong sense of energy, optimism, and drive. He is quick to raise his hand, naturally gravitates toward visible opportunities, and enjoys being part of conversations that shape direction or influence outcomes. He is socially confident and adapts quickly in new environments, often drawing in others with his enthusiasm and willingness to contribute.<br><br>He is ambitious and eager to grow, and responds well to encouragement and challenge. At times, his eagerness can lead him to move faster than his preparation, and he may focus more on making an impression than on refining the details. He benefits from leaders who can channel his energy while reinforcing structure and accountability. When guided well, he brings creativity and momentum to the team.";
SIM_CaseStudy6_Instructions;"Click down to continue reading...";
SIM_CaseStudy10_Title;"How You Define Success";
SIM_CaseStudy10_Text;"Your role in this simulation is to demonstrate what great leadership looks like. Success is not about having all the answers, but about bringing out the best in others. This means inspiring ownership, building trust, communicating with clarity, and creating an environment where people can grow and perform.<br><br>At Mubadira, leadership is shaped by moments. It is about tuning in to what your team needs and responding with purpose and intention.<br><br>Throughout the simulation, your leadership will be evaluated based on the following core metrics:<br><br><ul class=""circle-list""><li><b>Engaging Others</b> - motivating your team and encouraging initiative</li><li><b>Building Relationships</b> - creating trust and alignment across individuals and functions</li><li><b>Effective Communication</b> - being clear, timely, and intentional in your messages</li><li><b>Financial Results</b> - making decisions that drive value and outcomes</li><li><b>Time Management</b> - using your limited time effectively to balance people and performance</li></ul>";
SIM_CaseStudy_Next;"You will need to navigate trade-offs. Every choice you make will impact your team’s momentum, results, and your own growth as a leader.<br><br>This is your opportunity to lead. <span class=""highlight"">Good luck.</span>";
SIM_CaseStudy_Img;"/images/client/caseStudy.jpg";
SIM_CaseStudy_Img_C1;"/images/client/Picture1.jpg";
SIM_CaseStudy_Img_C2;"/images/client/Picture2.png";
SIM_CaseStudy_Img_C3;"/images/client/Picture3.jpg";
SIM_CaseStudy_Img_C4;"/images/client/Picture4.png";
SIM_CaseStudy_Img_C5;"/images/client/Picture5.jpg";
SIM_CaseStudy_Img_C6;"/images/client/Picture6.jpg";
SIM_CaseStudy_Img_C7;"/images/client/Picture7.png";
SIM_CaseStudy_Name_C1;"Leila";
SIM_CaseStudy_Name_C2;"Omar";
SIM_CaseStudy_Name_You;"You";
Header_LinkLogins_R1;"Accesses Round1";
GroupDirector_ExportBtn_R1;"Export Round1 data";
GD_R1_LandingPage;"Round 1";
R1_LandingPage_Title;"Round 1";
Logins_Table_InfoTitle_R1;"Round 1 - access";
GD_SIM_R1_Start;"Start Simulation";
SIM_R1_FAC_dashboard_Header;"Round 1 Dashboard";
SIM_R1_FAC_dashboard_control;"Control";
SIM_R1_FAC_dashboard_teams;"Teams";
SIM_R1_FAC_dashboard_roleplay;"Roleplay";
SIM_R1_FAC_dashboard_tab2;"Moments";
SIM_R1_FAC_dashboard_ranking;"Ranking";
SIM_R1_FAC_debrief_tab1;"Moment 1";
SIM_R1_FAC_debrief_tab2;"Moment 2";
SIM_R1_FAC_debrief_ranking;"Ranking";
SIM_R1_FAC_debrief_roleplay;"Roleplay Impact";
SIM_R1_Scenario1_Opt1_KPI1;"-1";
SIM_R1_Scenario1_Opt1_KPI2;"-1";
SIM_R1_Scenario1_Opt1_KPI3;"0";
SIM_R1_Scenario1_Opt1_KPI4;"0";
SIM_R1_Scenario1_Opt1_MTUs_1;"-1";
SIM_R1_Scenario1_Opt1_MTUs_2;"0";
SIM_R1_Scenario1_Opt1_MTUs_3;"0";
SIM_R1_Scenario1_Opt1_MTUs_4;"0";
SIM_R1_Scenario1_Opt1_MTUs_5;"0";
SIM_R1_Scenario1_Opt1_MTUs_6;"0";
SIM_R1_Scenario1_Opt1_MTUs_7;"0";
SIM_R1_Scenario1_Opt1_MTUs_8;"0";
SIM_R1_Scenario1_Opt2_KPI1;"-1";
SIM_R1_Scenario1_Opt2_KPI2;"0";
SIM_R1_Scenario1_Opt2_KPI3;"1";
SIM_R1_Scenario1_Opt2_KPI4;"0";
SIM_R1_Scenario1_Opt2_MTUs_1;"-1";
SIM_R1_Scenario1_Opt2_MTUs_2;"0";
SIM_R1_Scenario1_Opt2_MTUs_3;"0";
SIM_R1_Scenario1_Opt2_MTUs_4;"0";
SIM_R1_Scenario1_Opt2_MTUs_5;"0";
SIM_R1_Scenario1_Opt2_MTUs_6;"0";
SIM_R1_Scenario1_Opt2_MTUs_7;"0";
SIM_R1_Scenario1_Opt2_MTUs_8;"0";
SIM_R1_Scenario1_Opt3_KPI1;"0";
SIM_R1_Scenario1_Opt3_KPI2;"2";
SIM_R1_Scenario1_Opt3_KPI3;"1";
SIM_R1_Scenario1_Opt3_KPI4;"1";
SIM_R1_Scenario1_Opt3_MTUs_1;"-2";
SIM_R1_Scenario1_Opt3_MTUs_2;"0";
SIM_R1_Scenario1_Opt3_MTUs_3;"0";
SIM_R1_Scenario1_Opt3_MTUs_4;"0";
SIM_R1_Scenario1_Opt3_MTUs_5;"0";
SIM_R1_Scenario1_Opt3_MTUs_6;"0";
SIM_R1_Scenario1_Opt3_MTUs_7;"0";
SIM_R1_Scenario1_Opt3_MTUs_8;"0";
SIM_R1_Scenario1_Opt4_KPI1;"-1";
SIM_R1_Scenario1_Opt4_KPI2;"2";
SIM_R1_Scenario1_Opt4_KPI3;"0";
SIM_R1_Scenario1_Opt4_KPI4;"0";
SIM_R1_Scenario1_Opt4_MTUs_1;"0";
SIM_R1_Scenario1_Opt4_MTUs_2;"0";
SIM_R1_Scenario1_Opt4_MTUs_3;"-3";
SIM_R1_Scenario1_Opt4_MTUs_4;"0";
SIM_R1_Scenario1_Opt4_MTUs_5;"0";
SIM_R1_Scenario1_Opt4_MTUs_6;"0";
SIM_R1_Scenario1_Opt4_MTUs_7;"0";
SIM_R1_Scenario1_Opt4_MTUs_8;"0";
GD_SIM_R1_Finish;"Finish Round 1";
GD_SIM_R1_Finish_Modal;"<span class=""highlight"">Are you sure you want to finish the round 1 for all participants?</span><br><br>Please, make sure all participants have already finished this round.";
SIM_R1_Finish_Header;"End of round 1";
SIM_R1_Finish_Title;"Congratulations!";
SIM_R1_Finish_Text;"You finished the first round of the simulation. We hope you enjoyed the experience and had interesting conversations with your team. We will debrief the different challenges as a group during the workshop. The facilitator will come soon with next steps and further instructions.";
SIM_R1_Finish_Img;"/images/scenarios/sim_round_finish.jpg";
SIM_R1_Summary_Header;"Simulation Summary";
SIM_R1_Summary_Instructions;"Here you have the summary of how your performance has been in each of the KPIs:";
SIM_R1_FAC_debrief_Header;"Round 1 Debrief";
SIM_R1_Scenario1_Header;"Moment 1";
SIM_R1_Scenario1_Title;"Pacing Omar’s Energy";
SIM_R1_Scenario1_Text;"Omar has truly proven himself with his great performance in the last three projects, but he is now eager to jump into a new challenge and is hoping to get a leadership role in an upcoming project. You believe in Omar capability to grow into such role, but you acknowledge that he still is fairly new to Mubadira and it will take more time for him to be ready. However, his ambition and enthusiasm is something you appreciate and believe will make him a start performer. Omar has noticed that despite his constant hand-raising to take lead of projects, and to touch more high-risk deliverables – he still doesn’t get resourced to drive them. This starts to weigh on him and he shares that he would like to raise an important issue in the next 1:1 meeting that you have scheduled in a weeks’ time. You have observed that this has impacted his energy and performance, especially when he is once again assigned supporting roles in projects. You know what to expect when the time for your meeting arrives, so how do you decide to approach it?";
SIM_R1_Scenario1_Img;"/images/client/R1scenario1.jpg";
SIM_R1_Scenario1_Question;"How do you decide to approach it?";
SIM_R1_Scenario1_Opt1;"Slow and steady wins the race";
SIM_R1_Scenario1_Des1;"In the meeting, you praise Omar’s enthusiasm and quality of work. You let him know that careers often take a while to progress and mature, because there are so many things that one needs to learn. You remind him of the brilliant work that he’s already done, and mention that constantly seeking out feedback and raising his hand to be a part of multiple and different pieces of work are the exact behaviours that will help progress his career. You acknowledge his potential and hint at future leadership roles, suggesting that these early stages are valuable for his long-term career narrative.<br><br><ul class=""circle-list""><li>-1 MTU from you</li></ul>";
SIM_R1_Scenario1_Opt2;"Show vulnerability";
SIM_R1_Scenario1_Des2;"You decide to use this meeting to get closer to Omar by sharing your personal career story. You believe this will inspire him and let him know that things aren’t always what they seem. You share some of your failures, and hardships, as well as your current state of mind. At the end of the meeting, you let Omar know that he’s doing great, and that his career path looks bright – soon enough he’ll move into leadership roles.<br><br><ul class=""circle-list""><li>-1 MTU from you</li></ul>";
SIM_R1_Scenario1_Opt3;"Hear him out";
SIM_R1_Scenario1_Des3;"You remember a time in your career when you felt a similar way and Omar’s eagerness to progress reminds you of yourself at the start of your professional journey. You believe Omar needs to feel heard so you decide in the meeting to simply listen and create a safe space where he can share his frustrations. You listen attentively, you take notes, encourage him here and there, and even share a few laughs. After patiently listening to Omar’s frustrations, you decide to inspire him by sharing your personal career story and how you understand how he is feeling. You admit that this is a process that will take time, but share your honest perspective that you believe he has what it takes to reach that goal and is on the right track.<br><br><ul class=""circle-list""><li>-2 MTUs from you</li></ul>";
SIM_R1_Scenario1_Opt4;"Fast-Track Ambition";
SIM_R1_Scenario1_Des4;"You decide to use the meeting with Omar to offer him a leadership role on a smaller project. Offering Omar a leadership role on a smaller project is a calculated risk to observe how he handles increased responsibility. You're prepared to support him through this new challenge, believing that sometimes the best growth happens when thrown in the deep end.<br><br><ul class=""circle-list""><li>-3 MTUs from Omar</li></ul>";
SIM_R1_Scenario1_FB_Header;"Moment 1: Consequences";
SIM_R1_Scenario1_FB_Solution;"C";
SIM_R1_Scenario1_FB_Opt1_Text;"Omar appreciates the praise and recognition of his work, feeling reassured about his future at Mubadira. However, the emphasis on patience and gradual progression leaves him slightly disheartened. Despite your best efforts, the main message he walks away with is “You are still not ready”. Omar’s feeling of not being given chances to grow lingers and he ends up leaving your team for the first leadership offer he receives.<br><br><ul class=""circle-list""><li>-1 Engaging Others</li><li>-1 Financial Results</li></ul>";
SIM_R1_Scenario1_FB_Opt2_Text;"Omar is inspired by your vulnerability and story, but he doesn’t fully relate it to his situation. He still feels that his unique perspective and ambitions aren't being acknowledged. While he admires your journey, he leaves the meeting feeling that his own path might require different steps, and that his immediate concerns haven't been addressed. Omar’s feeling of not being given chances to grow lingers and he ends up leaving your team for the first leadership offer he receives.<br><br><ul class=""circle-list""><li>+1 Building Relationships</li><li>-1 Financial Results</li></ul>";
SIM_R1_Scenario1_FB_Opt3_Text;"<span class=""highlight""><i><u>This option aligns well with your goals and delivers the strongest outcomes.</u></i></span><br><br><span class=""highlight"">Taking the time to listen to Omar and connecting with him helped you see things from Omar’s perspective. Your approach showed true empathy which allowed Omar to feel safe in sharing his thoughts. You come to understand that Omar feels he is not given the chance to prove his leadership skills, nor is he given a pathway on what it would take from him to get there. This leads him to think that the company is not acknowledging him and is stifling his growth. After listening to Omar, you agree to creating a leadership development plan for Omar that will address some of his gaps to be ready for a leadership role, as well as providing visibility on possible future projects he can lead. This conversation strengthens his trust in your leadership and his place in the company.</span><br><br><ul class=""circle-list""><li>+2 Engaging Others</li><li>+1 Building Relationships</li><li>+1 Effective Communication</li></ul>";
SIM_R1_Scenario1_FB_Opt4_Text;"Omar is initially thrilled with the opportunity, but it soon becomes clear that the leap was premature. He struggles to balance the demands of leadership with his existing responsibilities, leading to stress and a few missteps. While the intention was to accelerate his growth, the experience ends up being a bit too overwhelming, serving as a stark reminder of the importance of timing and gradual progression in career development. Luckily, giving him a smaller project helped ensure the team’s results aren’t highly impacted. It also helped you understand exactly where Omar’s gaps are in order to address them.<br><br><ul class=""circle-list""><li>+2 Engaging Others</li><li>-1 Financial Results</li></ul>";
SIM_R1_Scenario2_Header;"Moment 2";
SIM_R1_Scenario2_Title;"Roleplay Context";
SIM_R1_Scenario2_Text;"You recently co-created a development plan with Omar following a 1:1 where he voiced frustration about being passed over for leadership roles. As part of that plan, you gave him <b>a scoped leadership moment</b>: presenting risk insights to a key <b>external stakeholder</b> in a strategic partnership review.<br><br>Omar saw this as a milestone and a way to show he’s ready. While he brought energy and initiative, he <b>went off-script</b>, adding unvetted risk points and skipping the anchor slide. This <b>weakened message clarity</b>, caused confusion, and <b>undermined alignment</b> with the team’s framing.<br><br><b>As a team</b>, decide how you will <b>approach giving Omar feedback</b>. Choose one person from the team to lead the conversation with Omar and make sure you speak with the bot in a quiet environment.";
SIM_R1_Scenario2_Img;"/images/client/R1scenario2.jpg";
SIM_R1_Scenario2_Link;"Please access the roleplay through the following link: <a href=""https://alpha.wonderway.io/share/mCuYBBoQbx1D8EVCKQ95"" target=""_blank""><u>https://alpha.wonderway.io/share/mCuYBBoQbx1D8EVCKQ95</u></a>.";
SIM_R1_Scenario2_Password;"When asked for the password, please enter the following: <b><i>brave-magenta-trout</i></b>";
SIM_Roleplay_R1_table_Title;"Roleplay Score";
SIM_Roleplay_R1_Submit_Success;"Participant score updated.";
GroupDirector_ExportBtn_R1_Part1;"Export Data Round 1 - Part 1";
GroupDirector_ExportBtn_R1_Part2;"Export Data Round 1 - Part 2";
GD_DebriefPage_R1_Part1;"Debrief Round 1 - Part 1";
GD_DebriefPage_R1_Part2;"Debrief Round 1 - Part 2";
GD_SIM_R1_Start_Part1;"Start Round 1 - Part 1";
GD_SIM_R1_Start_Part2;"Start Round 1 - Part 2";
SIM_R1_FAC_dashboard_Header_Part1;"Dashboard Round 1 - Part 1";
SIM_R1_FAC_dashboard_Header_Part2;"Dashboard Round 1 - Part 2";
GD_SIM_R1_Finish_Part1;"Finish Round 1 - Part 1";
GD_SIM_R1_Finish_Part2;"Finish Round 1 - Part 2";
GD_SIM_R1_Finish_Modal_Part1;"<span class=""highlight"">Are you sure you want to finish the part 1 of round 1 for all participants?</span>";
GD_SIM_R1_Finish_Modal_Part2;"<span class=""highlight"">Are you sure you want to finish the part 1 of round 1 for all participants?</span>";
SIM_R1_Finish_Text_Part1;"You finished the first part of the first round of the simulation. We hope you enjoyed the experience and had interesting conversations with your team. We will debrief the different challenges as a group during the workshop. The facilitator will come soon with next steps and further instructions.";
SIM_R1_Finish_Text_Part2;"You finished the second part of the first round of the simulation. We hope you enjoyed the experience and had interesting conversations with your team. We will debrief the different challenges as a group during the workshop. The facilitator will come soon with next steps and further instructions.";
SIM_R1_FAC_debrief_Header_Part1;"Round 1 Debrief - Part 1";
SIM_R1_FAC_debrief_Header_Part2;"Round 1 Debrief - Part 2";
Header_LinkLogins_R2;"Accesses Round2";
Landing_Subtitle_R2;"Welcome - Round 2";
GroupDirector_ExportBtn_R2;"Export Round2 data";
GD_R2_LandingPage;"Round 2";
R2_LandingPage_Title;"Round 2";
Logins_Table_InfoTitle_R2;"Round 2 - access";
GD_SIM_R2_Start;"Start Simulation";
SIM_R2_FAC_dashboard_Header;"Round 2 Dashboard";
SIM_R2_FAC_dashboard_control;"Control";
SIM_R2_FAC_dashboard_teams;"Teams";
SIM_R2_FAC_dashboard_roleplay;"Roleplay";
SIM_R2_FAC_dashboard_tab1;"Initiatives";
SIM_R2_FAC_dashboard_tab2;"Moments";
SIM_R2_FAC_dashboard_ranking;"Ranking";
SIM_R2_FAC_debrief_tab1;"Moment 3";
SIM_R2_FAC_debrief_tab2;"Moment 4";
SIM_R2_FAC_debrief_ranking;"Ranking";
SIM_R2_FAC_debrief_roleplay;"Roleplay Impact";
SIM_R2_Scenario1_Opt1_KPI1;"0";
SIM_R2_Scenario1_Opt1_KPI2;"1";
SIM_R2_Scenario1_Opt1_KPI3;"0";
SIM_R2_Scenario1_Opt1_KPI4;"2";
SIM_R2_Scenario1_Opt1_MTUs_1;"-2";
SIM_R2_Scenario1_Opt1_MTUs_2;"0";
SIM_R2_Scenario1_Opt1_MTUs_3;"0";
SIM_R2_Scenario1_Opt1_MTUs_4;"0";
SIM_R2_Scenario1_Opt1_MTUs_5;"0";
SIM_R2_Scenario1_Opt1_MTUs_6;"0";
SIM_R2_Scenario1_Opt1_MTUs_7;"0";
SIM_R2_Scenario1_Opt1_MTUs_8;"0";
SIM_R2_Scenario1_Opt2_KPI1;"0";
SIM_R2_Scenario1_Opt2_KPI2;"2";
SIM_R2_Scenario1_Opt2_KPI3;"1";
SIM_R2_Scenario1_Opt2_KPI4;"1";
SIM_R2_Scenario1_Opt2_MTUs_1;"-1";
SIM_R2_Scenario1_Opt2_MTUs_2;"0";
SIM_R2_Scenario1_Opt2_MTUs_3;"0";
SIM_R2_Scenario1_Opt2_MTUs_4;"0";
SIM_R2_Scenario1_Opt2_MTUs_5;"0";
SIM_R2_Scenario1_Opt2_MTUs_6;"0";
SIM_R2_Scenario1_Opt2_MTUs_7;"0";
SIM_R2_Scenario1_Opt2_MTUs_8;"0";
SIM_R2_Scenario1_Opt3_KPI1;"0";
SIM_R2_Scenario1_Opt3_KPI2;"0";
SIM_R2_Scenario1_Opt3_KPI3;"-1";
SIM_R2_Scenario1_Opt3_KPI4;"-1";
SIM_R2_Scenario1_Opt3_MTUs_1;"-1";
SIM_R2_Scenario1_Opt3_MTUs_2;"0";
SIM_R2_Scenario1_Opt3_MTUs_3;"0";
SIM_R2_Scenario1_Opt3_MTUs_4;"0";
SIM_R2_Scenario1_Opt3_MTUs_5;"0";
SIM_R2_Scenario1_Opt3_MTUs_6;"0";
SIM_R2_Scenario1_Opt3_MTUs_7;"0";
SIM_R2_Scenario1_Opt3_MTUs_8;"0";
SIM_R2_Scenario1_Opt4_KPI1;"0";
SIM_R2_Scenario1_Opt4_KPI2;"0";
SIM_R2_Scenario1_Opt4_KPI3;"-1";
SIM_R2_Scenario1_Opt4_KPI4;"-2";
SIM_R2_Scenario1_Opt4_MTUs_1;"0";
SIM_R2_Scenario1_Opt4_MTUs_2;"0";
SIM_R2_Scenario1_Opt4_MTUs_3;"0";
SIM_R2_Scenario1_Opt4_MTUs_4;"0";
SIM_R2_Scenario1_Opt4_MTUs_5;"0";
SIM_R2_Scenario1_Opt4_MTUs_6;"0";
SIM_R2_Scenario1_Opt4_MTUs_7;"0";
SIM_R2_Scenario1_Opt4_MTUs_8;"0";
GD_SIM_R2_Finish;"Finish Round 2";
GD_SIM_R2_Finish_Modal;"<span class=""highlight"">Are you sure you want to finish the round 2 for all participants?</span><br><br>Please, make sure all participants have already finished this round.";
SIM_R2_Finish_Header;"End of round 2";
SIM_R2_Finish_Title;"Congratulations!";
SIM_R2_Finish_Text;"You finished the second round of the simulation. We hope you enjoyed the experience and had interesting conversations with your team. We will debrief the different challenges as a group during the workshop. The facilitator will come soon with next steps and further instructions.";
SIM_R2_Finish_Img;"/images/scenarios/sim_round_finish.jpg";
SIM_R2_Summary_Header;"Simulation Summary";
SIM_R2_Summary_Instructions;"Here you have the summary of how your performance has been in each of the KPIs:";
SIM_R2_FAC_debrief_Header;"Round 2 Debrief";
SIM_R2_Scenario1_Header;"Moment 3";
SIM_R2_Scenario1_Title;"Lost in Translation";
SIM_R2_Scenario1_Text;"Leila has always been one of your most dependable team members — thoughtful, detail-oriented, and client-savvy. She communicates with precision and brings a calm, methodical energy to the team. However, you’ve started to notice a subtle disconnect in your one-on-ones with her.<br><br>In recent meetings, when you’ve spoken in broad strokes about strategy or upcoming projects, Leila has nodded politely but contributed little. After one meeting, she sent a follow-up email asking for clarification on the key outcomes you mentioned. Another time, she re-asked a question you felt had already been addressed. These moments have left you wondering: Is she disengaged, confused, or just cautious?<br><br>This week, during a virtual touchpoint, she mentioned:<br><br><i>“I sometimes struggle to follow where things are headed. I like knowing exactly what’s expected and how I can contribute, but lately it’s been a bit unclear.”</i><br><br>You recognize that Leila’s style might be different from yours. You prefer moving quickly and brainstorming in real-time — often thinking out loud. She prefers clarity, structure, and time to process. This is a moment to build trust and flex your communication style to connect more effectively. What do you do?";
SIM_R2_Scenario1_Img;"/images/client/R2scenario1.jpg";
SIM_R2_Scenario1_Question;"What do you do?";
SIM_R2_Scenario1_Opt1;"Clarify Through Structure";
SIM_R2_Scenario1_Des1;"You decide to shift your style. In your next meeting with Leila, you present a simple structure: project priorities, timelines, and where you see her role. You pause between each point and ask her to reflect back what she heard. You also ask what additional clarity she needs. You follow up with a short written summary to reinforce key points.<br><br><ul class=""circle-list""><li>-2 MTUs from you</li></ul>";
SIM_R2_Scenario1_Opt2;"Invite Her to Lead the Conversation";
SIM_R2_Scenario1_Des2;"At your next meeting, you open with: <i>“What’s the best way for us to communicate going forward?”</i><br>You share your reflections about how your styles may differ and ask her to help shape the structure of your updates. You invite her to build the meeting agenda, take the lead on action items, and define what “clarity” looks like for her.<br><br><ul class=""circle-list""><li>-1 MTU from you</li></ul>";
SIM_R2_Scenario1_Opt3;"Push for Adaptability";
SIM_R2_Scenario1_Des3;"You acknowledge that the environment at Mubadira is fast-paced and evolving. You tell Leila that while clarity is important, not every meeting will come with a perfect structure. You encourage her to become more comfortable with ambiguity and challenge her to speak up more when things feel unclear. You offer to check in more often — but with fewer details.<br><br><ul class=""circle-list""><li>-1 MTU from you</li></ul>";
SIM_R2_Scenario1_Opt4;"Let It Ride";
SIM_R2_Scenario1_Des4;"You decide that this isn’t a big issue. You continue your updates as usual, confident that Leila will ask questions when needed. You focus on your leadership priorities and give her space to figure things out independently.<br><br><ul class=""circle-list""><li>No Time Used</li></ul>";
SIM_R2_Scenario1_FB_Header;"Moment 3: Consequences";
SIM_R2_Scenario1_FB_Solution;"B";
SIM_R2_Scenario1_FB_Opt1_Text;"This took more time, but Leila appreciates your effort to meet her where she is. The meeting feels more collaborative, and her follow-up questions decrease significantly. Your written recap helps her work more independently, and she starts proactively bringing updates. While it takes a bit more upfront effort, the clarity improves delivery and mutual trust.<br><br><ul class=""circle-list""><li>+2 Effective Communication</li><li>+1 Engaging Others</li></ul>";
SIM_R2_Scenario1_FB_Opt2_Text;"<span class=""highlight""><i><u>This option aligns well with your goals and delivers the strongest outcomes.</u></i></span><br><br><span class=""highlight"">Leila feels empowered by your invitation and surprised by your vulnerability in naming the style mismatch. She proposes a shared checklist to anchor weekly updates and volunteers to draft team action plans. This sparks stronger engagement and creates a more balanced dynamic in your relationship.</span><br><br><ul class=""circle-list""><li>+1 Building Relationships</li><li>+2 Engaging Others</li><li>+1 Effective Communication</li></ul>";
SIM_R2_Scenario1_FB_Opt3_Text;"Your intention to grow Leila's resilience is clear, but the delivery falls flat. She agrees to try, but in future meetings she seems more reserved and hesitant. A week later, she asks to delay a key deliverable — something that’s never happened before. The message she received: adapt or fall behind.<br><br><ul class=""circle-list""><li>-1 Engaging Others</li><li>-1 Building Relationships</li></ul>";
SIM_R2_Scenario1_FB_Opt4_Text;"Leila remains quiet and compliant, but slowly disengages. She does her job, but no longer volunteers insights or stretch work. You eventually learn through another teammate that she’s looking for a transfer to a different team where “communication feels easier.”<br><br><ul class=""circle-list""><li>-2 Engaging Others</li><li>-1 Building Relationships</li></ul>";
SIM_R2_Scenario2_Header;"Moment 4";
SIM_R2_Scenario2_Title;"Roleplay Context";
SIM_R2_Scenario2_Text;"You recently adjusted your communication style to better support Leila. You introduced more structure to your updates, clarified expectations, and followed up in writing. These changes helped reduce her follow-up questions and seemed to address some of the confusion.<br><br>Since then, Leila has remained professional and steady, but you’ve noticed her engagement hasn’t fully returned. She’s contributing less in discussions, and she hasn’t expressed any goals or direction during performance planning.<br><br>Today’s conversation is part of the goal-setting process. It’s also an opportunity to check in more deeply on her development. You believe there may be something she hasn’t shared yet, and you want to understand what support she might need to move forward with more clarity and motivation.<br><br><b>As a team</b>, decide how you will <b>approach the career conversation with Leila</b>. Make sure to review the Mubadala Career development Framework for guidance. Choose one person from the team to lead the conversation with Leila and make sure you speak with the bot in a quiet environment.";
SIM_R2_Scenario2_Img;"/images/client/R2scenario2.jpg";
SIM_R2_Scenario2_Link;"Please access the roleplay through the following link: <a href=""https://alpha.wonderway.io/share/hUXSTWEWMQojuXATImpE"" target=""_blank""><u>https://alpha.wonderway.io/share/hUXSTWEWMQojuXATImpE</u></a>.";
SIM_R2_Scenario2_Password;"When asked for the password, please enter the following: <b><i>vital-amethyst-stoat</i></b>";
SIM_Roleplay_R2_table_Title;"Roleplay Score";
SIM_Roleplay_R2_Submit_Success;"Participant score updated.";
GroupDirector_ExportBtn_R2_Part1;"Export Data Round 2 - Part 1";
GroupDirector_ExportBtn_R2_Part2;"Export Data Round 2 - Part 2";
GD_DebriefPage_R2_Part1;"Debrief Round 2 - Part 1";
GD_DebriefPage_R2_Part2;"Debrief Round 2 - Part 2";
GD_SIM_R2_Start_Part1;"Start Round 2 - Part 1";
GD_SIM_R2_Start_Part2;"Start Round 2 - Part 2";
SIM_R2_FAC_dashboard_Header_Part1;"Dashboard Round 2 - Part 1";
SIM_R2_FAC_dashboard_Header_Part2;"Dashboard Round 2 - Part 2";
GD_SIM_R2_Finish_Part1;"Finish Round 2 - Part 1";
GD_SIM_R2_Finish_Part2;"Finish Round 2 - Part 2";
GD_SIM_R2_Finish_Modal_Part1;"<span class=""highlight"">Are you sure you want to finish the part 1 of round 2 for all participants?</span>";
GD_SIM_R2_Finish_Modal_Part2;"<span class=""highlight"">Are you sure you want to finish the part 1 of round 2 for all participants?</span>";
SIM_R2_Finish_Text_Part1;"You finished the first part of the second round of the simulation. We hope you enjoyed the experience and had interesting conversations with your team. We will debrief the different challenges as a group during the workshop. The facilitator will come soon with next steps and further instructions.";
SIM_R2_Finish_Text_Part2;"You finished the second part of the second round of the simulation. We hope you enjoyed the experience and had interesting conversations with your team. We will debrief the different challenges as a group during the workshop. The facilitator will come soon with next steps and further instructions.";
SIM_R2_FAC_debrief_Header_Part1;"Round 2 Debrief - Part 1";
SIM_R2_FAC_debrief_Header_Part2;"Round 2 Debrief - Part 2";
Header_LinkLogins_R3;"Accesses Round3";
Landing_Subtitle_R3;"Welcome - Round 3";
GroupDirector_ExportBtn_R3;"Export Round3 data";
GD_R3_LandingPage;"Round 3";
R3_LandingPage_Title;"Round 3";
Logins_Table_InfoTitle_R3;"Round 3 - access";
GD_SIM_R3_Start;"Start Simulation";
SIM_R3_FAC_dashboard_Header;"Round 3 Dashboard";
SIM_R3_FAC_dashboard_control;"Control";
SIM_R3_FAC_dashboard_teams;"Teams";
SIM_R3_FAC_dashboard_roleplay;"Roleplay";
SIM_R3_FAC_dashboard_tab1;"Initiatives";
SIM_R3_FAC_dashboard_tab2;"Moments";
SIM_R3_FAC_dashboard_ranking;"Ranking";
SIM_R3_FAC_debrief_tab1;"Moment 1";
SIM_R3_FAC_debrief_tab2;"Moment 2";
SIM_R3_FAC_debrief_ranking;"Ranking";
SIM_R3_FAC_debrief_roleplay;"Roleplay Impact";
SIM_R3_Scenario1_Opt1_KPI1;"0";
SIM_R3_Scenario1_Opt1_KPI2;"1";
SIM_R3_Scenario1_Opt1_KPI3;"0";
SIM_R3_Scenario1_Opt1_KPI4;"2";
SIM_R3_Scenario1_Opt1_MTUs_1;"-2";
SIM_R3_Scenario1_Opt1_MTUs_2;"0";
SIM_R3_Scenario1_Opt1_MTUs_3;"0";
SIM_R3_Scenario1_Opt1_MTUs_4;"0";
SIM_R3_Scenario1_Opt1_MTUs_5;"0";
SIM_R3_Scenario1_Opt1_MTUs_6;"0";
SIM_R3_Scenario1_Opt1_MTUs_7;"0";
SIM_R3_Scenario1_Opt1_MTUs_8;"0";
SIM_R3_Scenario1_Opt2_KPI1;"0";
SIM_R3_Scenario1_Opt2_KPI2;"2";
SIM_R3_Scenario1_Opt2_KPI3;"1";
SIM_R3_Scenario1_Opt2_KPI4;"1";
SIM_R3_Scenario1_Opt2_MTUs_1;"-1";
SIM_R3_Scenario1_Opt2_MTUs_2;"0";
SIM_R3_Scenario1_Opt2_MTUs_3;"0";
SIM_R3_Scenario1_Opt2_MTUs_4;"0";
SIM_R3_Scenario1_Opt2_MTUs_5;"0";
SIM_R3_Scenario1_Opt2_MTUs_6;"0";
SIM_R3_Scenario1_Opt2_MTUs_7;"0";
SIM_R3_Scenario1_Opt2_MTUs_8;"0";
SIM_R3_Scenario1_Opt3_KPI1;"0";
SIM_R3_Scenario1_Opt3_KPI2;"0";
SIM_R3_Scenario1_Opt3_KPI3;"-1";
SIM_R3_Scenario1_Opt3_KPI4;"-1";
SIM_R3_Scenario1_Opt3_MTUs_1;"-1";
SIM_R3_Scenario1_Opt3_MTUs_2;"0";
SIM_R3_Scenario1_Opt3_MTUs_3;"0";
SIM_R3_Scenario1_Opt3_MTUs_4;"0";
SIM_R3_Scenario1_Opt3_MTUs_5;"0";
SIM_R3_Scenario1_Opt3_MTUs_6;"0";
SIM_R3_Scenario1_Opt3_MTUs_7;"0";
SIM_R3_Scenario1_Opt3_MTUs_8;"0";
SIM_R3_Scenario1_Opt4_KPI1;"0";
SIM_R3_Scenario1_Opt4_KPI2;"0";
SIM_R3_Scenario1_Opt4_KPI3;"-1";
SIM_R3_Scenario1_Opt4_KPI4;"-2";
SIM_R3_Scenario1_Opt4_MTUs_1;"0";
SIM_R3_Scenario1_Opt4_MTUs_2;"0";
SIM_R3_Scenario1_Opt4_MTUs_3;"0";
SIM_R3_Scenario1_Opt4_MTUs_4;"0";
SIM_R3_Scenario1_Opt4_MTUs_5;"0";
SIM_R3_Scenario1_Opt4_MTUs_6;"0";
SIM_R3_Scenario1_Opt4_MTUs_7;"0";
SIM_R3_Scenario1_Opt4_MTUs_8;"0";
GD_SIM_R3_Finish;"Finish Round 3";
GD_SIM_R3_Finish_Modal;"<span class=""highlight"">Are you sure you want to finish the round 3 for all participants?</span><br><br>Please, make sure all participants have already finished this round.";
SIM_R3_Finish_Header;"End of round 3";
SIM_R3_Finish_Title;"Congratulations!";
SIM_R3_Finish_Text;"You finished the second round of the simulation. We hope you enjoyed the experience and had interesting conversations with your team. We will debrief the different challenges as a group during the workshop. The facilitator will come soon with next steps and further instructions.";
SIM_R3_Finish_Img;"/images/scenarios/sim_round_finish.jpg";
SIM_R3_Summary_Header;"Simulation Summary";
SIM_R3_Summary_Instructions;"Here you have the summary of how your performance has been in each of the KPIs:";
SIM_R3_FAC_debrief_Header;"Round 3 Debrief";
SIM_R3_Scenario1_Header;"Moment 5";
SIM_R3_Scenario1_Title;"Hitting a Wall with Fatima";
SIM_R3_Scenario1_Text;"It’s mid-year. You’re reviewing performance inputs for your project team. One of your team members, Fatima Noor, started the year strong. Her deliverables were sharp, reliable, and required little oversight. You trusted her to handle complexity independently, and she was seen as a steady contributor.<br><br>But over the last few months, things have shifted.<br><br>Following a project restructuring in Q2, Fatima’s role was expanded to include more strategic deliverables and increased visibility. Since then, her work has started slipping. She’s missed key details in analysis, seems less confident in meetings, and has stepped back from visible ownership.<br><br>You’ve already addressed this twice: once after a missed deliverable, and again during your last formal check-in. You were clear about expectations — more proactive engagement, sharper execution, and a more strategic mindset. She acknowledged the feedback, but the patterns haven’t changed.<br><br>Then, during a peer touchpoint, Dima Farouk mentions something in passing:<br><br>“Hey, just a heads-up — we had to rework part of Fatima’s analysis last week. It wasn’t bad, but definitely not at her usual level. Is everything okay?”<br><br>You’re now at a decision point. You want to be fair to Fatima — and to the standards of your team. But you’re also starting to wonder whether something deeper might be going on. What do you do?";
SIM_R3_Scenario1_Img;"/images/client/R3scenario1.jpg";
SIM_R3_Scenario1_Question;"What do you do?";
SIM_R3_Scenario1_Opt1;"Clarify expectations again";
SIM_R3_Scenario1_Des1;"You meet with Fatima once more to go over her role expectations. You walk her through the delivery standards, reinforce expectations around ownership and pace, and explain how things have evolved since the scope expanded. You hope that with a fresh reminder, you give her 3 months to course-correct before the year ends. It feels like the last gentle nudge before more formal steps.<br><br><ul class=""circle-list""><li>-1 MTUs from you</li></ul>";
SIM_R3_Scenario1_Opt2;"Deliver stronger feedback";
SIM_R3_Scenario1_Des2;"You decide it’s time to be more direct. You hold a feedback conversation focused on her recent gaps, missed deadlines, reduced initiative, and lower quality. You name the delta between her past performance and the current standard, and make it clear that this needs to shift. You hope the candor will trigger urgency and reflection.<br><br><ul class=""circle-list""><li>-1 MTU from you</li></ul>";
SIM_R3_Scenario1_Opt3;"Provide targeted support ";
SIM_R3_Scenario1_Des3;"You recognize that the shift in Fatima’s performance may be less about intent and more about enablement. You schedule a conversation to unpack what’s going on: Is it confidence? Technical depth? Strategic context? Based on that, you assign a senior colleague to mentor her — someone who can model quality, challenge her thinking, and give her space to grow.<br><br><ul class=""circle-list""><li>-1 MTU from you</li><li>-1 Time Unit from Team Member</li></ul>";
SIM_R3_Scenario1_Opt4;"Conclude she’s not a fit";
SIM_R3_Scenario1_Des4;"You begin to question whether Fatima is suited for this expanded scope. The matrix structure and rising complexity seem misaligned with her style. Quietly, you start documenting performance gaps and withdraw expectations. You stop reinforcing feedback and mentally shift toward a future without her on the team.<br><br><ul class=""circle-list""><li>No Time Used (but leadership perception is affected)</li></ul>";
SIM_R3_Scenario1_FB_Header;"Moment 5: Consequences";
SIM_R3_Scenario1_FB_Solution;"C";
SIM_R3_Scenario1_FB_Opt1_Text;"This took more time, but Leila appreciates your effort to meet her where she is. The meeting feels more collaborative, and her follow-up questions decrease significantly. Your written recap helps her work more independently, and she starts proactively bringing updates. While it takes a bit more upfront effort, the clarity improves delivery and mutual trust.<br><br><ul class=""circle-list""><li>+2 Effective Communication</li><li>+1 Engaging Others</li></ul>";
SIM_R3_Scenario1_FB_Opt2_Text;"<span class=""highlight""><i><u>This option aligns well with your goals and delivers the strongest outcomes.</u></i></span><br><br><span class=""highlight"">Leila feels empowered by your invitation and surprised by your vulnerability in naming the style mismatch. She proposes a shared checklist to anchor weekly updates and volunteers to draft team action plans. This sparks stronger engagement and creates a more balanced dynamic in your relationship.</span><br><br><ul class=""circle-list""><li>+1 Building Relationships</li><li>+2 Engaging Others</li><li>+1 Effective Communication</li></ul>";
SIM_R3_Scenario1_FB_Opt3_Text;"Your intention to grow Leila's resilience is clear, but the delivery falls flat. She agrees to try, but in future meetings she seems more reserved and hesitant. A week later, she asks to delay a key deliverable — something that’s never happened before. The message she received: adapt or fall behind.<br><br><ul class=""circle-list""><li>-1 Engaging Others</li><li>-1 Building Relationships</li></ul>";
SIM_R3_Scenario1_FB_Opt4_Text;"Leila remains quiet and compliant, but slowly disengages. She does her job, but no longer volunteers insights or stretch work. You eventually learn through another teammate that she’s looking for a transfer to a different team where “communication feels easier.”<br><br><ul class=""circle-list""><li>-2 Engaging Others</li><li>-1 Building Relationships</li></ul>";
SIM_R3_Scenario2_Header;"Moment 6";
SIM_R3_Scenario2_Title;"Roleplay Context";
SIM_R3_Scenario2_Text;"You recently adjusted your communication style to better support Leila. You introduced more structure to your updates, clarified expectations, and followed up in writing. These changes helped reduce her follow-up questions and seemed to address some of the confusion.<br><br>Since then, Leila has remained professional and steady, but you’ve noticed her engagement hasn’t fully returned. She’s contributing less in discussions, and she hasn’t expressed any goals or direction during performance planning.<br><br>Today’s conversation is part of the goal-setting process. It’s also an opportunity to check in more deeply on her development. You believe there may be something she hasn’t shared yet, and you want to understand what support she might need to move forward with more clarity and motivation.<br><br><b>As a team</b>, decide how you will <b>approach the career conversation with Leila</b>. Make sure to review the Mubadala Career development Framework for guidance. Choose one person from the team to lead the conversation with Leila and make sure you speak with the bot in a quiet environment.";
SIM_R3_Scenario2_Img;"/images/client/R3scenario2.jpg";
SIM_R3_Scenario2_Link;"Please access the roleplay through the following link: <a href=""https://alpha.wonderway.io/share/hUXSTWEWMQojuXATImpE"" target=""_blank""><u>https://alpha.wonderway.io/share/hUXSTWEWMQojuXATImpE</u></a>.";
SIM_R3_Scenario2_Password;"When asked for the password, please enter the following: <b><i>vital-amethyst-stoat</i></b>";
SIM_Roleplay_R3_table_Title;"Roleplay Score";
SIM_Roleplay_R3_Submit_Success;"Participant score updated.";
Header_LinkLogins_R4;"Accesses Round4";
Landing_Subtitle_R4;"Welcome - Round 4";
GroupDirector_ExportBtn_R4;"Export Round4 data";
GD_R4_LandingPage;"Round 4";
R4_LandingPage_Title;"Round 4";
Logins_Table_InfoTitle_R4;"Round 4 - access";
GD_SIM_R4_Start;"Start Simulation";
SIM_R4_FAC_dashboard_Header;"Round 4 Dashboard";
SIM_R4_FAC_dashboard_control;"Control";
SIM_R4_FAC_dashboard_teams;"Teams";
SIM_R4_FAC_dashboard_roleplay;"Roleplay";
SIM_R4_FAC_dashboard_tab1;"Initiatives";
SIM_R4_FAC_dashboard_tab2;"Moments";
SIM_R4_FAC_dashboard_ranking;"Ranking";
SIM_R4_FAC_debrief_tab1;"Moment 3";
SIM_R4_FAC_debrief_tab2;"Moment 4";
SIM_R4_FAC_debrief_ranking;"Ranking";
SIM_R4_FAC_debrief_roleplay;"Roleplay Impact";
SIM_R4_Scenario1_Opt1_KPI1;"0";
SIM_R4_Scenario1_Opt1_KPI2;"1";
SIM_R4_Scenario1_Opt1_KPI3;"0";
SIM_R4_Scenario1_Opt1_KPI4;"2";
SIM_R4_Scenario1_Opt1_MTUs_1;"-2";
SIM_R4_Scenario1_Opt1_MTUs_2;"0";
SIM_R4_Scenario1_Opt1_MTUs_3;"0";
SIM_R4_Scenario1_Opt1_MTUs_4;"0";
SIM_R4_Scenario1_Opt1_MTUs_5;"0";
SIM_R4_Scenario1_Opt1_MTUs_6;"0";
SIM_R4_Scenario1_Opt1_MTUs_7;"0";
SIM_R4_Scenario1_Opt1_MTUs_8;"0";
SIM_R4_Scenario1_Opt2_KPI1;"0";
SIM_R4_Scenario1_Opt2_KPI2;"2";
SIM_R4_Scenario1_Opt2_KPI3;"1";
SIM_R4_Scenario1_Opt2_KPI4;"1";
SIM_R4_Scenario1_Opt2_MTUs_1;"-1";
SIM_R4_Scenario1_Opt2_MTUs_2;"0";
SIM_R4_Scenario1_Opt2_MTUs_3;"0";
SIM_R4_Scenario1_Opt2_MTUs_4;"0";
SIM_R4_Scenario1_Opt2_MTUs_5;"0";
SIM_R4_Scenario1_Opt2_MTUs_6;"0";
SIM_R4_Scenario1_Opt2_MTUs_7;"0";
SIM_R4_Scenario1_Opt2_MTUs_8;"0";
SIM_R4_Scenario1_Opt3_KPI1;"0";
SIM_R4_Scenario1_Opt3_KPI2;"0";
SIM_R4_Scenario1_Opt3_KPI3;"-1";
SIM_R4_Scenario1_Opt3_KPI4;"-1";
SIM_R4_Scenario1_Opt3_MTUs_1;"-1";
SIM_R4_Scenario1_Opt3_MTUs_2;"0";
SIM_R4_Scenario1_Opt3_MTUs_3;"0";
SIM_R4_Scenario1_Opt3_MTUs_4;"0";
SIM_R4_Scenario1_Opt3_MTUs_5;"0";
SIM_R4_Scenario1_Opt3_MTUs_6;"0";
SIM_R4_Scenario1_Opt3_MTUs_7;"0";
SIM_R4_Scenario1_Opt3_MTUs_8;"0";
SIM_R4_Scenario1_Opt4_KPI1;"0";
SIM_R4_Scenario1_Opt4_KPI2;"0";
SIM_R4_Scenario1_Opt4_KPI3;"-1";
SIM_R4_Scenario1_Opt4_KPI4;"-2";
SIM_R4_Scenario1_Opt4_MTUs_1;"0";
SIM_R4_Scenario1_Opt4_MTUs_2;"0";
SIM_R4_Scenario1_Opt4_MTUs_3;"0";
SIM_R4_Scenario1_Opt4_MTUs_4;"0";
SIM_R4_Scenario1_Opt4_MTUs_5;"0";
SIM_R4_Scenario1_Opt4_MTUs_6;"0";
SIM_R4_Scenario1_Opt4_MTUs_7;"0";
SIM_R4_Scenario1_Opt4_MTUs_8;"0";
GD_SIM_R4_Finish;"Finish Round 4";
GD_SIM_R4_Finish_Modal;"<span class=""highlight"">Are you sure you want to finish the round 4 for all participants?</span><br><br>Please, make sure all participants have already finished this round.";
SIM_R4_Finish_Header;"End of round 4";
SIM_R4_Finish_Title;"Congratulations!";
SIM_R4_Finish_Text;"You finished the second round of the simulation. We hope you enjoyed the experience and had interesting conversations with your team. We will debrief the different challenges as a group during the workshop. The facilitator will come soon with next steps and further instructions.";
SIM_R4_Finish_Img;"/images/scenarios/sim_round_finish.jpg";
SIM_R4_Summary_Header;"Simulation Summary";
SIM_R4_Summary_Instructions;"Here you have the summary of how your performance has been in each of the KPIs:";
SIM_R4_FAC_debrief_Header;"Round 4 Debrief";
SIM_R4_Scenario1_Header;"Moment 7";
SIM_R4_Scenario1_Title;"Lost in Translation";
SIM_R4_Scenario1_Text;"Leila has always been one of your most dependable team members — thoughtful, detail-oriented, and client-savvy. She communicates with precision and brings a calm, methodical energy to the team. However, you’ve started to notice a subtle disconnect in your one-on-ones with her.<br><br>In recent meetings, when you’ve spoken in broad strokes about strategy or upcoming projects, Leila has nodded politely but contributed little. After one meeting, she sent a follow-up email asking for clarification on the key outcomes you mentioned. Another time, she re-asked a question you felt had already been addressed. These moments have left you wondering: Is she disengaged, confused, or just cautious?<br><br>This week, during a virtual touchpoint, she mentioned:<br><br><i>“I sometimes struggle to follow where things are headed. I like knowing exactly what’s expected and how I can contribute, but lately it’s been a bit unclear.”</i><br><br>You recognize that Leila’s style might be different from yours. You prefer moving quickly and brainstorming in real-time — often thinking out loud. She prefers clarity, structure, and time to process. This is a moment to build trust and flex your communication style to connect more effectively. What do you do?";
SIM_R4_Scenario1_Img;"/images/client/R4scenario1.jpg";
SIM_R4_Scenario1_Question;"What do you do?";
SIM_R4_Scenario1_Opt1;"Clarify Through Structure";
SIM_R4_Scenario1_Des1;"You decide to shift your style. In your next meeting with Leila, you present a simple structure: project priorities, timelines, and where you see her role. You pause between each point and ask her to reflect back what she heard. You also ask what additional clarity she needs. You follow up with a short written summary to reinforce key points.<br><br><ul class=""circle-list""><li>-2 MTUs from you</li></ul>";
SIM_R4_Scenario1_Opt2;"Invite Her to Lead the Conversation";
SIM_R4_Scenario1_Des2;"At your next meeting, you open with: <i>“What’s the best way for us to communicate going forward?”</i><br>You share your reflections about how your styles may differ and ask her to help shape the structure of your updates. You invite her to build the meeting agenda, take the lead on action items, and define what “clarity” looks like for her.<br><br><ul class=""circle-list""><li>-1 MTU from you</li></ul>";
SIM_R4_Scenario1_Opt3;"Push for Adaptability";
SIM_R4_Scenario1_Des3;"You acknowledge that the environment at Mubadira is fast-paced and evolving. You tell Leila that while clarity is important, not every meeting will come with a perfect structure. You encourage her to become more comfortable with ambiguity and challenge her to speak up more when things feel unclear. You offer to check in more often — but with fewer details.<br><br><ul class=""circle-list""><li>-1 MTU from you</li></ul>";
SIM_R4_Scenario1_Opt4;"Let It Ride";
SIM_R4_Scenario1_Des4;"You decide that this isn’t a big issue. You continue your updates as usual, confident that Leila will ask questions when needed. You focus on your leadership priorities and give her space to figure things out independently.<br><br><ul class=""circle-list""><li>No Time Used</li></ul>";
SIM_R4_Scenario1_FB_Header;"Moment 7: Consequences";
SIM_R4_Scenario1_FB_Solution;"B";
SIM_R4_Scenario1_FB_Opt1_Text;"This took more time, but Leila appreciates your effort to meet her where she is. The meeting feels more collaborative, and her follow-up questions decrease significantly. Your written recap helps her work more independently, and she starts proactively bringing updates. While it takes a bit more upfront effort, the clarity improves delivery and mutual trust.<br><br><ul class=""circle-list""><li>+2 Effective Communication</li><li>+1 Engaging Others</li></ul>";
SIM_R4_Scenario1_FB_Opt2_Text;"<span class=""highlight""><i><u>This option aligns well with your goals and delivers the strongest outcomes.</u></i></span><br><br><span class=""highlight"">Leila feels empowered by your invitation and surprised by your vulnerability in naming the style mismatch. She proposes a shared checklist to anchor weekly updates and volunteers to draft team action plans. This sparks stronger engagement and creates a more balanced dynamic in your relationship.</span><br><br><ul class=""circle-list""><li>+1 Building Relationships</li><li>+2 Engaging Others</li><li>+1 Effective Communication</li></ul>";
SIM_R4_Scenario1_FB_Opt3_Text;"Your intention to grow Leila's resilience is clear, but the delivery falls flat. She agrees to try, but in future meetings she seems more reserved and hesitant. A week later, she asks to delay a key deliverable — something that’s never happened before. The message she received: adapt or fall behind.<br><br><ul class=""circle-list""><li>-1 Engaging Others</li><li>-1 Building Relationships</li></ul>";
SIM_R4_Scenario1_FB_Opt4_Text;"Leila remains quiet and compliant, but slowly disengages. She does her job, but no longer volunteers insights or stretch work. You eventually learn through another teammate that she’s looking for a transfer to a different team where “communication feels easier.”<br><br><ul class=""circle-list""><li>-2 Engaging Others</li><li>-1 Building Relationships</li></ul>";
SIM_R4_Scenario2_Header;"Moment 8";
SIM_R4_Scenario2_Title;"Roleplay Context";
SIM_R4_Scenario2_Text;"You recently adjusted your communication style to better support Leila. You introduced more structure to your updates, clarified expectations, and followed up in writing. These changes helped reduce her follow-up questions and seemed to address some of the confusion.<br><br>Since then, Leila has remained professional and steady, but you’ve noticed her engagement hasn’t fully returned. She’s contributing less in discussions, and she hasn’t expressed any goals or direction during performance planning.<br><br>Today’s conversation is part of the goal-setting process. It’s also an opportunity to check in more deeply on her development. You believe there may be something she hasn’t shared yet, and you want to understand what support she might need to move forward with more clarity and motivation.<br><br><b>As a team</b>, decide how you will <b>approach the career conversation with Leila</b>. Make sure to review the Mubadala Career development Framework for guidance. Choose one person from the team to lead the conversation with Leila and make sure you speak with the bot in a quiet environment.";
SIM_R4_Scenario2_Img;"/images/client/R4scenario2.jpg";
SIM_R4_Scenario2_Link;"Please access the roleplay through the following link: <a href=""https://alpha.wonderway.io/share/hUXSTWEWMQojuXATImpE"" target=""_blank""><u>https://alpha.wonderway.io/share/hUXSTWEWMQojuXATImpE</u></a>.";
SIM_R4_Scenario2_Password;"When asked for the password, please enter the following: <b><i>vital-amethyst-stoat</i></b>";
SIM_Roleplay_R4_table_Title;"Roleplay Score";
SIM_Roleplay_R4_Submit_Success;"Participant score updated.";


;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;
;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;
;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;
;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;
;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;
;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;
;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;


Participants:;KeypadId;Email;Language;Expiry;Password;Privileges;CurrentActionControl;GD;GD_Foreman;Follower;Team;Team_Foreman;Filter_Agg;

Control Room;0;admin;;;;Administrator,GroupPrivileges;Director;

Gonzalo Ortega;;<EMAIL>;;;;Vote,Administrator,GroupPrivileges;Individual;
Joshua Duffill;;<EMAIL>;;;;Vote,Administrator,GroupPrivileges;Individual;
Liliana Puente;;<EMAIL>;;;;Vote,Administrator,GroupPrivileges;Individual;
Cristian Rossato;;<EMAIL>;;;;Vote,Administrator,GroupPrivileges;Individual;
Peiwen Low;;<EMAIL>;;;;Vote,Administrator,GroupPrivileges;Individual;
Antonio Gonzalez;;<EMAIL>;;;;Vote,Administrator,GroupPrivileges;Individual;
Gabriel Oliveira;;<EMAIL>;;;;Vote,Administrator,GroupPrivileges;Individual;

Facilitator;;mbdlfac;en;;mb%0;Vote,GroupDirector;Individual:LandingPage_FAC;1;1;;;;;

Team 1;;mbdl1;en;;mb%0;Vote;ForemanOrDirector;1;;0;1;1;1;
Team 1 - follower;;mbdl1f;en;;mb%0;Vote;ForemanOrDirector;1;;1;1;;;
Team 2;;mbdl2;en;;mb%0;Vote;ForemanOrDirector;1;;0;2;2;1;
Team 2 - follower;;mbdl2f;en;;mb%0;Vote;ForemanOrDirector;1;;1;2;;;
Team 3;;mbdl3;en;;mb%0;Vote;ForemanOrDirector;1;;0;3;3;1;
Team 3 - follower;;mbdl3f;en;;mb%0;Vote;ForemanOrDirector;1;;1;3;;;
Team 4;;mbdl4;en;;mb%0;Vote;ForemanOrDirector;1;;0;4;4;1;
Team 4 - follower;;mbdl4f;en;;mb%0;Vote;ForemanOrDirector;1;;1;4;;;
Team 5;;mbdl5;en;;mb%0;Vote;ForemanOrDirector;1;;0;5;5;1;
Team 5 - follower;;mbdl5f;en;;mb%0;Vote;ForemanOrDirector;1;;1;5;;;
Team 6;;mbdl6;en;;mb%0;Vote;ForemanOrDirector;1;;0;6;6;1;
Team 6 - follower;;mbdl6f;en;;mb%0;Vote;ForemanOrDirector;1;;1;6;;;
Team 7;;mbdl7;en;;mb%0;Vote;ForemanOrDirector;1;;0;7;7;1;
Team 7 - follower;;mbdl7f;en;;mb%0;Vote;ForemanOrDirector;1;;1;7;;;
Team 8;;mbdl8;en;;mb%0;Vote;ForemanOrDirector;1;;0;8;8;1;
Team 8 - follower;;mbdl8f;en;;mb%0;Vote;ForemanOrDirector;1;;1;8;;;
Team 9;;mbdl9;en;;mb%0;Vote;ForemanOrDirector;1;;0;9;9;1;
Team 9 - follower;;mbdl9f;en;;mb%0;Vote;ForemanOrDirector;1;;1;9;;;

DataExport:;SavedOn;ExportConfig;
Data_Report_R1_P1;2025-06-02T20:40:06Z;{"id":null,"separator":{"value":"1"},"newtemplate":{"value":false},"selectedFields":{"isAssmntData":{"value":true},"isIdeaField":{"value":false},"isVideoField":{"value":false}},"userData":{"name":{"value":true,"label":null},"company":{"value":false,"label":null},"language":{"value":false,"label":null},"questions":[{"id":350759,"label":null,"questionName":"Q_FINISH_DATE_R1","format":null},{"id":350757,"label":null,"questionName":"Q_LOGIN_DATE_R1","format":null},{"id":350762,"label":null,"questionName":"Q_My_Avatar","format":null},{"id":350761,"label":null,"questionName":"Q_My_Name","format":null},{"id":350792,"label":null,"questionName":"Q_SIM_R1_Scenario1","format":null},{"id":350767,"label":null,"questionName":"Score_SIM_Init_KPI1","format":null},{"id":350768,"label":null,"questionName":"Score_SIM_Init_KPI2","format":null},{"id":350769,"label":null,"questionName":"Score_SIM_Init_KPI3","format":null},{"id":350770,"label":null,"questionName":"Score_SIM_Init_KPI4","format":null},{"id":350771,"label":null,"questionName":"Score_SIM_Init_MTUs_1","format":null},{"id":350772,"label":null,"questionName":"Score_SIM_Init_MTUs_2","format":null},{"id":350773,"label":null,"questionName":"Score_SIM_Init_MTUs_3","format":null},{"id":350805,"label":null,"questionName":"Score_SIM_R1_Roleplay","format":null},{"id":350806,"label":null,"questionName":"Score_SIM_R1_Roleplay_Impact","format":null},{"id":350793,"label":null,"questionName":"Score_SIM_R1_Scenario1_KPI1","format":null},{"id":350794,"label":null,"questionName":"Score_SIM_R1_Scenario1_KPI2","format":null},{"id":350795,"label":null,"questionName":"Score_SIM_R1_Scenario1_KPI3","format":null},{"id":350796,"label":null,"questionName":"Score_SIM_R1_Scenario1_KPI4","format":null},{"id":350797,"label":null,"questionName":"Score_SIM_R1_Scenario1_MTUs_1","format":null},{"id":350798,"label":null,"questionName":"Score_SIM_R1_Scenario1_MTUs_2","format":null},{"id":350799,"label":null,"questionName":"Score_SIM_R1_Scenario1_MTUs_3","format":null},{"id":350779,"label":null,"questionName":"Score_SIM_Total_KPI1","format":null},{"id":350780,"label":null,"questionName":"Score_SIM_Total_KPI2","format":null},{"id":350781,"label":null,"questionName":"Score_SIM_Total_KPI3","format":null},{"id":350782,"label":null,"questionName":"Score_SIM_Total_KPI4","format":null},{"id":350783,"label":null,"questionName":"Score_SIM_Total_MTUs_1","format":null},{"id":350784,"label":null,"questionName":"Score_SIM_Total_MTUs_2","format":null},{"id":350785,"label":null,"questionName":"Score_SIM_Total_MTUs_3","format":null},{"id":350832,"label":null,"questionName":"Score_SIM_Total_R1","format":null},{"id":350807,"label":null,"questionName":"Score_SIM_Total_R1_KPI1","format":null},{"id":350830,"label":null,"questionName":"Score_SIM_Total_R1_KPI1_Base","format":null},{"id":350827,"label":null,"questionName":"Score_SIM_Total_R1_KPI1_Impacts","format":null},{"id":350808,"label":null,"questionName":"Score_SIM_Total_R1_KPI2","format":null},{"id":350809,"label":null,"questionName":"Score_SIM_Total_R1_KPI3","format":null},{"id":350810,"label":null,"questionName":"Score_SIM_Total_R1_KPI4","format":null},{"id":350811,"label":null,"questionName":"Score_SIM_Total_R1_MTUs_1","format":null},{"id":350813,"label":null,"questionName":"Score_SIM_Total_R1_MTUs_2","format":null},{"id":350815,"label":null,"questionName":"Score_SIM_Total_R1_MTUs_3","format":null},{"id":350754,"label":null,"questionName":"Team","format":null}]},"assessmentData":{"module":{"value":false,"label":null},"sessionStartTime":{"value":false,"label":null},"timeZone":{"value":null},"assessorRoles":[],"capabilityScores":{"value":false,"label":null},"totalScore":{"value":false,"label":null},"capabilities":{"value":false,"label":null},"overallAssessment":{"value":false,"label":null},"cohort":{"value":false,"label":null},"delivery":{"value":false,"label":null},"cohortRegistrar":{"value":false,"label":null},"sessionRegistrar":{"value":false,"label":null}},"exerciseData":{"modules":[],"categorical":{"value":false},"formatData":{"calibration":false,"aggregate":false}},"questionsList":[],"ideaHunt":{"action":{"value":null},"entryQuestion":null,"questions":[],"rankings":[],"author":{"value":false,"label":null},"avgRating":{"value":false,"label":null},"rank":{"value":false,"label":null},"individualVotes":{"value":false,"label":null}},"videoHunt":{"action":{"value":null},"author":{"value":false,"label":null},"entryQuestion":null,"questions":[],"exportVideos":{"value":false,"label":null}},"phoneBank":{"phoneBankExercise":[]},"filters":{"startDateTime":null,"endDateTime":null,"cohorts":[],"filterQuestions":[],"deliveries":[],"companies":[]},"meetingData":{"isMeetingIncluded":false},"gatewayData":{"isGatewayIncluded":false},"riskStormData":{"isRiskStormIdeaIncluded":false,"isRiskStormVideoIncluded":false,"downloadVideos":false}};
Data_Report_R1_P2;2025-06-02T20:50:00Z;{"id":null,"separator":{"value":"1"},"newtemplate":{"value":false},"selectedFields":{"isAssmntData":{"value":true},"isIdeaField":{"value":false},"isVideoField":{"value":false}},"userData":{"name":{"value":true,"label":null},"company":{"value":false,"label":null},"language":{"value":false,"label":null},"questions":[{"id":350760,"label":null,"questionName":"Q_FINISH_DATE_R2","format":null},{"id":350758,"label":null,"questionName":"Q_LOGIN_DATE_R2","format":null},{"id":350762,"label":null,"questionName":"Q_My_Avatar","format":null},{"id":350761,"label":null,"questionName":"Q_My_Name","format":null},{"id":350833,"label":null,"questionName":"Q_SIM_R2_Scenario1","format":null},{"id":350846,"label":null,"questionName":"Score_SIM_R2_Roleplay","format":null},{"id":350847,"label":null,"questionName":"Score_SIM_R2_Roleplay_Impact","format":null},{"id":350834,"label":null,"questionName":"Score_SIM_R2_Scenario1_KPI1","format":null},{"id":350835,"label":null,"questionName":"Score_SIM_R2_Scenario1_KPI2","format":null},{"id":350836,"label":null,"questionName":"Score_SIM_R2_Scenario1_KPI3","format":null},{"id":350837,"label":null,"questionName":"Score_SIM_R2_Scenario1_KPI4","format":null},{"id":350838,"label":null,"questionName":"Score_SIM_R2_Scenario1_MTUs_1","format":null},{"id":350839,"label":null,"questionName":"Score_SIM_R2_Scenario1_MTUs_2","format":null},{"id":350840,"label":null,"questionName":"Score_SIM_R2_Scenario1_MTUs_3","format":null},{"id":350779,"label":null,"questionName":"Score_SIM_Total_KPI1","format":null},{"id":350780,"label":null,"questionName":"Score_SIM_Total_KPI2","format":null},{"id":350781,"label":null,"questionName":"Score_SIM_Total_KPI3","format":null},{"id":350782,"label":null,"questionName":"Score_SIM_Total_KPI4","format":null},{"id":350783,"label":null,"questionName":"Score_SIM_Total_MTUs_1","format":null},{"id":350784,"label":null,"questionName":"Score_SIM_Total_MTUs_2","format":null},{"id":350785,"label":null,"questionName":"Score_SIM_Total_MTUs_3","format":null},{"id":350807,"label":null,"questionName":"Score_SIM_Total_R1_KPI1","format":null},{"id":350808,"label":null,"questionName":"Score_SIM_Total_R1_KPI2","format":null},{"id":350809,"label":null,"questionName":"Score_SIM_Total_R1_KPI3","format":null},{"id":350810,"label":null,"questionName":"Score_SIM_Total_R1_KPI4","format":null},{"id":350811,"label":null,"questionName":"Score_SIM_Total_R1_MTUs_1","format":null},{"id":350813,"label":null,"questionName":"Score_SIM_Total_R1_MTUs_2","format":null},{"id":350815,"label":null,"questionName":"Score_SIM_Total_R1_MTUs_3","format":null},{"id":350848,"label":null,"questionName":"Score_SIM_Total_R2_KPI1","format":null},{"id":350871,"label":null,"questionName":"Score_SIM_Total_R2_KPI1_Base","format":null},{"id":350868,"label":null,"questionName":"Score_SIM_Total_R2_KPI1_Impacts","format":null},{"id":350849,"label":null,"questionName":"Score_SIM_Total_R2_KPI2","format":null},{"id":350850,"label":null,"questionName":"Score_SIM_Total_R2_KPI3","format":null},{"id":350851,"label":null,"questionName":"Score_SIM_Total_R2_KPI4","format":null},{"id":350852,"label":null,"questionName":"Score_SIM_Total_R2_MTUs_1","format":null},{"id":350854,"label":null,"questionName":"Score_SIM_Total_R2_MTUs_2","format":null},{"id":350856,"label":null,"questionName":"Score_SIM_Total_R2_MTUs_3","format":null},{"id":350754,"label":null,"questionName":"Team","format":null}]},"assessmentData":{"module":{"value":false,"label":null},"sessionStartTime":{"value":false,"label":null},"timeZone":{"value":null},"assessorRoles":[],"capabilityScores":{"value":false,"label":null},"totalScore":{"value":false,"label":null},"capabilities":{"value":false,"label":null},"overallAssessment":{"value":false,"label":null},"cohort":{"value":false,"label":null},"delivery":{"value":false,"label":null},"cohortRegistrar":{"value":false,"label":null},"sessionRegistrar":{"value":false,"label":null}},"exerciseData":{"modules":[],"categorical":{"value":false},"formatData":{"calibration":false,"aggregate":false}},"questionsList":[],"ideaHunt":{"action":{"value":null},"entryQuestion":null,"questions":[],"rankings":[],"author":{"value":false,"label":null},"avgRating":{"value":false,"label":null},"rank":{"value":false,"label":null},"individualVotes":{"value":false,"label":null}},"videoHunt":{"action":{"value":null},"author":{"value":false,"label":null},"entryQuestion":null,"questions":[],"exportVideos":{"value":false,"label":null}},"phoneBank":{"phoneBankExercise":[]},"filters":{"startDateTime":null,"endDateTime":null,"cohorts":[],"filterQuestions":[],"deliveries":[],"companies":[]},"meetingData":{"isMeetingIncluded":false},"gatewayData":{"isGatewayIncluded":false},"riskStormData":{"isRiskStormIdeaIncluded":false,"isRiskStormVideoIncluded":false,"downloadVideos":false}};

Subsystems:;type
Syntase.Subsystem.Editor, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null;Syntase.Subsystem.Editor.EditorSystem
Syntase.Subsystem.Ranking, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null;Syntase.Subsystem.Ranking.RankingSystem
Wizer.Subsystem.Search, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null;Wizer.Subsystem.Search.SearchSystem
Wizer.Subsystem.Cache, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null;Wizer.Subsystem.Cache.CacheSystem


