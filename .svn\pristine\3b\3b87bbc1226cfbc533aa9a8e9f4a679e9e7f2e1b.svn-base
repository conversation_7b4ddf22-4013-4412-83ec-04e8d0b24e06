﻿<div class="container {{?it.valign}}valign-container{{?}}"> 
    <div class="row {{?it.valign}}valign-wrapper{{?}}">
     
        <div>
            
            {{?it.header}}<h4 class="header title">{{=it.header}}</h4>{{?}}
            {{?it.subheader}}<h5 class="header subtitle">{{=it.subheader}}</h5>{{?}}
            {{?it.instructions}}<h6 class="instructions flow-text">{{=it.instructions}}</h6>{{?}}
            <div class="card hoverable {{?it.size}}{{=it.size}}{{?}}">
                
                <div class="card-content">
                    
                    {{?it.title}}<h5 class="header title">{{=it.title}}</h5>{{?}}
                    <div class="row">
                        <form class="col s12" novalidate autocomplete="off">
                            
                            {{?it.password.subtitle}}<h6 class="header strong subtitle">{{=it.password.subtitle}}</h6>{{?}}
                            <div class="row">
                                <div class="input-field inline col s12">
                                    <i class="material-icons prefix">security</i>
                                    
                                    <input name="{{=it.password.name}}" type="password" maxlength="{{=it.password.length}}"
                                        required class="no-validate"/>
                                    
                                    <label for="{{=it.password.name}}">{{=it.password.label}}</label>

                                    <span class="helper-text" data-error="{{=it.password.wrong}}" data-success="{{=it.password.right}}"></span>
                                </div>
                            </div>

                        </form>
                    </div>

                </div>           
            </div>

                
            <div class="row submit">
                
                {{?it.submitBtn}}
                <a id="submitBtn" {{?it.submitBtn.hidden}}hidden{{?}} class="btn client-colors red"
                   {{?it.DB[it.isFollower]>0}}disabled{{?}}>
                    <i class="medium material-icons right">send</i>{{=it.submitBtn.label}}
                </a>
                {{?}}
            </div>

        </div>

    </div>
</div>
