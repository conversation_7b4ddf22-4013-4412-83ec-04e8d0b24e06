<?xml version="1.0" encoding="utf-8" ?>
<Action>
    
  <Component type="Header" wizletName="HeaderFAC" customJS="true"><![CDATA[{
    templateInEvent: "html/header.dot",
    css: "styles/header.css",
    logo:   {
        src: "!{Head<PERSON>_logo}",
        alt: "logo"
    },
    user: {
      background: "",
      avatar: "!{Header_logo_menu}",
      my_avatar: "Q_My_Avatar"
    },
    
    myName: "Q_My_Name",
    myTeam: "",
      
    follower_suffix: " - !{Header_Follower}",
    trackTeam: "Team",
    isFollower: "Follower",

    scope: ["Q_My_Name","Q_My_Avatar","Follower"],

    links: [
      {
        section: true,
        sectionID: "dropdownInfo",
        sectionTitle: "!{Header_LinkSection_Info}",
        sectionLast: false,
        id: "link_teams",
        title: "!{Header_LinkTeams}",
        icon: "group",
        popup: "TeamName_Table_menu",
        popupID: "modal-teams"
      },
        {
          divider: true
        },
      {
        section: true,
        sectionLast: true,
        id: "link_case",
        title: "!{Header_LinkCase}",
        icon: "menu_book",
        popup: "SIM_CaseStudy_menu",
        popupID: "modal-case"
      },
        {
          divider: true
        },
      {
        id: "link_refresh",
        title: "!{Header_LinkRefresh}",
        icon: "refresh",
        onclick: "window.location.reload(true)"
      },
        {
          divider: true
        },
      {
        id: "link_exit",
        title: "!{Header_LinkExit}",
        icon: "exit_to_app",
        modalID: "modal-logout"
      }
    ],
    
    close: "!{Header_LinkClose}",
    sectionsID: [
      "modal-teams", "modal-ranking", "modal-case"
    ],

    logout: {
      modalID: "modal-logout",
      header: "!{Header_Modal_Logout_Title}",
      text: "!{Header_Modal_Logout_Text}",
      close: "!{Header_Modal_Logout_Close}",
      logout: "!{Header_Modal_Logout_Logout}",
      onclick: "logout()"
    }    
    
  }]]></Component>


  <Component type="Vanilla" wizletName="Footer"><![CDATA[{
    templateInEvent: "html/footer.dot",
    css: "styles/footer.css",
    footer: {
      color: "dark-grey",
      banner: "!{Footer_Img}",
      _logo: {src:"", alt:""},
      title: "!{Footer_Title}",
      subtitle: "!{Footer_Subtitle}",
      copyright: "", link: ""
    }
  }]]></Component>

</Action>