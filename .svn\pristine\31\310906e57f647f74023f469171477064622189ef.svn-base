<?xml version="1.0" encoding="utf-8" ?>
<Action>

	<Component type="Gauges" wizletName="KPIgauges" customJS="true">
		<![CDATA[{
			templateInEvent: "html/gauges.dot",
			css: "styles/gauges.css",
			animate: "zoomIn",
			totals: [
				{
					label: "!{KPI_Score}",
					binding: "Score_SIM_Total_R3"
				}
			],
			dynamic: "true",
      gauges:[
        {
          binding: "Score_SIM_Total_R3_KPI1",
          showNumber: true,
          percentage: false,
          titleGauge: {  text: "!{KPI_Metric1}"  },
          yAxis: {
            stops:[
              [0, 'rgba(255,255,255,0)'],
              [0.01, '#ffffff'],
              [100, '#ffffff']
            ],
            lineWidth: 0,
            minorTickInterval: null,
            showFirstLabel:false,
            showLastLabel:false,
            min: !{KPI_Metric1_Min},
            max: !{KPI_Metric1_Max},
            tickPixelInterval: 400,
            tickWidth: 0
          }
        },
        {
          binding: "Score_SIM_Total_R3_KPI2",
          showNumber: true,
          percentage: false,
          titleGauge: {  text: "!{KPI_Metric2}"  },
          yAxis: {
            stops:[
              [0, 'rgba(255,255,255,0)'],
              [0.01, '#ffffff'],
              [100, '#ffffff']
            ],
            lineWidth: 0,
            minorTickInterval: null,
            showFirstLabel:false,
            showLastLabel:false,
            min: !{KPI_Metric2_Min},
            max: !{KPI_Metric2_Max},
            tickPixelInterval: 400,
            tickWidth: 0
          }
        },
        {
          binding: "Score_SIM_Total_R3_KPI3",
          showNumber: true,
          percentage: false,
          titleGauge: {  text: "!{KPI_Metric3}"  },
          yAxis: {
            stops:[
              [0, 'rgba(255,255,255,0)'],
              [0.01, '#ffffff'],
              [100, '#ffffff']
            ],
            lineWidth: 0,
            minorTickInterval: null,
            showFirstLabel:false,
            showLastLabel:false,
            min: !{KPI_Metric3_Min},
            max: !{KPI_Metric3_Max},
            tickPixelInterval: 400,
            tickWidth: 0
          }
        },
        {
          binding: "Score_SIM_Total_R3_KPI4",
          showNumber: true,
          percentage: false,
          titleGauge: {  text: "!{KPI_Metric4}"  },
          yAxis: {
            stops:[
              [0, 'rgba(255,255,255,0)'],
              [0.01, '#ffffff'],
              [100, '#ffffff']
            ],
            lineWidth: 0,
            minorTickInterval: null,
            showFirstLabel:false,
            showLastLabel:false,
            min: !{KPI_Metric4_Min},
            max: !{KPI_Metric4_Max},
            tickPixelInterval: 400,
            tickWidth: 0
          }
        }
      ],

      modal_details: {
        id: "KPIroles_modal3",
        icon: "person_pin",
        close: "!{Header_LinkClose}"
      },
      
			bindings: {
				"Score_SIM_Total_R3": {
					dynamic:true,
					bind: "db:Score_SIM_Total_R3",
          trackQuestion: "Team",          			
					render: "renderer/spanBox",
					renderOptions: {
						numberFormat: {
							value: "0.0"
						}
					}
				},
				"Score_SIM_Total_R3_KPI1": {
					dynamic:true,
					bind: "db:Score_SIM_Total_R3_KPI1",
          trackQuestion: "Team",     
					render: "renderer/spanBox",
					renderOptions: {
						numberFormat: {
							value: "0"
						}
					}
				},
				"Score_SIM_Total_R3_KPI2": {
					dynamic:true,
					bind: "db:Score_SIM_Total_R3_KPI2",
          trackQuestion: "Team",     
					render: "renderer/spanBox",
					renderOptions: {
						numberFormat: {
							value: "0"
						}
					}
				},
				"Score_SIM_Total_R3_KPI3": {
					dynamic:true,
					bind: "db:Score_SIM_Total_R3_KPI3",
          trackQuestion: "Team",     
					render: "renderer/spanBox",
					renderOptions: {
						numberFormat: {
							value: "0"
						}
					}
				},
				"Score_SIM_Total_R3_KPI4": {
					dynamic:true,
					bind: "db:Score_SIM_Total_R3_KPI4",
          trackQuestion: "Team",     
					render: "renderer/spanBox",
					renderOptions: {
						numberFormat: {
							value: "0"
						}
					}
				}
			},
      
      trackTeam: "Team",
      isFollower: "Follower",

			scope: [
        "Follower",
        "Score_SIM_Total_R3",
        "Score_SIM_Total_R3_KPI1",
        "Score_SIM_Total_R3_KPI2",
        "Score_SIM_Total_R3_KPI3",
        "Score_SIM_Total_R3_KPI4"
      ]
		}]]>
	</Component>  


</Action>