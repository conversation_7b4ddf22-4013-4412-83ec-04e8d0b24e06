<?xml version="1.0" encoding="utf-8" ?>
<Action>
  

  <Component type="Vanilla">
    <![CDATA[{
      templateInEvent: "html/card.dot",
      css: "styles/card.css",
      orientation: "vertical",
      header: "!{SIM_CaseStudy_Header}",
      valign: false,
      animate: "fadeIn",
      content: {
        img_embed: { 
          materialboxed: false, borders: false, frame: "", nopadding: false,
          src: "!{SIM_CaseStudy_Img}",  alt: "!{SIM_CaseStudy_Title}" ,
          position: "right", style: "",
          src_vert: "!{}"
        },
        _img: { 
          materialboxed: false, borders: false, frame: "", nopadding: false,
          src: "!{SIM_CaseStudy_Img}",  alt: "!{SIM_CaseStudy_Title}" ,
          src_vert: "!{}"
        },
        position: "up",
        title: "!{SIM_CaseStudy1_Title}",
        body: "!{SIM_CaseStudy1_Text}"
      }      
    }]]>
  </Component>
  

  <Component type="Vanilla">
    <![CDATA[{
      templateInEvent: "html/card.dot",
      css: "styles/card.css",
      orientation: "vertical",
      header: "!{}",
      valign: false,
      animate: "fadeIn",
      content: {
        _img_embed: { 
          materialboxed: false, borders: false, frame: "", nopadding: false,
          src: "!{SIM_CaseStudy_Img}",  alt: "!{SIM_CaseStudy_Title}" ,
          position: "right", style: "",
          src_vert: "!{}"
        },
        _img: { 
          materialboxed: false, borders: false, frame: "", nopadding: false,
          src: "!{SIM_CaseStudy_Img}",  alt: "!{SIM_CaseStudy_Title}" ,
          src_vert: "!{}"
        },
        position: "up",
        title: "!{SIM_CaseStudy2_Title}",
        body: "!{SIM_CaseStudy2_Text}"
      }      
    }]]>
  </Component>
  

  <Component type="Vanilla">
    <![CDATA[{
      templateInEvent: "html/card.dot",
      css: "styles/card.css",
      orientation: "vertical",
      header: "!{}",
      valign: false,
      animate: "fadeIn",
      content: {
        _img_embed: { 
          materialboxed: false, borders: false, frame: "", nopadding: false,
          src: "!{SIM_CaseStudy_Img}",  alt: "!{SIM_CaseStudy_Title}" ,
          position: "right", style: "",
          src_vert: "!{}"
        },
        _img: { 
          materialboxed: false, borders: false, frame: "", nopadding: false,
          src: "!{SIM_CaseStudy_Img}",  alt: "!{SIM_CaseStudy_Title}" ,
          src_vert: "!{}"
        },
        position: "up",
        title: "!{SIM_CaseStudy3_Title}",
        body: "!{SIM_CaseStudy3_Text}"
      }      
    }]]>
  </Component>

  <Component type="Vanilla">
    <![CDATA[{
      templateInEvent: "html/card.dot",
      css: "styles/card.css",
      orientation: "vertical",
      header: "!{}",
      valign: false,
      animate: "fadeIn",
      content: {
        _img_embed: { 
          materialboxed: false, borders: false, frame: "", nopadding: false,
          src: "!{SIM_CaseStudy_Img}",  alt: "!{SIM_CaseStudy_Title}" ,
          position: "right", style: "",
          src_vert: "!{}"
        },
        _img: { 
          materialboxed: false, borders: false, frame: "", nopadding: false,
          src: "!{SIM_CaseStudy_Img}",  alt: "!{SIM_CaseStudy_Title}" ,
          src_vert: "!{}"
        },
        position: "up",
        title: "!{SIM_CaseStudy4_Title}",
        body: "!{SIM_CaseStudy4_Text}"
      }      
    }]]>
  </Component>

  <Component type="Vanilla">
    <![CDATA[{
      templateInEvent: "html/card.dot",
      css: "styles/card.css",
      orientation: "vertical",
      header: "!{}",
      valign: false,
      animate: "fadeIn",
      content: {
        _img_embed: { 
          materialboxed: false, borders: false, frame: "", nopadding: false,
          src: "!{SIM_CaseStudy_Img}",  alt: "!{SIM_CaseStudy_Title}" ,
          position: "right", style: "",
          src_vert: "!{}"
        },
        _img: { 
          materialboxed: false, borders: false, frame: "", nopadding: false,
          src: "!{SIM_CaseStudy_Img}",  alt: "!{SIM_CaseStudy_Title}" ,
          src_vert: "!{}"
        },
        position: "up",
        title: "!{SIM_CaseStudy5_Title}",
        body: "!{SIM_CaseStudy5_Text}"
      }      
    }]]>
  </Component>

  <Component type="Vanilla">
    <![CDATA[{
      templateInEvent: "html/card.dot",
      css: "styles/card.css",
      orientation: "vertical",
      header: "!{}",
      valign: false,
      animate: "fadeIn",
      content: {
        _img_embed: { 
          materialboxed: false, borders: false, frame: "", nopadding: false,
          src: "!{SIM_CaseStudy_Img}",  alt: "!{SIM_CaseStudy_Title}" ,
          position: "right", style: "",
          src_vert: "!{}"
        },
        _img: { 
          materialboxed: false, borders: false, frame: "", nopadding: false,
          src: "!{SIM_CaseStudy_Img}",  alt: "!{SIM_CaseStudy_Title}" ,
          src_vert: "!{}"
        },
        position: "up",
        title: "!{SIM_CaseStudy6_Title}",
        body: "!{SIM_CaseStudy6_Text}"
      }      
    }]]>
  </Component>

  <Component type="Vanilla">
    <![CDATA[{
      templateInEvent: "html/card.dot",
      css: "styles/card.css",
      orientation: "vertical",
      header: "!{}",
      valign: false,
      animate: "fadeIn",
      content: {
        _img_embed: { 
          materialboxed: false, borders: false, frame: "", nopadding: false,
          src: "!{SIM_CaseStudy_Img}",  alt: "!{SIM_CaseStudy_Title}" ,
          position: "right", style: "",
          src_vert: "!{}"
        },
        _img: { 
          materialboxed: false, borders: false, frame: "", nopadding: false,
          src: "!{SIM_CaseStudy_Img}",  alt: "!{SIM_CaseStudy_Title}" ,
          src_vert: "!{}"
        },
        position: "up",
        title: "!{SIM_CaseStudy10_Title}",
        body: "!{SIM_CaseStudy10_Text}"
      }      
    }]]>
  </Component>
  


  <Component type="ScrollToButton" customJS="true">
    <![CDATA[{
      templateInEvent: "html/scrollToButton.dot",
      css: "styles/scrollToButton.css",
      animate: "bounceInUp",
      id: "",
      isHidden: false, 
      isModalWindow: true,
      icon: "publish",
      scrollTop: 0,
      showIfPosition: 40,
      pulse: true
    }]]>
  </Component>

</Action>