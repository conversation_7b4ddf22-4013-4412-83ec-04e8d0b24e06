<?xml version="1.0" encoding="utf-8" ?>
<Action disableDynamicVotes="false" mainAreaLayout="../../../layout/mainLayoutFAC" layout="../../../layout/tabsLayoutX2">
  <Include name="HeaderFAC_R3"></Include>


  <!-- Tabs component -->
  <Component type="Tabs" customJS="true"><![CDATA[{
      templateInEvent: "html/tabs.dot",
      css: "styles/tabs.css",
      header: "!{SIM_R2_FAC_debrief_Header_Part1}",
      instructions: "!{}",
      swipeable: true,
      tabs: [
          "!{SIM_R3_FAC_debrief_tab1}",
          "!{SIM_R3_FAC_debrief_tab2}",
          "!{SIM_R3_FAC_debrief_ranking}"
      ],
      scope: null
  }]]></Component>


<!-- ***** -->
<!-- TAB 1 -->
<!-- ***** -->
    
  <Component type="HCPieChart" customJS="true"><![CDATA[{
    templateInEvent: "html/HCPieChart.dot",
    css: "styles/HCPieChart.css",
    id: "pieChart1",
    class: "",
    header: "!{}",
    instructions: "!{RadioInstructionsAnswers}",    
    isAnswersDistribution: true,
    isCountingIfChecked: false,
    isCountingAllCombined: false,
    listenModel: false,
    questions: [
      {
        binding: "Q_SIM_R3_Scenario1"
      }
    ],
    trackQuestion: "",
    chartConfig: {
      chart: {    
        plotBackgroundColor: null,
        plotBorderWidth: null,
        plotShadow: false,
        _height: '60%',
        type: "pie"
      },
      title: {
        text: "!{SIM_R3_Scenario1_Question}"
      },
      subtitle: {
        text: ""
      },
      tooltip: {
        headerFormat: "",
        pointFormat: "{point.name}: <b>{point.y}</b> ({point.percentage:.1f}%)"
      },
      legend: {
        align: "center",
        verticalAlign: "bottom",
        floating: false
      },
      plotOptions: {
        pie: {
          size: "100%", 
          allowPointSelect: true,
          cursor: 'pointer',
          dataLabels: {
            enabled: true,
            useHTML: false,
            format: '<b>{point.percentage:.1f}</b> %',
            distance: -50,
            filter: {
                property: 'percentage',
                operator: '>',
                value: 1
            }
          },
          showInLegend: true
        }
      },
      series: [ 
        { 
          name: "",
          colorByPoint: true,
          data: [
            {
              name: "!{Choice_Opt1} - !{SIM_R3_Scenario1_Opt1}"
            },
            {
              name: "!{Choice_Opt2} - !{SIM_R3_Scenario1_Opt2}"
            },
            {
              name: "!{Choice_Opt3} - !{SIM_R3_Scenario1_Opt3}"
            },
            {
              name: "!{Choice_Opt4} - !{SIM_R3_Scenario1_Opt4}"
            }
          ]
        }
      ],

      responsive: {
        rules: [{
          condition: { maxWidth: 700 },
          chartOptions: {
            chart: { height: 'auto' },
            plotOptions: {
              pie: {  dataLabels: {  enabled: false  } }
            }
          }
        }]
      }
    },
    scope: [ ],    
    answers: "!{Results_VotesLabel}"
  }]]></Component>
  

  <Component type="IndividualResults" customJS="true"><![CDATA[{
    templateInEvent: "html/rankingTable.dot",
    css: "styles/rankingTable.css",
    header: "!{}",
    _instructions: "!{CheckboxesInstructionsAnswers}",   
    position: "",
    userHeader: "!{Ranking_User}",
    _me: "!{Me}",
    boundName: "Q_My_Name",
    showOriginalName: false,
    avatar: " ",
    boundAvatar: "Q_My_Avatar",
    defaultAvatar: "!{defaultAvatar}",
    isDataTables: false,
    class: "_responsive-table_ myresponsive-table _verticalMode_ subheaders noBold fixed",
    headerMultiLines: true,
    headerEllipsis: true,
    questions: [
      {
        show: true,
        title: "!{SIM_R3_Scenario1_Question}",
        binding: "Q_SIM_R3_Scenario1"
      }
    ],
    answervalidation: true,
    subheaders: [
      "",
      "!{}",
      "!{SIM_R3_Scenario1_FB_Solution}"
    ],
    trackQuestion: "GD",
    rankQuestion: "",
    sortByQuestion: "",
    sortOrder: "asc",
    listSkip: 0,
    listLength:100,
    showNear: 5,
    liveUpdate: false,
    markUpdate: false
  }]]></Component>


<!-- ***** -->
<!-- TAB 2 -->
<!-- ***** -->

    <Component type="IndividualResults" customJS="true"><![CDATA[{
    templateInEvent: "html/rankingTable.dot",
    css: "styles/rankingTable.css",
    class: "",
    position: "#",
    header: "!{}",
    userHeader: "!{Ranking_User}",
    boundName: "Q_My_Name",
    showOriginalName: true,
    avatar: " ",
    boundAvatar: "Q_My_Avatar",
    defaultAvatar: "!{defaultAvatar}",
    isDataTables: false,
    class: "_verticalMode_ myresponsive-table _noBold _fixed",
    questions: [
      {
        show: true,
        title: "!{Ranking_Score}",
        binding: "Score_SIM_R3_Roleplay",
				format: "0"
      }
    ],
    trackQuestion: "GD",
    rankQuestion: "",
    sortByQuestion: "Score_SIM_R3_Roleplay",
    sortOrder: "desc",
    listSkip: 0,
    listLength:100,
    showNear: 5,
    liveUpdate: false,
    markUpdate: false
  }]]></Component>


  <Component type="Vanilla">
    <![CDATA[{
      templateInEvent: "html/card.dot",
      css: "styles/card.css"    
    }]]>
  </Component>


<!-- ******* -->
<!-- RANKING -->
<!-- ******* -->

  <Component type="IndividualResults" customJS="true"><![CDATA[{
    templateInEvent: "html/rankingTable.dot",
    css: "styles/rankingTable.css",
    class: "",
    position: "#",
    header: "!{}",
    userHeader: "!{Ranking_User}",
    boundName: "Q_My_Name",
    showOriginalName: true,
    avatar: " ",
    boundAvatar: "Q_My_Avatar",
    defaultAvatar: "!{defaultAvatar}",
    isDataTables: false,
    class: "_verticalMode_ myresponsive-table _noBold _fixed",
    questions: [
      {
        show: true,
        title: "!{KPI_Metric1}",
        binding: "Score_SIM_Total_R3_KPI1",
				format: "0"
      },
      {
        show: true,
        title: "!{KPI_Metric2}",
        binding: "Score_SIM_Total_R3_KPI2",
				format: "0"
      },
      {
        show: true,
        title: "!{KPI_Metric3}",
        binding: "Score_SIM_Total_R3_KPI3",
				format: "0"
      },
      {
        show: true,
        title: "!{KPI_Metric4}",
        binding: "Score_SIM_Total_R3_KPI4",
				format: "0"
      },
      {
        show: true,
        title: "!{SIM_R3_FAC_debrief_roleplay}",
        binding: "Score_SIM_R3_Roleplay_Impact",
				format: "0.0"
      },
      {
        show: true,
        title: "!{KPI_TOTAL}",
        binding: "Score_SIM_Total_R3",
				format: "0.0"
      }
    ],
    trackQuestion: "GD",
    rankQuestion: "",
    sortByQuestion: "Score_SIM_Total_R3",
    sortOrder: "desc",
    listSkip: 0,
    listLength:100,
    showNear: 5,
    liveUpdate: false,
    markUpdate: false
  }]]></Component>
  

  <Component type="Vanilla">
    <![CDATA[{
      templateInEvent: "html/card.dot",
      css: "styles/card.css"    
    }]]>
  </Component>

  
  <!-- EXPORT CSV BUTTON -->
  
  <Component type="ActionButton" customJS="true">
    <![CDATA[{
      templateInEvent: "html/actionButton.dot",
      css: "styles/actionButton.css",
      animate: "bounceInUp",
      id: "btn_export",
      isHidden: false, _showDelay: 3000,
      title: "!{GroupDirector_ExportBtn_R2_Part1}",
      icon: "cloud_download",
      onclick: "",
      pulse: true,
      color: "aux1",
      modal: {
        modalID: "modal-export",
        header: "!{GroupDirector_Export_Modal_Title}",
        text: "!{GroupDirector_Export_Modal_Text}",
        close: "!{GroupDirector_Export_Modal_Close}",
        action: "!{GroupDirector_Export_Modal_Action}",
        onclick: "",
        onclickFunction: "dataExport",
        onclickQuestion: "GD",
        onclickQuestion2: "Data_Report_R2_P1"
      },
      
      isFollower: "Follower",
      scope: ["Follower"]
    }]]>
  </Component>





  <Component type="ScrollToButton" customJS="true">
    <![CDATA[{
      templateInEvent: "html/scrollToButton.dot",
      css: "styles/scrollToButton.css",
      animate: "bounceInUp",
      id: "",
      _isHidden: true, _showDelay: 5000,
      icon: "publish",
      scrollTop: 0,
      showIfPosition: 40,
      pulse: true
    }]]>
  </Component>


  <!-- Navigation component
       parameters:
        - aligned:
        - animate: kind of animation
        - id: to be located and shown when is hidden
        - isHidden: attribute to hide
        - showDelay: show the hidden button after Xms (waiting the animation)
        - buttons: array of buttons (previous, next or target)
   -->
  <Component type="Navigation" customJS="true">
    <![CDATA[{
      templateInEvent: "html/navigation.dot",
      css: "styles/navigation.css",
      aligned: "center",
      animate: "zoomIn animate__delay-2s",
      id: "btn_navigation_home",
      hasModals: true,
      buttons: [
        {
          type: "modal",
          pulse: false,
          popupID: "Modal_menu",
          popup: "FAC_Navigation",
          close: "!{Header_LinkClose}",
          label: "!{Navigation_menu}",
          icon: "list"
        },
        {
          type: "target",
          pulse: false,
          gdActionTrack: "GD",
          gdActionSection: "R4_LandingPage",
          targetSection: "R4_LandingPage_FAC",
          label: "!{GD_SIM_R2_Start_Part2}",
          icon: "screen_share"
        }
      ],
      
      isFollower: "Follower",
      scope: ["Follower"]
    }]]>
  </Component>




</Action>