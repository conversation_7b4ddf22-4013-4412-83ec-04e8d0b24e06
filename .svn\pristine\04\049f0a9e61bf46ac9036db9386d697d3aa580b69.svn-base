<?xml version="1.0" encoding="utf-8" ?>
<Action disableDynamicVotes="false" mainAreaLayout="../../../layout/mainLayoutFAC" layout="../../../layout/tabsLayoutX2">
  <Include name="HeaderFAC_R2"></Include>


  <!-- Tabs component -->
  <Component type="Tabs" customJS="true"><![CDATA[{
      templateInEvent: "html/tabs.dot",
      css: "styles/tabs.css",
      header: "!{SIM_R2_FAC_debrief_Header}",
      instructions: "!{}",
      swipeable: true,
      tabs: [
          "!{SIM_R2_FAC_debrief_tab2}",
          "!{SIM_R2_FAC_debrief_tab3}",
          "!{SIM_R2_FAC_debrief_tab4}",
          "!{SIM_R2_FAC_debrief_ranking}"
      ],
      scope: null
  }]]></Component>

      

<!-- ***** -->
<!-- TAB 1 -->
<!-- ***** -->

  <!-- <Component type="HCBarChart" customJS="true"><![CDATA[{
    templateInEvent: "html/HCBarChart.dot",
    css: "styles/HCBarChart.css",
    header: "!{}",
    instructions: "!{AllInitiativesInstructions}",   

    isStacked: false,

    isAnswersDistribution: true,    

    isRanking: false,
    isCheckBox: true,
    isPercentage: true, 
      _percentageMax: 100,

    listenModel: false,
    questions: [
      { binding: "Q_SIM_R2_Initiatives_1" },
      { binding: "Q_SIM_R2_Initiatives_2" },
      { binding: "Q_SIM_R2_Initiatives_3" },
      { binding: "Q_SIM_R2_Initiatives_4" },
      { binding: "Q_SIM_R2_Initiatives_5" },
      { binding: "Q_SIM_R2_Initiatives_6" },
      { binding: "Q_SIM_R2_Initiatives_7" },
      { binding: "Q_SIM_R2_Initiatives_8" },
      { binding: "Q_SIM_R2_Initiatives_9" },
      { binding: "Q_SIM_R2_Initiatives_10" },
      { binding: "Q_SIM_R2_Initiatives_11" },
      { binding: "Q_SIM_R2_Initiatives_12" }
    ],
    reversed: true,
    trackQuestion: "GD",
    chartConfig: {
      chart: {
        _type: "column",
        _marginTop: 15,
        type: "bar",
        marginRight: 15
      },
      title: {
        text: "!{}"
      },
      subtitle: {
        text: ""
      },
      xAxis: [
        {
          categories: [
            "!{Badge_Opt1}. !{SIM_R2_Initiatives_Opt1}",
            "!{Badge_Opt2}. !{SIM_R2_Initiatives_Opt2}",
            "!{Badge_Opt3}. !{SIM_R2_Initiatives_Opt3}",
            "!{Badge_Opt4}. !{SIM_R2_Initiatives_Opt4}",
            "!{Badge_Opt5}. !{SIM_R2_Initiatives_Opt5}",
            "!{Badge_Opt6}. !{SIM_R2_Initiatives_Opt6}",
            "!{Badge_Opt7}. !{SIM_R2_Initiatives_Opt7}",
            "!{Badge_Opt8}. !{SIM_R2_Initiatives_Opt8}",
            "!{Badge_Opt9}. !{SIM_R2_Initiatives_Opt9}",
            "!{Badge_Opt10}. !{SIM_R2_Initiatives_Opt10}",
            "!{Badge_Opt11}. !{SIM_R2_Initiatives_Opt11}",
            "!{Badge_Opt12}. !{SIM_R2_Initiatives_Opt12}"
          ],
          question: "Q_My_Name",
          isName: true
        }
      ],
      labels: {
        format: "{value} %"
      },
      yAxis: {
        min: 0,
        max: 100,
        title: {
          text: "!{}"
        },
        _tickInterval: "!{Barchart_Results_Max}",
        gridLineWidth: 0,
        endOnTick: true,
        labels: {
          format: "{value} %",
          step: 2
        },
        stackLabels: {
          enabled: false,
          format: "{total}",
          style: {
            fontWeight: 'bold'
          }
        },
        reversedStacks: false
      },
      tooltip: {
        headerFormat: "<b>{point.x}</b><br/>",
        _headerFormat: "<b>{point.x}</b><br/> <table>",
        pointFormat: '{point.y:.2f} %',
        _pointFormat: "<tr><td><b>{point.y:.0f}</b></td></tr>",
        footerFormat: '',
        _footerFormat: "</table>",
        crosshairs: true,
        shared: true,
        useHTML: true
      },
      legend: {
        reversed: false,
        enabled: false,
        align: "center",
        verticalAlign: "bottom",
        floating: false
      },
      plotOptions: {
        bar: {
          minPointLength: 5,
          stacking: "normal",
          dataLabels: {
            enabled: true,
            format: '{point.y:.0f}%',
            allowOverlap: true,
            inside: true
          }
        },
        column: {
          minPointLength: 5,
          stacking: "normal",
          dataLabels: {
            enabled: false,
            format: "{point.y:.0f}%",
            inside: true
          }
        }
      },
      series: [],
      _series: [
        { name: "!{SIM_R2_Initiatives_Opt1}" },
        { name: "!{SIM_R2_Initiatives_Opt2}" },
        { name: "!{SIM_R2_Initiatives_Opt3}" },
        { name: "!{SIM_R2_Initiatives_Opt4}" }
      ]
    },

    scope: [ ],
    _answersExcluded: 3,
    _answers: "!{Results_VotesLabel}"
  }]]></Component>



  <Component type="IndividualResults" customJS="true"><![CDATA[{
    templateInEvent: "html/rankingTable.dot",
    css: "styles/rankingTable.css",
    header: "!{}",
    _instructions: "!{CheckboxesInstructionsAnswers}",   
    position: "",
    userHeader: "!{Ranking_User}",
    _me: "!{Me}",
    boundName: "Q_My_Name",
    showOriginalName: false,
    avatar: " ",
    boundAvatar: "Q_My_Avatar",
    defaultAvatar: "!{defaultAvatar}",
    isDataTables: false,
    class: "_responsive-table_ _myresponsive-table_ verticalMode _subheaders_ noBold _fixed_",
    headerMultiLines: false,
    headerEllipsis: true,
    questions: [
      {
        show: true,
        title: "!{Badge_Opt1}-!{SIM_R2_Initiatives_Opt1}",
        binding: "Q_SIM_R2_Initiatives_1"
      },
      {
        show: true,
        title: "!{Badge_Opt2}-!{SIM_R2_Initiatives_Opt2}",
        binding: "Q_SIM_R2_Initiatives_2"
      },
      {
        show: true,
        title: "!{Badge_Opt3}-!{SIM_R2_Initiatives_Opt3}",
        binding: "Q_SIM_R2_Initiatives_3"
      },
      {
        show: true,
        title: "!{Badge_Opt4}-!{SIM_R2_Initiatives_Opt4}",
        binding: "Q_SIM_R2_Initiatives_4"
      },
      {
        show: true,
        title: "!{Badge_Opt5}-!{SIM_R2_Initiatives_Opt5}",
        binding: "Q_SIM_R2_Initiatives_5"
      },
      {
        show: true,
        title: "!{Badge_Opt6}-!{SIM_R2_Initiatives_Opt6}",
        binding: "Q_SIM_R2_Initiatives_6"
      },
      {
        show: true,
        title: "!{Badge_Opt7}-!{SIM_R2_Initiatives_Opt7}",
        binding: "Q_SIM_R2_Initiatives_7"
      },
      {
        show: true,
        title: "!{Badge_Opt8}-!{SIM_R2_Initiatives_Opt8}",
        binding: "Q_SIM_R2_Initiatives_8"
      },
      {
        show: true,
        title: "!{Badge_Opt9}-!{SIM_R2_Initiatives_Opt9}",
        binding: "Q_SIM_R2_Initiatives_9"
      },
      {
        show: true,
        title: "!{Badge_Opt10}-!{SIM_R2_Initiatives_Opt10}",
        binding: "Q_SIM_R2_Initiatives_10"
      },
      {
        show: true,
        title: "!{Badge_Opt11}-!{SIM_R2_Initiatives_Opt11}",
        binding: "Q_SIM_R2_Initiatives_11"
      },
      {
        show: true,
        title: "!{Badge_Opt12}-!{SIM_R2_Initiatives_Opt12}",
        binding: "Q_SIM_R2_Initiatives_12"
      }
    ],
    _answervalidation: true,
    _subheaders: [
      "",
      "!{}",
      "!{}"
    ],
    trackQuestion: "",
    rankQuestion: "",
    sortByQuestion: "",
    sortOrder: "asc",
    listSkip: 0,
    listLength:100,
    showNear: 5,
    liveUpdate: true,
    markUpdate: true
  }]]></Component> -->
  



<!-- ***** -->
<!-- TAB 2 -->
<!-- ***** -->
    
  <Component type="HCPieChart" customJS="true"><![CDATA[{
    templateInEvent: "html/HCPieChart.dot",
    css: "styles/HCPieChart.css",
    id: "pieChart1",
    class: "",
    header: "!{}",
    instructions: "!{RadioInstructionsAnswers}",    
    isAnswersDistribution: true,
    isCountingIfChecked: false,
    isCountingAllCombined: false,
    listenModel: false,
    questions: [
      {
        binding: "Q_SIM_R2_Scenario1"
      }
    ],
    trackQuestion: "",
    chartConfig: {
      chart: {    
        plotBackgroundColor: null,
        plotBorderWidth: null,
        plotShadow: false,
        _height: '60%',
        type: "pie"
      },
      title: {
        text: "!{SIM_R2_Scenario1_Question}"
      },
      subtitle: {
        text: ""
      },
      tooltip: {
        headerFormat: "",
        pointFormat: "{point.name}: <b>{point.y}</b> ({point.percentage:.1f}%)"
      },
      legend: {
        align: "center",
        verticalAlign: "bottom",
        floating: false
      },
      plotOptions: {
        pie: {
          size: "100%", 
          allowPointSelect: true,
          cursor: 'pointer',
          dataLabels: {
            enabled: true,
            useHTML: false,
            format: '<b>{point.percentage:.1f}</b> %',
            distance: -50,
            filter: {
                property: 'percentage',
                operator: '>',
                value: 1
            }
          },
          showInLegend: true
        }
      },
      series: [ 
        { 
          name: "",
          colorByPoint: true,
          data: [
            {
              name: "!{Choice_Opt1} - !{SIM_R2_Scenario1_Opt1}"
            },
            {
              name: "!{Choice_Opt2} - !{SIM_R2_Scenario1_Opt2}"
            },
            {
              name: "!{Choice_Opt3} - !{SIM_R2_Scenario1_Opt3}"
            },
            {
              name: "!{Choice_Opt4} - !{SIM_R2_Scenario1_Opt4}"
            }
          ]
        }
      ],

      responsive: {
        rules: [{
          condition: { maxWidth: 700 },
          chartOptions: {
            chart: { height: 'auto' },
            plotOptions: {
              pie: {  dataLabels: {  enabled: false  } }
            }
          }
        }]
      }
    },
    scope: [ ],    
    answers: "!{Results_VotesLabel}"
  }]]></Component>
  


  <Component type="IndividualResults" customJS="true"><![CDATA[{
    templateInEvent: "html/rankingTable.dot",
    css: "styles/rankingTable.css",
    header: "!{}",
    _instructions: "!{CheckboxesInstructionsAnswers}",   
    position: "",
    userHeader: "!{Ranking_User}",
    _me: "!{Me}",
    boundName: "Q_My_Name",
    showOriginalName: false,
    avatar: " ",
    boundAvatar: "Q_My_Avatar",
    defaultAvatar: "!{defaultAvatar}",
    isDataTables: false,
    class: "_responsive-table_ myresponsive-table _verticalMode_ subheaders noBold fixed",
    headerMultiLines: true,
    headerEllipsis: true,
    questions: [
      {
        show: true,
        title: "!{SIM_R2_Scenario1_Question}",
        binding: "Q_SIM_R2_Scenario1"
      }
    ],
    answervalidation: true,
    subheaders: [
      "",
      "!{}",
      "!{SIM_R2_Scenario1_FB_Solution}"
    ],
    trackQuestion: "",
    rankQuestion: "",
    sortByQuestion: "",
    sortOrder: "asc",
    listSkip: 0,
    listLength:100,
    showNear: 5,
    liveUpdate: false,
    markUpdate: false
  }]]></Component>
  




<!-- ***** -->
<!-- TAB 3 -->
<!-- ***** -->
    
    
  <Component type="HCPieChart" customJS="true"><![CDATA[{
    templateInEvent: "html/HCPieChart.dot",
    css: "styles/HCPieChart.css",
    id: "pieChart2",
    class: "",
    header: "!{}",
    instructions: "!{RadioInstructionsAnswers}",    
    isAnswersDistribution: true,
    isCountingIfChecked: false,
    isCountingAllCombined: false,
    listenModel: false,
    questions: [
      {
        binding: "Q_SIM_R2_Scenario2"
      }
    ],
    trackQuestion: "",
    chartConfig: {
      chart: {    
        plotBackgroundColor: null,
        plotBorderWidth: null,
        plotShadow: false,
        _height: '60%',
        type: "pie"
      },
      title: {
        text: "!{SIM_R2_Scenario2_Question}"
      },
      subtitle: {
        text: ""
      },
      tooltip: {
        headerFormat: "",
        pointFormat: "{point.name}: <b>{point.y}</b> ({point.percentage:.1f}%)"
      },
      legend: {
        align: "center",
        verticalAlign: "bottom",
        floating: false
      },
      plotOptions: {
        pie: {
          size: "100%", 
          allowPointSelect: true,
          cursor: 'pointer',
          dataLabels: {
            enabled: true,
            useHTML: false,
            format: '<b>{point.percentage:.1f}</b> %',
            distance: -50,
            filter: {
                property: 'percentage',
                operator: '>',
                value: 1
            }
          },
          showInLegend: true
        }
      },
      series: [ 
        { 
          name: "",
          colorByPoint: true,
          data: [
            {
              name: "!{Choice_Opt1} - !{SIM_R2_Scenario2_Opt1}"
            },
            {
              name: "!{Choice_Opt2} - !{SIM_R2_Scenario2_Opt2}"
            },
            {
              name: "!{Choice_Opt3} - !{SIM_R2_Scenario2_Opt3}"
            },
            {
              name: "!{Choice_Opt4} - !{SIM_R2_Scenario2_Opt4}"
            }
          ]
        }
      ],

      responsive: {
        rules: [{
          condition: { maxWidth: 700 },
          chartOptions: {
            chart: { height: 'auto' },
            plotOptions: {
              pie: {  dataLabels: {  enabled: false  } }
            }
          }
        }]
      }
    },
    scope: [ ],    
    answers: "!{Results_VotesLabel}"
  }]]></Component>
  


  <Component type="IndividualResults" customJS="true"><![CDATA[{
    templateInEvent: "html/rankingTable.dot",
    css: "styles/rankingTable.css",
    header: "!{}",
    _instructions: "!{CheckboxesInstructionsAnswers}",   
    position: "",
    userHeader: "!{Ranking_User}",
    _me: "!{Me}",
    boundName: "Q_My_Name",
    showOriginalName: false,
    avatar: " ",
    boundAvatar: "Q_My_Avatar",
    defaultAvatar: "!{defaultAvatar}",
    isDataTables: false,
    class: "_responsive-table_ myresponsive-table _verticalMode_ subheaders noBold fixed",
    headerMultiLines: true,
    headerEllipsis: true,
    questions: [
      {
        show: true,
        title: "!{SIM_R2_Scenario2_Question}",
        binding: "Q_SIM_R2_Scenario2"
      }
    ],
    answervalidation: true,
    subheaders: [
      "",
      "!{}",
      "!{SIM_R2_Scenario2_FB_Solution}"
    ],
    trackQuestion: "",
    rankQuestion: "",
    sortByQuestion: "",
    sortOrder: "asc",
    listSkip: 0,
    listLength:100,
    showNear: 5,
    liveUpdate: false,
    markUpdate: false
  }]]></Component>
  


<!-- ***** -->
<!-- TAB 4 -->
<!-- ***** -->

    
  <Component type="HCPieChart" customJS="true"><![CDATA[{
    templateInEvent: "html/HCPieChart.dot",
    css: "styles/HCPieChart.css",
    id: "pieChart3",
    class: "",
    header: "!{}",
    instructions: "!{RadioInstructionsAnswers}",    
    isAnswersDistribution: true,
    isCountingIfChecked: false,
    isCountingAllCombined: false,
    listenModel: false,
    questions: [
      {
        binding: "Q_SIM_R2_Scenario3"
      }
    ],
    trackQuestion: "",
    chartConfig: {
      chart: {    
        plotBackgroundColor: null,
        plotBorderWidth: null,
        plotShadow: false,
        _height: '60%',
        type: "pie"
      },
      title: {
        text: "!{SIM_R2_Scenario3_Question}"
      },
      subtitle: {
        text: ""
      },
      tooltip: {
        headerFormat: "",
        pointFormat: "{point.name}: <b>{point.y}</b> ({point.percentage:.1f}%)"
      },
      legend: {
        align: "center",
        verticalAlign: "bottom",
        floating: false
      },
      plotOptions: {
        pie: {
          size: "100%", 
          allowPointSelect: true,
          cursor: 'pointer',
          dataLabels: {
            enabled: true,
            useHTML: false,
            format: '<b>{point.percentage:.1f}</b> %',
            distance: -50,
            filter: {
                property: 'percentage',
                operator: '>',
                value: 1
            }
          },
          showInLegend: true
        }
      },
      series: [ 
        { 
          name: "",
          colorByPoint: true,
          data: [
            {
              name: "!{Choice_Opt1} - !{SIM_R2_Scenario3_Opt1}"
            },
            {
              name: "!{Choice_Opt2} - !{SIM_R2_Scenario3_Opt2}"
            },
            {
              name: "!{Choice_Opt3} - !{SIM_R2_Scenario3_Opt3}"
            },
            {
              name: "!{Choice_Opt4} - !{SIM_R2_Scenario3_Opt4}"
            }
          ]
        }
      ],

      responsive: {
        rules: [{
          condition: { maxWidth: 700 },
          chartOptions: {
            chart: { height: 'auto' },
            plotOptions: {
              pie: {  dataLabels: {  enabled: false  } }
            }
          }
        }]
      }
    },
    scope: [ ],    
    answers: "!{Results_VotesLabel}"
  }]]></Component>
  


  <Component type="IndividualResults" customJS="true"><![CDATA[{
    templateInEvent: "html/rankingTable.dot",
    css: "styles/rankingTable.css",
    header: "!{}",
    _instructions: "!{CheckboxesInstructionsAnswers}",   
    position: "",
    userHeader: "!{Ranking_User}",
    _me: "!{Me}",
    boundName: "Q_My_Name",
    showOriginalName: false,
    avatar: " ",
    boundAvatar: "Q_My_Avatar",
    defaultAvatar: "!{defaultAvatar}",
    isDataTables: false,
    class: "_responsive-table_ myresponsive-table _verticalMode_ subheaders noBold fixed",
    headerMultiLines: true,
    headerEllipsis: true,
    questions: [
      {
        show: true,
        title: "!{SIM_R2_Scenario3_Question}",
        binding: "Q_SIM_R2_Scenario3"
      }
    ],
    answervalidation: true,
    subheaders: [
      "",
      "!{}",
      "!{SIM_R2_Scenario3_FB_Solution}"
    ],
    trackQuestion: "",
    rankQuestion: "",
    sortByQuestion: "",
    sortOrder: "asc",
    listSkip: 0,
    listLength:100,
    showNear: 5,
    liveUpdate: false,
    markUpdate: false
  }]]></Component>
  


    

<!-- ***** -->
<!-- TAB 5 -->
<!-- ***** -->

    
  <!-- <Component type="HCPieChart" customJS="true"><![CDATA[{
    templateInEvent: "html/HCPieChart.dot",
    css: "styles/HCPieChart.css",
    id: "pieChart4",
    class: "",
    header: "!{}",
    instructions: "!{RadioInstructionsAnswers}",    
    isAnswersDistribution: true,
    isCountingIfChecked: false,
    isCountingAllCombined: false,
    listenModel: false,
    questions: [
      {
        binding: "Q_SIM_R2_Scenario4"
      }
    ],
    trackQuestion: "",
    chartConfig: {
      chart: {    
        plotBackgroundColor: null,
        plotBorderWidth: null,
        plotShadow: false,
        _height: '60%',
        type: "pie"
      },
      title: {
        text: "!{SIM_R2_Scenario4_Question}"
      },
      subtitle: {
        text: ""
      },
      tooltip: {
        headerFormat: "",
        pointFormat: "{point.name}: <b>{point.y}</b> ({point.percentage:.1f}%)"
      },
      legend: {
        align: "center",
        verticalAlign: "bottom",
        floating: false
      },
      plotOptions: {
        pie: {
          size: "100%", 
          allowPointSelect: true,
          cursor: 'pointer',
          dataLabels: {
            enabled: true,
            useHTML: false,
            format: '<b>{point.percentage:.1f}</b> %',
            distance: -50,
            filter: {
                property: 'percentage',
                operator: '>',
                value: 1
            }
          },
          showInLegend: true
        }
      },
      series: [ 
        { 
          name: "",
          colorByPoint: true,
          data: [
            {
              name: "!{Choice_Opt1} - !{SIM_R2_Scenario4_Opt1}"
            },
            {
              name: "!{Choice_Opt2} - !{SIM_R2_Scenario4_Opt2}"
            },
            {
              name: "!{Choice_Opt3} - !{SIM_R2_Scenario4_Opt3}"
            }
          ]
        }
      ],

      responsive: {
        rules: [{
          condition: { maxWidth: 700 },
          chartOptions: {
            chart: { height: 'auto' },
            plotOptions: {
              pie: {  dataLabels: {  enabled: false  } }
            }
          }
        }]
      }
    },
    scope: [ ],    
    answers: "!{Results_VotesLabel}"
  }]]></Component>
  


  <Component type="IndividualResults" customJS="true"><![CDATA[{
    templateInEvent: "html/rankingTable.dot",
    css: "styles/rankingTable.css",
    header: "!{}",
    _instructions: "!{CheckboxesInstructionsAnswers}",   
    position: "",
    userHeader: "!{Ranking_User}",
    _me: "!{Me}",
    boundName: "Q_My_Name",
    showOriginalName: false,
    avatar: " ",
    boundAvatar: "Q_My_Avatar",
    defaultAvatar: "!{defaultAvatar}",
    isDataTables: false,
    class: "_responsive-table_ myresponsive-table _verticalMode_ subheaders noBold fixed",
    headerMultiLines: true,
    headerEllipsis: true,
    questions: [
      {
        show: true,
        title: "!{SIM_R2_Scenario4_Question}",
        binding: "Q_SIM_R2_Scenario4"
      }
    ],
    answervalidation: true,
    subheaders: [
      "",
      "!{}",
      "!{SIM_R2_Scenario4_FB_Solution}"
    ],
    trackQuestion: "",
    rankQuestion: "",
    sortByQuestion: "",
    sortOrder: "asc",
    listSkip: 0,
    listLength:100,
    showNear: 5,
    liveUpdate: false,
    markUpdate: false
  }]]></Component> -->
  


<!-- ******* -->
<!-- RANKING -->
<!-- ******* -->

  <Component type="IndividualResults" customJS="true"><![CDATA[{
    templateInEvent: "html/rankingTable.dot",
    css: "styles/rankingTable.css",
    class: "",
    position: "#",
    _rank: 
      {
        title: "!{Ranking_Position}",
        binding: "Score_SIM_Total_R2_Rank"
      },
    header: "!{}",
    _subheader: "!{SIM_R2_FAC_debrief_ranking}",
    userHeader: "!{Ranking_User}",
    _me: "!{Me}",
    boundName: "Q_My_Name",
    showOriginalName: true,
    avatar: " ",
    boundAvatar: "Q_My_Avatar",
    defaultAvatar: "!{defaultAvatar}",
    isDataTables: false,
    class: "_verticalMode_ myresponsive-table _noBold _fixed",
    questions: [
      {
        show: true,
        title: "!{KPI_Metric8}",
        binding: "Score_SIM_Total_R2_KPI8",
				format: "0"
      },
      {
        show: true,
        title: "!{KPI_Metric18}",
        binding: "Score_SIM_Total_R2_KPI18",
				format: "0"
      },
      {
        show: true,
        title: "!{KPI_Metric7}",
        binding: "Score_SIM_Total_R2_KPI7",
				format: "0"
      },
      {
        show: true,
        title: "!{KPI_Score}",
        binding: "Score_SIM_Total_R2",
				format: "0.0"
      }
    ],
    _trackQuestion: "Team",
    rankQuestion: "",
    sortByQuestion: "Score_SIM_Total_R2",
    sortOrder: "desc",
    listSkip: 0,
    listLength:100,
    showNear: 5,
    liveUpdate: false,
    markUpdate: false
  }]]></Component>
  
  
  
  <!-- EXPORT CSV BUTTON (adding empty component to get the Export button out of the tabs layout which goes in pairs)-->
  <Component type="Vanilla">
    <![CDATA[{
      templateInEvent: "html/card.dot",
      css: "styles/card.css"    
    }]]>
  </Component>
  <Component type="ActionButton" customJS="true">
    <![CDATA[{
      templateInEvent: "html/actionButton.dot",
      css: "styles/actionButton.css",
      animate: "bounceInUp",
      id: "btn_export",
      isHidden: false, _showDelay: 3000,
      title: "!{GroupDirector_ExportBtn_R2}",
      icon: "cloud_download",
      onclick: "",
      pulse: true,
      color: "aux1",
      modal: {
        modalID: "modal-export",
        header: "!{GroupDirector_Export_Modal_Title}",
        text: "!{GroupDirector_Export_Modal_Text}",
        close: "!{GroupDirector_Export_Modal_Close}",
        action: "!{GroupDirector_Export_Modal_Action}",
        onclick: "",
        onclickFunction: "dataExport",
        onclickQuestion: "GD",
        onclickQuestion2: "Data Report R2"
      },
      
      isFollower: "Follower",
      scope: ["Follower"]
    }]]>
  </Component>





  <Component type="ScrollToButton" customJS="true">
    <![CDATA[{
      templateInEvent: "html/scrollToButton.dot",
      css: "styles/scrollToButton.css",
      animate: "bounceInUp",
      id: "",
      _isHidden: true, _showDelay: 5000,
      icon: "publish",
      scrollTop: 0,
      showIfPosition: 40,
      pulse: true
    }]]>
  </Component>


  <!-- Navigation component
       parameters:
        - aligned:
        - animate: kind of animation
        - id: to be located and shown when is hidden
        - isHidden: attribute to hide
        - showDelay: show the hidden button after Xms (waiting the animation)
        - buttons: array of buttons (previous, next or target)
   -->
  <Component type="Navigation" customJS="true">
    <![CDATA[{
      templateInEvent: "html/navigation.dot",
      css: "styles/navigation.css",
      aligned: "center",
      animate: "zoomIn animate__delay-2s",
      id: "btn_navigation_home",
      _isHidden: true, _showDelay: 1000,
      hasModals: true,
      buttons: [
        {
          type: "modal",
          pulse: false,
          popupID: "Modal_menu",
          popup: "FAC_Navigation",
          close: "!{Header_LinkClose}",
          label: "!{Navigation_menu}",
          icon: "list"
        }
      ],
      
      isFollower: "Follower",
      scope: ["Follower"]
    }]]>
  </Component>




</Action>