<?xml version="1.0" encoding="utf-8" ?>

<Action layout="../../../layout/tabsLayoutModal">
  
  <Component type="Tabs" customJS="true"><![CDATA[{
      templateInEvent: "html/tabs.dot",
      css: "styles/tabs.css",
      header: "!{}",
      swipeable: false,
      tabPanelID: "tabPanelModal",
      tabs: [
          "!{SIM_CaseStudy_Name_C1}",
          "!{SIM_CaseStudy_Name_C2}",
          "!{SIM_CaseStudy_Name_You}"
      ]
  }]]></Component>
  
  <Component type="Card" customJS="true">
    <![CDATA[{
      templateInEvent: "html/card.dot",
      css: "styles/card.css",
      orientation: "vertical",
      header: "!{}",
      instructions: "!{}",    
      valign: false,
      animate: "fadeIn",
      content: {
        img: { 
          materialboxed: false, 
          src: "!{SIM_CaseStudy_Img_C1}",  alt: "!{SIM_CaseStudy_Name_C1}" 
        },
        title: "!{}",
        body: "!{}"
      },
      feedback_score: {
        title: "!{SIM_CaseStudy_Name_C1}",
        kpi_titles: ["!{KPI_LTU}"],
        fromDB: true,
        kpi_scores: [],
        kpi_scoresDB: ["Score_SIM_Total_R2_MTUs_2"],
        lastBold: false,
        bind: "",
        value: 0
      },
      trackTeam: "Team",
      isFollower: "Follower",
      scope: ["Score_SIM_Total_R2_MTUs_2"]     
    }]]>
  </Component>
  <Component type="Card" customJS="true">
    <![CDATA[{
      templateInEvent: "html/card.dot",
      css: "styles/card.css",
      orientation: "vertical",
      header: "!{}",
      instructions: "!{}",    
      valign: false,
      animate: "fadeIn",
      content: {
        img: { 
          materialboxed: false, 
          src: "!{SIM_CaseStudy_Img_C2}",  alt: "!{SIM_CaseStudy_Name_C2}" 
        },
        title: "!{}",
        body: "!{}"
      },
      feedback_score: {
        title: "!{SIM_CaseStudy_Name_C2}",
        kpi_titles: ["!{KPI_LTU}"],
        fromDB: true,
        kpi_scores: [],
        kpi_scoresDB: ["Score_SIM_Total_R2_MTUs_3"],
        lastBold: false,
        bind: "",
        value: 0
      },
      trackTeam: "Team",
      isFollower: "Follower",
      scope: ["Score_SIM_Total_R2_MTUs_3"]     
    }]]>
  </Component>
  <Component type="Card" customJS="true">
    <![CDATA[{
      templateInEvent: "html/card_qImg.dot",
      css: "styles/card.css",
      orientation: "vertical",
      header: "!{}",
      instructions: "!{}",    
      valign: false,
      animate: "fadeIn",
      content: {
        qImg: { 
          materialboxed: false, 
          path: "images/avatar/",
          src: "Q_My_Avatar",  alt: "!{SIM_CaseStudy_Name_You}" 
        },
        title: "!{}",
        body: "!{}"
      },
      feedback_score: {
        title: "!{SIM_CaseStudy_Name_You}",
        kpi_titles: ["!{KPI_LTU}"],
        fromDB: true,
        kpi_scores: [],
        kpi_scoresDB: ["Score_SIM_Total_R2_MTUs_1"],
        lastBold: false,
        bind: "",
        value: 0
      },
      trackTeam: "Team",
      isFollower: "Follower",
      scope: ["Q_My_Avatar", "Score_SIM_Total_R2_MTUs_1"]
    }]]>
  </Component>

</Action>