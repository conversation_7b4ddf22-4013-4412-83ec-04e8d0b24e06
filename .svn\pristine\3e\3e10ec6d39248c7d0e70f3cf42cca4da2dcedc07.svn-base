﻿/* 
    "HCWebChart" component that takes a template and some data as the input
    and renders the chart using highcharts library.

*/

define(['jquery', 'Q', 'wizer-api', 'wizletBase', 'doT', 'highcharts-styled', 'highcharts-styled-more', 'css!lib/highcharts/code/css/highcharts.css', 'numeral', 'jsCalcLib/numberFormatting'], 
        function ($, Q, WizerApi, WizletBase, doT, Highcharts, HighchartsMore, HighchartsCss, numeral, numberFormatting) {

    var HCWebChart = function () {
        this.type = 'HCWebChart';
        this.level = 1;
    };

    HCWebChart.prototype.loadHandler = function (unused, wizletInfo, wizletContext, wizerApi) {
        this.wizletInfo = wizletInfo;
        this.wizletContext = wizletContext;
        this.wizerApi = wizerApi;
        

        this.templateDefer = new Q.defer();
        var self = this;
        var requirements = [];
        requirements.push(WizletBase.loadTemplate(wizletInfo, 'HCWebChart.dot'));

        if (wizletInfo.css) {
            requirements.push(WizletBase.loadCss(wizletInfo));
        }
        require(requirements, function (doTTemplate, css) {
            self.templateDefer.resolve(doTTemplate);
        });

        return WizletBase.loadHandler({ wizlet: this, render: this.render });
    };

    HCWebChart.prototype.unloadHandler = function () {
        //unload wizletbase
        WizletBase.unloadHandler({ wizlet: this });
        //$(document).off("wizer:model:change", this.redrawChart);
    };

    HCWebChart.prototype.render = function (options) {
        var self = this;
		 var fetching = new Q.defer();
        return self.templateDefer.promise.then(function (template) {
            var fragment = template(options.wizletInfo);
            options.context.html(fragment);
			self.isDelivery = self.wizletInfo.isDelivery || false;
            self.renderChart().then(function () {
                fetching.resolve(true);
            });
        })
        .fail(this.wizerApi.showError);
    };

    HCWebChart.prototype.renderChart = function() {
        var self = this;
        var rendering = new Q.defer();
        var chartDefaults = {
            "lang": {
                "thousandsSep": ",",
                "numericSymbols": ['k', 'm', 'b', 't']
            },
            "chart": {
                "polar": true,
                "type": 'line'
            },
            "credits": {
                "enabled": false
            },
            "legend":{ },
            "title": {
                "text": ''
            },
           "xAxis": [
                {
                    "categories": [],
                    "tickmarkPlacement": 'on',
                    "lineWidth": 0
                }
			],
            "yAxis": {
                "gridLineInterpolation": 'polygon',
                "lineWidth": 0
            },
            "tooltip": {
                "headerFormat": '<b>{point.x}</b><br/>',
                // "pointFormat": '{series.name}: {point.y}<br/>Total: {point.stackTotal}'
                "pointFormat": '{series.name}: {point.y}'
            },
            "plotOptions": {
                "series": {
                    "dataLabels": {
                        "enabled": true
                    }
                }
            }
        }


        var chartOptions = self.wizletInfo.chartConfig;
        var chartElem = self.wizletContext.find("[data-webchartholder]");
        chartOptions = $.extend(true, {}, chartDefaults, chartOptions);

        
        // check for db questions
        if (typeof self.wizletInfo.questions !== "undefined" && self.wizletInfo.questions.length) {
            

            if (self.wizletInfo.isAnswersDistribution) {
                self.calculateDistribution().then(function(data){
                    self.renderChartData.call(self, data, chartOptions, chartElem);
                    rendering.resolve(true);
                });


            } else {
                self.fetchVotes().then(function(data){
                    self.renderChartData.call(self, data, chartOptions, chartElem);
                     rendering.resolve(true);
                });

            }


        }
        else {
            rendering.resolve(true);
        }
		return rendering.promise;
    };



    HCWebChart.prototype.calculateDistribution = function() {
        var self = this;
		
        var trackDiffer = new Q.defer();

        if (self.wizletInfo.trackQuestion) {
            //fetch the trackQuestionId and my vote on that trackQuestion. This then becomes the new filterQuestionId and filterText
            var trackQuestionId = self.wizerApi.getQuestionIdByName(self.wizletInfo.trackQuestion);
            var gettingMyVoteOnTrack = self.wizerApi.getMyVotes([trackQuestionId]);
            gettingMyVoteOnTrack.then(function (votes) {
                self.myVoteonTrack = votes.votes[trackQuestionId];
                if (self.myVoteonTrack) {
                    trackDiffer.resolve(true);
                }
                else {
                    trackDiffer.resolve(true);
                    Wizer.Api.showError("No Votes of this user on trackQuestion: " + self.wizletInfo.trackQuestion);
                }
            });

        }
        else {
            trackDiffer.resolve(true);
        }


        return trackDiffer.promise.then(function () {

            var questName = [];
            self.wizletInfo.questions.forEach (function (question, idx) {
                questName.push(question.binding);
            });
            
            var waiting = self.wizerApi.getVotePercentage({
                shortNames: questName,
                filterQuestionId: self.wizletInfo.trackQuestion ? trackQuestionId : null,  
                filterText: self.wizletInfo.trackQuestion ? self.myVoteonTrack[0]  : '',
                seed: Math.random().toString().replace(".", ""),
                isDelivery: null
            });

            return waiting.then(function (result) { 
                var voteCount = result.totalVoteCount;
                if (self.wizletInfo.answersExcluded)
                    voteCount = voteCount * self.wizletInfo.questions.length / (self.wizletInfo.questions.length-self.wizletInfo.answersExcluded);
                self.wizletContext.find('.answers span.votes').html(voteCount);
                return self.processAverageVotes(result.votes[0]);;
            });
        });
        

    }



    HCWebChart.prototype.fetchVotes = function() {
        var self = this;
        var questions = [];
        self.wizletInfo.questions.forEach(function(q) {
            questions.push(q.binding);
        });
        
        self.wizletInfo.chartConfig.series.forEach(function(serie) {
            if(serie.question){
                questions.push(serie.question);
            }           
        });
        
        return self.wizerApi.getVotesByQuestionName(questions, self.wizletInfo.trackQuestion, self.wizletInfo.sorting, null, self.isDelivery).then(function(response){
            return self.processVotes(response);
        });

    }

    

    HCWebChart.prototype.processAverageVotes = function(votes) {
        
        var retVal = {}, series = [], self = this;
        
        votes.list.forEach (function (vote, idx) {

            if (self.wizletInfo.isRanking) {
                var maxRankingPoints = vote.totalCount * votes.list.length;
                var value = (maxRankingPoints-vote.totalVotes+vote.totalCount);
                if (self.wizletInfo.isPercentage) {
                    value = value / maxRankingPoints * 100;
                }
                series.push({y: value});                       

            } else {
                if (self.wizletInfo.isCheckBox) {
                    if (self.wizletInfo.isPercentage) {
                        series.push({y: Number((vote.averageVotes*100).toFixed(2))});
                    } else {
                        series.push({y: vote.totalVotes});
                    }
                } else {
                    if (self.wizletInfo.isPercentage) {
                        series.push({y: Number((vote.averageVotes * 100 / self.wizletInfo.percentageMax ).toFixed(2))});
                        // series.push({y: vote.averageVotes});
                    } else {
                        series.push({y: vote.averageVotes});
                    }
                }
            }
        });

                    
        retVal.series = [{
                data: series
            }];
        
        return retVal;
    }


    HCWebChart.prototype.processVotes = function(votes) {
        var retVal = {}, series = [], self = this;
        var teams = new Array(votes.participants.length);
        for (var i = 0; i < teams.length; i++) {
            teams[i] = new Array(self.wizletInfo.questions.length);
        }

        for (var i = 0; i < self.wizletInfo.questions.length; i++) {
            const q = self.wizletInfo.questions[i];
			var val;
            for (var index = 0; index < votes.participants.length; index++) {
                const p = votes.participants[index];
				if(p.questionMap && p.questionMap[q.binding] && p.questionMap[q.binding].value){
                    val = numeral(p.questionMap[q.binding].value).value();
                    
                    if (self.wizletInfo.isPercentage) {
                        val = Number((val * 100 / self.wizletInfo.percentageMax ).toFixed(2));
                    }
				}
				else{
					val = "";
				}
                teams[index][i] = val;
            }
        }
        
        var names;
        self.wizletInfo.chartConfig.series.forEach(function(serie) {
            var cat = serie.categories || [];
            var val;
            for (var index = 0; index < votes.participants.length; index++) {
                const p = votes.participants[index];
                
                val = ""; 
                if(serie.question){
                    if(p.questionMap[serie.question] && 
                        p.questionMap[serie.question].value){
                        val = p.questionMap[serie.question].value;
                    }
                }else{
                    val = p.name;
                }
                if (serie.isName && !val) val = p.name;
                cat.push(val);
            }
            names = cat;
            
        });

        for (var i = 0; i < teams.length; i++) {
            series.push({name: names[i], data: teams[i], pointPlacement: 'on'});
        }

        retVal.series = series;
        
        return retVal;
    }

    HCWebChart.prototype.renderChartData = function(data, chartOptions, chartElem) {

        chartOptions.series = $.extend(true, [], chartOptions.series, data.series);

        this.applyNumberFormat(chartOptions);
        this.formatTooltips(chartOptions);
        // render chart
        chartElem.highcharts(chartOptions);
		
		
    };

    HCWebChart.prototype.formatTooltips = function(chartOptions) {
        var formatters = discover(chartOptions, 'numformat');

        formatters.forEach(function(ob) {
            var format = ob.numformat,
                scaler = ob.scaler,
                formatFunction = (function(){
                    return function() {
                        var val = 0, point = this, out = '<strong>' + point.series.name + '</strong><br />';
                        out += '' + point.x + ': ';
                        if (typeof this.value !== "undefined") {
                            val = this.value;
                        }
                        else if (typeof this.y !== "undefined") {
                            val = this.y;
                        }
                        else if (typeof this.x !== "undefined") {
                            val = this.x;
                        }
                        out += numberFormatting.format(val, format, scaler);
                        return out;
                    }
                }());

            ob.formatter = formatFunction;
        });
    }

    HCWebChart.prototype.applyNumberFormat = function(chartOptions) {
        var formatters = discover(chartOptions, 'formatter');

        formatters.forEach(function(ob) {
            var format = ob.formatter,
                scaler = ob.scaler,
                formatFunction = (function(){
                    return function() {
                        var val = 0;
                        if (typeof this.value !== "undefined") {
                            val = this.value;
                        }
                        else if (typeof this.y !== "undefined") {
                            val = this.y;
                        }
                        else if (typeof this.x !== "undefined") {
                            val = this.x;
                        }
                        return numberFormatting.format(val, format, scaler);
                    }
                }());

            ob.formatter = formatFunction;
        });
    }

    HCWebChart.prototype.redrawChart = function(e) {
        var self = e.data.self;
        var redraw = new Q.defer();
        var chart = self.wizletContext.find("[data-columnChartHolder]").highcharts();
        if (typeof self.wizletInfo.questions !== "undefined" && self.wizletInfo.questions.length) {


            if (self.wizletInfo.isAnswersDistribution) {
                self.calculateDistribution().then(function(data){
                    self.renderChartData.call(self, data, chartOptions, chartElem);
                    redraw.resolve(true);
                });


            } else {
                self.fetchVotes().then(function(data){
                    self.renderChartData.call(self, data, chartOptions, chartElem);
                     redraw.resolve(true);
                });

            }

        }
        return redraw.promise;
    };

    HCWebChart.getRegistration = function () {
        return new HCWebChart();
    };


    function search(tree, propertyName, result) {
        if ($.isArray(tree)) {
            for (var i = 0; i < tree.length; ++i) {
                search(tree[i], propertyName, result);
            }
        } else if ($.isPlainObject(tree)) {
            for (var pName in tree) {
                if (tree.hasOwnProperty(pName)) {
                    var subTree = tree[pName];
                    if (pName == propertyName) {
                        result.push(tree);
                    } else {
                        search(subTree, propertyName, result);
                    }
                }
            }
        }
    };

    function discover(src, propertyName) { 
        var propertyName = propertyName || 'formatter';
        var formatters = [];
        search(src, propertyName, formatters);
        return formatters;
    };

	
	
    return HCWebChart;

});
