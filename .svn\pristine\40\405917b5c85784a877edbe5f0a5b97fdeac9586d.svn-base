<?xml version="1.0" encoding="utf-8" ?>
<Action disableDynamicVotes="false" mainAreaLayout="../../../layout/mainLayout_SIM_R1" layout="../../../layout/tabsLayout2">
  <Include name="Header_SIM_R1"></Include>
  <Include name="KPIgauges"></Include>
  
  
  <!-- Breadcrumbs progress -->
  <Component type="Vanilla">
    <![CDATA[{
      templateInEvent: "html/breadcrumbs.dot",
      css: "styles/breadcrumbs.css",
      animate: "fadeInLeft",
      progress: "1",
      steps: [ "!{SIM_BreadCrumbs_1}",
               "!{SIM_BreadCrumbs_2}" ]
    }]]>
  </Component>

  
  
  <Component type="Tabs" customJS="true"><![CDATA[{
      templateInEvent: "html/tabs.dot",
      css: "styles/tabs.css",
      header: "!{SIM_R1_Scenario1_FB_Header}",
      swipeable: true,
      tabs: [
          "!{Feedback_Opt} !{Choice_Opt1}",
          "!{Feedback_Opt} !{Choice_Opt2}",
          "!{Feedback_Opt} !{Choice_Opt3}",
          "!{Feedback_Opt} !{Choice_Opt4}"
      ],
      correctOption: 0,
      myText: "!{Feedback_MyOpt}",
      isCheck: false,
      bind: "Q_SIM_R1_Scenario1",
      
      trackTeam: "Team",
      isFollower: "Follower",
      scope: ["Follower","Q_SIM_R1_Scenario1"]
  }]]></Component>


  <Component type="Card" customJS="true">
    <![CDATA[{
      templateInEvent: "html/card.dot",
      css: "styles/card.css",
      orientation: "vertical",
      header: "!{}",
      valign: false,
      animate: "fadeIn",
      content: {
        title: "!{SIM_R1_Scenario1_Opt1}",
        body: "!{SIM_R1_Scenario1_FB_Opt1_Text}"
      },
      trackTeam: "Team",
      isFollower: "Follower",
      scope: ["Follower", "Q_SIM_R1_Scenario1"]
    }]]>
  </Component>
  <Component type="Card" customJS="true">
    <![CDATA[{
      templateInEvent: "html/card.dot",
      css: "styles/card.css",
      orientation: "vertical",
      header: "!{}",
      valign: false,
      animate: "fadeIn",
      content: {
        title: "!{SIM_R1_Scenario1_Opt2}",
        body: "!{SIM_R1_Scenario1_FB_Opt2_Text}"
      },
      trackTeam: "Team",
      isFollower: "Follower",
      scope: ["Follower", "Q_SIM_R1_Scenario1"]
    }]]>
  </Component>
  <Component type="Card" customJS="true">
    <![CDATA[{
      templateInEvent: "html/card.dot",
      css: "styles/card.css",
      orientation: "vertical",
      header: "!{}",
      valign: false,
      animate: "fadeIn",
      content: {
        title: "!{SIM_R1_Scenario1_Opt3}",
        body: "!{SIM_R1_Scenario1_FB_Opt3_Text}"
      },
      trackTeam: "Team",
      isFollower: "Follower",
      scope: ["Follower", "Q_SIM_R1_Scenario1"]
    }]]>
  </Component>
  <Component type="Card" customJS="true">
    <![CDATA[{
      templateInEvent: "html/card.dot",
      css: "styles/card.css",
      orientation: "vertical",
      header: "!{}",
      valign: false,
      animate: "fadeIn",
      content: {
        title: "!{SIM_R1_Scenario1_Opt4}",
        body: "!{SIM_R1_Scenario1_FB_Opt4_Text}"
      },
      trackTeam: "Team",
      isFollower: "Follower",
      scope: ["Follower", "Q_SIM_R1_Scenario1"]
    }]]>
  </Component>


  


  <!-- Navigation component
       parameters:
        - aligned:
        - animate: kind of animation
        - id: to be located and shown when is hidden
        - isHidden: attribute to hide
        - buttons: array of buttons (previous, next or target)
   -->
  <!-- <Component type="Navigation" customJS="true">
    <![CDATA[{
      templateInEvent: "html/navigation.dot",
      css: "styles/navigation.css",
      aligned: "right",
      animate: "zoomIn animate__delay-5s",
      id: "navButton",
      _isHidden: true, _showDelay: "5000",
      hasModals: false,
      buttons: [
        {
          type: "next",
          pulse: true,
          gdActionEmbed: "",
          gdActionTrack: "GD",
          gdActionSection: "",
          targetSection: "",
          label: "!{Navigation_next}",
          tooltip: "!{Navigation_next_tt}",
          icon: "forward"
        }
      ],
      
      isFollower: "Follower",
      scope: ["Follower"]
    }]]>
  </Component> -->





</Action>