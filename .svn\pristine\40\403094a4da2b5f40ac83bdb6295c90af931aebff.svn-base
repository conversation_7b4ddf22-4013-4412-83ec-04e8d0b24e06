<?xml version="1.0" encoding="utf-8" ?>
<Action>


  <Component type="IndividualResults" customJS="true"><![CDATA[{
    templateInEvent: "html/rankingTable.dot",
    css: "styles/rankingTable.css",
    class: "",
    _position: "#",
    rank: 
      {
        title: "!{Ranking_Position}",
        binding: "Score_SIM_Total_R2_Rank"
      },
    header: "!{Ranking_Header}",
    userHeader: "!{Ranking_User}",
    _me: "!{Me}",
    boundName: "Q_My_Name",
    showOriginalName: true,
    avatar: " ",
    boundAvatar: "Q_My_Avatar",
    defaultAvatar: "!{defaultAvatar}",
    isDataTables: false,
    class: "_verticalMode_ myresponsive-table _noBold _fixed",
    questions: [
      {
        show: true,
        title: "!{KPI_Metric8}",
        binding: "Score_SIM_Total_R2_KPI8",
				format: "0"
      },
      {
        show: true,
        title: "!{KPI_Metric18}",
        binding: "Score_SIM_Total_R2_KPI18",
				format: "0"
      },
      {
        show: true,
        title: "!{KPI_Metric7}",
        binding: "Score_SIM_Total_R2_KPI7",
				format: "0"
      },
      {
        show: true,
        title: "!{KPI_Score}",
        binding: "Score_SIM_Total_R2",
				format: "0.0"
      }
    ],
    _trackQuestion: "Team",
    rankQuestion: "",
    sortByQuestion: "Score_SIM_Total_R2",
    sortOrder: "desc",
    listSkip: 0,
    listLength:100,
    showNear: 5,
    liveUpdate: false,
    markUpdate: false
  }]]></Component>
  
  
  

</Action>