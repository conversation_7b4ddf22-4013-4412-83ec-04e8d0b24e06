﻿/**
 * Header component
 * 
 * Imports materialize library for:
 *  - sidenav when responsive
 *  - modals screens
 *  - dropdowns sections buttons
 * 
 * Checks if follower to change the name
 * 
 * Loads new screen when popUp clicks
 * 
 * Jump to other screen when data-action links clicked
 */

define(['jquery', 'Q', 'wizer-api', 'wizletBase', 'doT', '../styles/materialize-src/js/materialize.min'], function($, Q, WizerApi, WizletBase, doT) {

    var Header = function() {
        this.type = 'Header';
        this.level = 1;
    };

    Header.prototype.loadHandler = function(unused, wizletInfo, wizletContext, wizerApi) {
        this.wizletInfo = wizletInfo;
        this.wizletContext = wizletContext;
        this.wizerApi = wizerApi;

        this.templateDefer = Q.defer();
        var self = this;
        var requirements = [];
        
        if (wizletInfo.templateInEvent) {
            requirements.push('doT!' + 'events/' + wizerApi.eventName() + '/' + wizletInfo.templateInEvent);
        }
        if (wizletInfo.css) {
            requirements.push(WizletBase.loadCss(wizletInfo));
        }
        require(requirements, function(doTTemplate, css) {
            self.templateDefer.resolve(doTTemplate);
        });

        return WizletBase.loadHandler({ wizlet: this, render: this.render });
    };

    Header.prototype.unloadHandler = function() {
        
        //unload wizletbase
        $('.material-tooltip').remove();
        WizletBase.unloadHandler({ wizlet: this });
    };

    Header.prototype.render = function(options) {
        var self = this;
        return self.templateDefer.promise.then(function(template) {
                var fragment = template(options.wizletInfo);
                options.context.html(fragment);

                //Init Materialize SideNav
                options.context.find('.sidenav').sidenav();
                // options.context.find('.sidenav.panel').width(0);
                
                //Init Materialize Dropdown
                options.context.find(".dropdown-trigger").dropdown({coverTrigger: false, constrainWidth: false});
                //Init Materialize Modal
                options.context.find('.modal').modal({
                    onCloseEnd : function(current_item) {
                        if (options.context.find('video').length > 0)
                            options.context.find('video').get(0).pause();
                    }
                });
                
                //Init Materialize Collapsible for the Glossary
                if (options.wizletInfo.glossary && options.wizletInfo.glossary.collapsible)
                    options.context.find('.collapsible').collapsible();


                //Init materialize tooltip components
                options.context.find('.tooltipped.theader').tooltip( {
                    enterDelay: 1000, html: "theader"
                });

                //Jump to other screen closing the sideNav if opened                
                options.context.find("[data-action]").off("click").on("click", function (e) {
                    var instance = M.Sidenav.getInstance(options.context.find('.sidenav'));
                    instance.close();

                    var target = $(this).data("action");
                    if (target) {
                        if (options.wizletInfo.isMeeting) {
                            self.getMeetings(target).then(function(response){
                                if(response.success) {
                                    self.wizerApi.jump(target);
                                } else {
                                  return true;
                                }
                            });
                        } else {
                            wizerApi.jump(target, false);
                        }
                    }
                });
                
                //Call a function (defined in this JS) by its name (given by parameter to the XML): used for logout
                options.context.find("[data-function]").off("click").on("click", function (e) {
                    self[$(this).data("function")] ($(this).data("question"));
                });
                

                
                //Groupd Director Action: Embed action (as aggregation) within a modal which is opened after the embed action
                options.context.find("[data-gdembed]").on("click", function (e) {
                    
                    var $button = $(this);

                    var embedFile = $button.data("gdembed");
                    var embedIdx = $button.data("index");
                    
                    if (embedFile) {
                        
                        var optionsObj = {actionXML: embedFile};
                        var embedding = require('wizletEmbedding');
                        embedding.unembedWizletExcept('MenuComponent');
                        optionsObj.selector = '#embedding-'+embedIdx;
                        optionsObj.noscroll = true;
                        embedding.embedWizlet({
                            context: $(document),
                            actionScriptName: embedFile,
                            options: optionsObj
                        }).then(function() { 
                            if (typeof $button.data("modal") !== 'undefined') 
                                self.loadPage(
                                    $button.data('modal'), 
                                    options.context, 
                                    self.unsedContext, 
                                    $( $( $button.attr('href')).find('.modal-content') ) 
                                );
                        });
                    }
                    else {
                        self.wizerApi.showError('actionXML attribute missing from options');
                    }
                    
                });

                
                //Load a new page(an action XML) into a modal (if not with embed)
                options.context.find("[data-modal]").on("click", function (e) {
                    
                    if (typeof $(this).data("gdembed") == 'undefined') 
                        self.loadPage(
                            $(this).data('modal'), 
                            options.context, 
                            self.unsedContext, 
                            $( $( $(this).attr('href')).find('.modal-content') ) 
                        );
                });

                
                //Disable current event language
                self.wizerApi.getLanguagesFromVote().then(function (response) {
                    var currentLanguage='';
                    $.each(response.languages, function (index, language) {
                        if (language.CurrentSelection) currentLanguage=language.Key;
                        return;
                    });
                    $.each(options.context.find("a[data-function='language']"), function (index, item) {
                        if (currentLanguage==$(item).data('question')) {
                            $(item).attr('disabled',true);
                            $(item).addClass('disabled');
                        }
                        return;
                    });
                });


                                       
                //The follower takes leader's name
                if (self.iAmFollower()) {
                            
                    //check if the leader had already a name
                    var teamId = self.wizerApi.getQuestionIdByName(options.wizletInfo.trackTeam);
                    var questionId = self.wizerApi.getQuestionIdByName(options.wizletInfo.myName);
                    self.wizerApi.getForemanVotes(teamId,[questionId]).then(function (response) {     
                        if ( response.votes[questionId].length > 0 ) {
                            $('header .participantName').html( response.votes[questionId][0] + options.wizletInfo.follower_suffix );
                        }                                
                    });  
                       
                };

                                  
                //The user takes the meeting ID
                if (options.wizletInfo.isMeeting) { 

                    var trackQuestionId=self.wizerApi.getQuestionIdByName(options.wizletInfo.trackTeam);

                    self.wizerApi.getMyVotes([trackQuestionId]).then(function(res){
                        if(res.success){                            
                            var teamnamevalue = res.responses[trackQuestionId][0].responseText;
                            $('header .participantName').html( $('header .participantName').html() + " [" +options.wizletInfo.meetingIDlabel + ": " + teamnamevalue + "]" );
                        }
                    }); 
                    
                };


                return true;
            })
            .fail(this.wizerApi.showError)
    };


    /**
     * Language: change language of the event
     */
    Header.prototype.language = function (key) {
        var self = this;

        self.wizerApi.setLanguage(key).done(function () {
            window.location.reload(true);
        });
        
    };


    /**
     * Logout: save 1 when logout (used to autojump to the landingpage in the next login)
     */
    Header.prototype.logout = function (questionName) {
        var self = this;
       
        self.addVote(questionName, 1).then(function () {
            wizerApi.logout();
        });
        
    };

    /**
     * Load an action screen inside a modal window
     */
    Header.prototype.loadPage = function (actionXMLName, context, unusedContext, modalContainer) {
        var self = this;

        //unload previous 
        if (self.currentWizletModule && self.currentWizletModule.length > 0) {
            $.each(self.currentWizletModule, function (index, module) {
                if (module.wizletInstance.unloadHandler) {
                    module.wizletInstance.unloadHandler();
                }
            });
        }
        
        // $('.material-tooltip').remove();
        $('.material-tooltip:not(.timage)').remove();
        
        var loading = self.wizerApi.loadActionInContainer(actionXMLName, context, unusedContext, modalContainer);
        loading.then(function (loads) {
           
            self.currentWizletModule = loads;
            
            var page = "wizer:action:init.mainArea";
                        
            $(document).trigger(page, actionXMLName);
        });
        
    };

    /**
     * Check if the user is a follower
     */
    
    Header.prototype.iAmFollower = function () {
        var self = this;
        return (self.wizletInfo.isFollower && 
                self.wizletInfo.DB[self.wizletInfo.isFollower] == 1);
    };

    
    /**
     * Promise function: Addvote value to a questionName
     */
    Header.prototype.addVote = function (questionName, val) {
        var self = this;

        var questionId = self.wizerApi.getQuestionIdByName(questionName);

        var defer = new Q.defer();

        var waiting = self.wizerApi.addVotes({
            votes:[{
                questionId:   questionId, 
                responseText: val
            }]});

        waiting.then(function (result) {            
            defer.resolve(result);
        });                    
        
        return defer.promise;
    };

    Header.prototype.getMeetings = function (actionName) {
        var self = this;

            return $.ajax({
                url: 'Wizer/Meeting/GetAll?actionName=' + actionName,
                contentType: 'application/json',
                type: 'GET',
                datatype: 'json',
                success: function (response) {
                    return response;
                },
                error: function (response) {
                    return response;
                }
            });
        
    };

    Header.getRegistration = function() {
        return new Header();
    };

    return Header;

});