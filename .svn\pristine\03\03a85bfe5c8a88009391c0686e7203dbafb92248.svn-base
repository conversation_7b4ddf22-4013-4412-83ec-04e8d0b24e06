<?xml version="1.0" encoding="utf-8" ?>
  
<Action>

  <Component type="Vanilla">
    <![CDATA[{
      templateInEvent: "html/card.dot",
      css: "styles/card.css",
      orientation: "vertical",
      header: "!{SIM_R2_Intro_Header}",
      valign: false,
      animate: "fadeIn",
      content: {
        img_embed: { 
          materialboxed: false, borders: false, frame: "", nopadding: false,
          src: "!{SIM_R2_Intro_Img}",  alt: "!{SIM_R2_Intro_Title}" ,
          position: "right", style: "",
          src_vert: "!{}",
          animate: "fadeInLeft animate__delay-1s", _animateLater: "bounce"
        },
        _img: { 
          materialboxed: false, borders: false, frame: "", nopadding: false,
          src: "!{SIM_R2_Intro_Img}",  alt: "!{SIM_R2_Intro_Title}" ,
          src_vert: "!{}"
        },
        position: "up",
        animate: "fadeInUp animate__delay-2s",
        title: "!{SIM_R2_Intro1_Title}",
        body: "!{SIM_R2_Intro1_Text}"
      }      
    }]]>
  </Component>
  


</Action>