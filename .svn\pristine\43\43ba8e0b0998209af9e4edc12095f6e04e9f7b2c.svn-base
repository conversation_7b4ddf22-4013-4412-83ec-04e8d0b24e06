{"v": "5.6.4", "fr": 29.9700012207031, "ip": 0, "op": 94.0000038286985, "w": 500, "h": 500, "nm": "3007642 2", "ddd": 1, "assets": [], "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "Layer 2 Outlines", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [136.345, 196.834, 0], "ix": 2}, "a": {"a": 0, "k": [36.171, 24.67, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-3.313, -10.8], [-8.072, 0.244], [-2.04, 0.131], [2.591, 4.501], [6.138, 0.535]], "o": [[0, 0], [8.072, -0.245], [0, 0], [0, 0], [-6.137, -0.534]], "v": [[-19.979, 2.215], [-9.977, 8.34], [20.21, 4.245], [20.701, -4.214], [1.755, -4.599]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.882000014361, 0.910000011968, 0.948999980852, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [36.385, 40.505], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-0.934, 0], [-1.245, 0], [0, 0], [0, -0.778], [-0.312, -0.778], [0.311, -0.623], [0.778, -0.468], [0.623, 0.311], [-0.779, 0.467], [0, 0], [1.402, -0.155], [1.245, 0.623], [0, 0], [0, 0]], "o": [[0, 0], [0.934, 0], [1.245, 0], [0, 0], [0, 0.778], [0.311, 0.778], [-0.312, 0.622], [-0.779, 0.466], [-0.623, -0.311], [0.778, -0.467], [0, 0], [-1.4, 0.156], [-1.245, -0.623], [0, 0], [0, 0]], "v": [[-7.783, -4.903], [-4.203, -4.748], [5.137, -5.37], [6.382, -3.969], [7.627, -1.167], [7.939, 1.479], [7.472, 3.503], [5.604, 4.904], [3.269, 5.059], [3.425, 4.125], [4.825, 3.035], [2.49, 4.125], [-2.335, 3.658], [-5.76, 1.946], [-8.25, 1.635]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.62400004069, 0.458999992819, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [63.842, 42.174], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 3, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [2.172, 2.251], [4.374, -3.23], [-1.129, -3], [-3.323, -2.82], [-0.884, 5.373]], "o": [[0, 0], [-2.647, -2.655], [-4.373, 3.23], [1.13, 3], [3.324, 2.82], [0.885, -5.374]], "v": [[8.625, 2.615], [3.079, -19.238], [-8.309, -20.735], [-12.035, -9.606], [3.517, 21.146], [12.279, 17.557]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.882000014361, 0.910000011968, 0.948999980852, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [13.414, 24.216], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 3, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 94.0000038286985, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "Layer 3 Outlines", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.571}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [192.307, 184.176, 0], "to": [0, -0.013, 0], "ti": [0, 0.069, 0]}, {"i": {"x": 0.833, "y": 0.706}, "o": {"x": 0.167, "y": 0.103}, "t": 1, "s": [192.307, 184.096, 0], "to": [0, -0.069, 0], "ti": [0, 0.195, 0]}, {"i": {"x": 0.833, "y": 0.755}, "o": {"x": 0.167, "y": 0.116}, "t": 2, "s": [192.307, 183.764, 0], "to": [0, -0.195, 0], "ti": [0, 0.411, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.126}, "t": 3, "s": [192.307, 182.923, 0], "to": [0, -0.411, 0], "ti": [0, 0.541, 0]}, {"i": {"x": 0.833, "y": 0.859}, "o": {"x": 0.167, "y": 0.167}, "t": 4, "s": [192.307, 181.298, 0], "to": [0, -0.541, 0], "ti": [0, 0.457, 0]}, {"i": {"x": 0.833, "y": 0.858}, "o": {"x": 0.167, "y": 0.204}, "t": 5, "s": [192.307, 179.675, 0], "to": [0, -0.457, 0], "ti": [0, 0.318, 0]}, {"i": {"x": 0.833, "y": 0.855}, "o": {"x": 0.167, "y": 0.201}, "t": 6, "s": [192.307, 178.558, 0], "to": [0, -0.318, 0], "ti": [0, 0.229, 0]}, {"i": {"x": 0.833, "y": 0.853}, "o": {"x": 0.167, "y": 0.196}, "t": 7, "s": [192.307, 177.768, 0], "to": [0, -0.229, 0], "ti": [0, 0.172, 0]}, {"i": {"x": 0.833, "y": 0.85}, "o": {"x": 0.167, "y": 0.192}, "t": 8, "s": [192.307, 177.183, 0], "to": [0, -0.172, 0], "ti": [0, 0.134, 0]}, {"i": {"x": 0.833, "y": 0.849}, "o": {"x": 0.167, "y": 0.188}, "t": 9, "s": [192.307, 176.734, 0], "to": [0, -0.134, 0], "ti": [0, 0.107, 0]}, {"i": {"x": 0.833, "y": 0.849}, "o": {"x": 0.167, "y": 0.187}, "t": 10, "s": [192.307, 176.377, 0], "to": [0, -0.107, 0], "ti": [0, 0.087, 0]}, {"i": {"x": 0.833, "y": 0.849}, "o": {"x": 0.167, "y": 0.186}, "t": 11, "s": [192.307, 176.09, 0], "to": [0, -0.087, 0], "ti": [0, 0.07, 0]}, {"i": {"x": 0.833, "y": 0.85}, "o": {"x": 0.167, "y": 0.187}, "t": 12, "s": [192.307, 175.857, 0], "to": [0, -0.07, 0], "ti": [0, 0.056, 0]}, {"i": {"x": 0.833, "y": 0.852}, "o": {"x": 0.167, "y": 0.188}, "t": 13, "s": [192.307, 175.669, 0], "to": [0, -0.056, 0], "ti": [0, 0.044, 0]}, {"i": {"x": 0.833, "y": 0.854}, "o": {"x": 0.167, "y": 0.19}, "t": 14, "s": [192.307, 175.519, 0], "to": [0, -0.044, 0], "ti": [0, 0.034, 0]}, {"i": {"x": 0.833, "y": 0.856}, "o": {"x": 0.167, "y": 0.193}, "t": 15, "s": [192.307, 175.402, 0], "to": [0, -0.034, 0], "ti": [0, 0.025, 0]}, {"i": {"x": 0.833, "y": 0.861}, "o": {"x": 0.167, "y": 0.199}, "t": 16, "s": [192.307, 175.314, 0], "to": [0, -0.025, 0], "ti": [0, 0.018, 0]}, {"i": {"x": 0.833, "y": 0.87}, "o": {"x": 0.167, "y": 0.208}, "t": 17, "s": [192.307, 175.25, 0], "to": [0, -0.018, 0], "ti": [0, 0.011, 0]}, {"i": {"x": 0.833, "y": 0.89}, "o": {"x": 0.167, "y": 0.231}, "t": 18, "s": [192.307, 175.207, 0], "to": [0, -0.011, 0], "ti": [0, 0.005, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.347}, "t": 19, "s": [192.307, 175.183, 0], "to": [0, -0.005, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.653}, "o": {"x": 0.167, "y": 0.167}, "t": 20, "s": [192.307, 175.176, 0], "to": [0, 0, 0], "ti": [0, -0.005, 0]}, {"i": {"x": 0.833, "y": 0.769}, "o": {"x": 0.167, "y": 0.11}, "t": 21, "s": [192.307, 175.183, 0], "to": [0, 0.005, 0], "ti": [0, -0.011, 0]}, {"i": {"x": 0.833, "y": 0.792}, "o": {"x": 0.167, "y": 0.13}, "t": 22, "s": [192.307, 175.207, 0], "to": [0, 0.011, 0], "ti": [0, -0.018, 0]}, {"i": {"x": 0.833, "y": 0.801}, "o": {"x": 0.167, "y": 0.139}, "t": 23, "s": [192.307, 175.25, 0], "to": [0, 0.018, 0], "ti": [0, -0.025, 0]}, {"i": {"x": 0.833, "y": 0.807}, "o": {"x": 0.167, "y": 0.144}, "t": 24, "s": [192.307, 175.314, 0], "to": [0, 0.025, 0], "ti": [0, -0.034, 0]}, {"i": {"x": 0.833, "y": 0.81}, "o": {"x": 0.167, "y": 0.146}, "t": 25, "s": [192.307, 175.402, 0], "to": [0, 0.034, 0], "ti": [0, -0.044, 0]}, {"i": {"x": 0.833, "y": 0.812}, "o": {"x": 0.167, "y": 0.148}, "t": 26, "s": [192.307, 175.519, 0], "to": [0, 0.044, 0], "ti": [0, -0.056, 0]}, {"i": {"x": 0.833, "y": 0.813}, "o": {"x": 0.167, "y": 0.15}, "t": 27, "s": [192.307, 175.669, 0], "to": [0, 0.056, 0], "ti": [0, -0.07, 0]}, {"i": {"x": 0.833, "y": 0.814}, "o": {"x": 0.167, "y": 0.151}, "t": 28, "s": [192.307, 175.857, 0], "to": [0, 0.07, 0], "ti": [0, -0.087, 0]}, {"i": {"x": 0.833, "y": 0.813}, "o": {"x": 0.167, "y": 0.151}, "t": 29, "s": [192.307, 176.09, 0], "to": [0, 0.087, 0], "ti": [0, -0.107, 0]}, {"i": {"x": 0.833, "y": 0.812}, "o": {"x": 0.167, "y": 0.151}, "t": 30, "s": [192.307, 176.377, 0], "to": [0, 0.107, 0], "ti": [0, -0.134, 0]}, {"i": {"x": 0.833, "y": 0.808}, "o": {"x": 0.167, "y": 0.15}, "t": 31, "s": [192.307, 176.734, 0], "to": [0, 0.134, 0], "ti": [0, -0.172, 0]}, {"i": {"x": 0.833, "y": 0.804}, "o": {"x": 0.167, "y": 0.147}, "t": 32, "s": [192.307, 177.183, 0], "to": [0, 0.172, 0], "ti": [0, -0.229, 0]}, {"i": {"x": 0.833, "y": 0.799}, "o": {"x": 0.167, "y": 0.145}, "t": 33, "s": [192.307, 177.768, 0], "to": [0, 0.229, 0], "ti": [0, -0.318, 0]}, {"i": {"x": 0.833, "y": 0.796}, "o": {"x": 0.167, "y": 0.142}, "t": 34, "s": [192.307, 178.558, 0], "to": [0, 0.318, 0], "ti": [0, -0.457, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.141}, "t": 35, "s": [192.307, 179.675, 0], "to": [0, 0.457, 0], "ti": [0, -0.541, 0]}, {"i": {"x": 0.833, "y": 0.874}, "o": {"x": 0.167, "y": 0.167}, "t": 36, "s": [192.307, 181.298, 0], "to": [0, 0.541, 0], "ti": [0, -0.411, 0]}, {"i": {"x": 0.833, "y": 0.884}, "o": {"x": 0.167, "y": 0.245}, "t": 37, "s": [192.307, 182.923, 0], "to": [0, 0.411, 0], "ti": [0, -0.195, 0]}, {"i": {"x": 0.833, "y": 0.897}, "o": {"x": 0.167, "y": 0.294}, "t": 38, "s": [192.307, 183.764, 0], "to": [0, 0.195, 0], "ti": [0, -0.069, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.429}, "t": 39, "s": [192.307, 184.096, 0], "to": [0, 0.069, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.571}, "o": {"x": 0.167, "y": 0.167}, "t": 40, "s": [192.307, 184.176, 0], "to": [0, 0, 0], "ti": [0, 0.069, 0]}, {"i": {"x": 0.833, "y": 0.706}, "o": {"x": 0.167, "y": 0.103}, "t": 41, "s": [192.307, 184.096, 0], "to": [0, -0.069, 0], "ti": [0, 0.195, 0]}, {"i": {"x": 0.833, "y": 0.755}, "o": {"x": 0.167, "y": 0.116}, "t": 42, "s": [192.307, 183.764, 0], "to": [0, -0.195, 0], "ti": [0, 0.411, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.126}, "t": 43, "s": [192.307, 182.923, 0], "to": [0, -0.411, 0], "ti": [0, 0.541, 0]}, {"i": {"x": 0.833, "y": 0.859}, "o": {"x": 0.167, "y": 0.167}, "t": 44, "s": [192.307, 181.298, 0], "to": [0, -0.541, 0], "ti": [0, 0.457, 0]}, {"i": {"x": 0.833, "y": 0.858}, "o": {"x": 0.167, "y": 0.204}, "t": 45, "s": [192.307, 179.675, 0], "to": [0, -0.457, 0], "ti": [0, 0.318, 0]}, {"i": {"x": 0.833, "y": 0.855}, "o": {"x": 0.167, "y": 0.201}, "t": 46, "s": [192.307, 178.558, 0], "to": [0, -0.318, 0], "ti": [0, 0.229, 0]}, {"i": {"x": 0.833, "y": 0.853}, "o": {"x": 0.167, "y": 0.196}, "t": 47, "s": [192.307, 177.768, 0], "to": [0, -0.229, 0], "ti": [0, 0.172, 0]}, {"i": {"x": 0.833, "y": 0.85}, "o": {"x": 0.167, "y": 0.192}, "t": 48, "s": [192.307, 177.183, 0], "to": [0, -0.172, 0], "ti": [0, 0.134, 0]}, {"i": {"x": 0.833, "y": 0.849}, "o": {"x": 0.167, "y": 0.188}, "t": 49, "s": [192.307, 176.734, 0], "to": [0, -0.134, 0], "ti": [0, 0.107, 0]}, {"i": {"x": 0.833, "y": 0.849}, "o": {"x": 0.167, "y": 0.187}, "t": 50, "s": [192.307, 176.377, 0], "to": [0, -0.107, 0], "ti": [0, 0.087, 0]}, {"i": {"x": 0.833, "y": 0.849}, "o": {"x": 0.167, "y": 0.186}, "t": 51, "s": [192.307, 176.09, 0], "to": [0, -0.087, 0], "ti": [0, 0.07, 0]}, {"i": {"x": 0.833, "y": 0.85}, "o": {"x": 0.167, "y": 0.187}, "t": 52, "s": [192.307, 175.857, 0], "to": [0, -0.07, 0], "ti": [0, 0.056, 0]}, {"i": {"x": 0.833, "y": 0.852}, "o": {"x": 0.167, "y": 0.188}, "t": 53, "s": [192.307, 175.669, 0], "to": [0, -0.056, 0], "ti": [0, 0.044, 0]}, {"i": {"x": 0.833, "y": 0.854}, "o": {"x": 0.167, "y": 0.19}, "t": 54, "s": [192.307, 175.519, 0], "to": [0, -0.044, 0], "ti": [0, 0.034, 0]}, {"i": {"x": 0.833, "y": 0.856}, "o": {"x": 0.167, "y": 0.193}, "t": 55, "s": [192.307, 175.402, 0], "to": [0, -0.034, 0], "ti": [0, 0.025, 0]}, {"i": {"x": 0.833, "y": 0.861}, "o": {"x": 0.167, "y": 0.199}, "t": 56, "s": [192.307, 175.314, 0], "to": [0, -0.025, 0], "ti": [0, 0.018, 0]}, {"i": {"x": 0.833, "y": 0.87}, "o": {"x": 0.167, "y": 0.208}, "t": 57, "s": [192.307, 175.25, 0], "to": [0, -0.018, 0], "ti": [0, 0.011, 0]}, {"i": {"x": 0.833, "y": 0.89}, "o": {"x": 0.167, "y": 0.231}, "t": 58, "s": [192.307, 175.207, 0], "to": [0, -0.011, 0], "ti": [0, 0.005, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.347}, "t": 59, "s": [192.307, 175.183, 0], "to": [0, -0.005, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.653}, "o": {"x": 0.167, "y": 0.167}, "t": 60, "s": [192.307, 175.176, 0], "to": [0, 0, 0], "ti": [0, -0.005, 0]}, {"i": {"x": 0.833, "y": 0.769}, "o": {"x": 0.167, "y": 0.11}, "t": 61, "s": [192.307, 175.183, 0], "to": [0, 0.005, 0], "ti": [0, -0.011, 0]}, {"i": {"x": 0.833, "y": 0.792}, "o": {"x": 0.167, "y": 0.13}, "t": 62, "s": [192.307, 175.207, 0], "to": [0, 0.011, 0], "ti": [0, -0.018, 0]}, {"i": {"x": 0.833, "y": 0.801}, "o": {"x": 0.167, "y": 0.139}, "t": 63, "s": [192.307, 175.25, 0], "to": [0, 0.018, 0], "ti": [0, -0.025, 0]}, {"i": {"x": 0.833, "y": 0.807}, "o": {"x": 0.167, "y": 0.144}, "t": 64, "s": [192.307, 175.314, 0], "to": [0, 0.025, 0], "ti": [0, -0.034, 0]}, {"i": {"x": 0.833, "y": 0.81}, "o": {"x": 0.167, "y": 0.146}, "t": 65, "s": [192.307, 175.402, 0], "to": [0, 0.034, 0], "ti": [0, -0.044, 0]}, {"i": {"x": 0.833, "y": 0.812}, "o": {"x": 0.167, "y": 0.148}, "t": 66, "s": [192.307, 175.519, 0], "to": [0, 0.044, 0], "ti": [0, -0.056, 0]}, {"i": {"x": 0.833, "y": 0.813}, "o": {"x": 0.167, "y": 0.15}, "t": 67, "s": [192.307, 175.669, 0], "to": [0, 0.056, 0], "ti": [0, -0.07, 0]}, {"i": {"x": 0.833, "y": 0.814}, "o": {"x": 0.167, "y": 0.151}, "t": 68, "s": [192.307, 175.857, 0], "to": [0, 0.07, 0], "ti": [0, -0.087, 0]}, {"i": {"x": 0.833, "y": 0.813}, "o": {"x": 0.167, "y": 0.151}, "t": 69, "s": [192.307, 176.09, 0], "to": [0, 0.087, 0], "ti": [0, -0.107, 0]}, {"i": {"x": 0.833, "y": 0.812}, "o": {"x": 0.167, "y": 0.151}, "t": 70, "s": [192.307, 176.377, 0], "to": [0, 0.107, 0], "ti": [0, -0.134, 0]}, {"i": {"x": 0.833, "y": 0.808}, "o": {"x": 0.167, "y": 0.15}, "t": 71, "s": [192.307, 176.734, 0], "to": [0, 0.134, 0], "ti": [0, -0.172, 0]}, {"i": {"x": 0.833, "y": 0.804}, "o": {"x": 0.167, "y": 0.147}, "t": 72, "s": [192.307, 177.183, 0], "to": [0, 0.172, 0], "ti": [0, -0.229, 0]}, {"i": {"x": 0.833, "y": 0.799}, "o": {"x": 0.167, "y": 0.145}, "t": 73, "s": [192.307, 177.768, 0], "to": [0, 0.229, 0], "ti": [0, -0.318, 0]}, {"i": {"x": 0.833, "y": 0.796}, "o": {"x": 0.167, "y": 0.142}, "t": 74, "s": [192.307, 178.558, 0], "to": [0, 0.318, 0], "ti": [0, -0.457, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.141}, "t": 75, "s": [192.307, 179.675, 0], "to": [0, 0.457, 0], "ti": [0, -0.541, 0]}, {"i": {"x": 0.833, "y": 0.874}, "o": {"x": 0.167, "y": 0.167}, "t": 76, "s": [192.307, 181.298, 0], "to": [0, 0.541, 0], "ti": [0, -0.411, 0]}, {"i": {"x": 0.833, "y": 0.884}, "o": {"x": 0.167, "y": 0.245}, "t": 77, "s": [192.307, 182.923, 0], "to": [0, 0.411, 0], "ti": [0, -0.195, 0]}, {"i": {"x": 0.833, "y": 0.897}, "o": {"x": 0.167, "y": 0.294}, "t": 78, "s": [192.307, 183.764, 0], "to": [0, 0.195, 0], "ti": [0, -0.069, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.429}, "t": 79, "s": [192.307, 184.096, 0], "to": [0, 0.069, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.571}, "o": {"x": 0.167, "y": 0.167}, "t": 80, "s": [192.307, 184.176, 0], "to": [0, 0, 0], "ti": [0, 0.069, 0]}, {"i": {"x": 0.833, "y": 0.706}, "o": {"x": 0.167, "y": 0.103}, "t": 81, "s": [192.307, 184.096, 0], "to": [0, -0.069, 0], "ti": [0, 0.195, 0]}, {"i": {"x": 0.833, "y": 0.755}, "o": {"x": 0.167, "y": 0.116}, "t": 82, "s": [192.307, 183.764, 0], "to": [0, -0.195, 0], "ti": [0, 0.411, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.126}, "t": 83, "s": [192.307, 182.923, 0], "to": [0, -0.411, 0], "ti": [0, 0.541, 0]}, {"i": {"x": 0.833, "y": 0.859}, "o": {"x": 0.167, "y": 0.167}, "t": 84, "s": [192.307, 181.298, 0], "to": [0, -0.541, 0], "ti": [0, 0.457, 0]}, {"i": {"x": 0.833, "y": 0.858}, "o": {"x": 0.167, "y": 0.204}, "t": 85, "s": [192.307, 179.675, 0], "to": [0, -0.457, 0], "ti": [0, 0.318, 0]}, {"i": {"x": 0.833, "y": 0.855}, "o": {"x": 0.167, "y": 0.201}, "t": 86, "s": [192.307, 178.558, 0], "to": [0, -0.318, 0], "ti": [0, 0.229, 0]}, {"i": {"x": 0.833, "y": 0.853}, "o": {"x": 0.167, "y": 0.196}, "t": 87, "s": [192.307, 177.768, 0], "to": [0, -0.229, 0], "ti": [0, 0.172, 0]}, {"i": {"x": 0.833, "y": 0.85}, "o": {"x": 0.167, "y": 0.192}, "t": 88, "s": [192.307, 177.183, 0], "to": [0, -0.172, 0], "ti": [0, 0.134, 0]}, {"i": {"x": 0.833, "y": 0.849}, "o": {"x": 0.167, "y": 0.188}, "t": 89, "s": [192.307, 176.734, 0], "to": [0, -0.134, 0], "ti": [0, 0.107, 0]}, {"i": {"x": 0.833, "y": 0.849}, "o": {"x": 0.167, "y": 0.187}, "t": 90, "s": [192.307, 176.377, 0], "to": [0, -0.107, 0], "ti": [0, 0.087, 0]}, {"i": {"x": 0.833, "y": 0.849}, "o": {"x": 0.167, "y": 0.186}, "t": 91, "s": [192.307, 176.09, 0], "to": [0, -0.087, 0], "ti": [0, 0.07, 0]}, {"i": {"x": 0.833, "y": 0.85}, "o": {"x": 0.167, "y": 0.187}, "t": 92, "s": [192.307, 175.857, 0], "to": [0, -0.07, 0], "ti": [0, 0.056, 0]}, {"i": {"x": 0.833, "y": 0.852}, "o": {"x": 0.167, "y": 0.188}, "t": 93, "s": [192.307, 175.669, 0], "to": [0, -0.056, 0], "ti": [0, 0.044, 0]}, {"i": {"x": 0.833, "y": 0.854}, "o": {"x": 0.167, "y": 0.19}, "t": 94, "s": [192.307, 175.519, 0], "to": [0, -0.044, 0], "ti": [0, 0.034, 0]}, {"i": {"x": 0.833, "y": 0.856}, "o": {"x": 0.167, "y": 0.193}, "t": 95, "s": [192.307, 175.402, 0], "to": [0, -0.034, 0], "ti": [0, 0.025, 0]}, {"i": {"x": 0.833, "y": 0.861}, "o": {"x": 0.167, "y": 0.199}, "t": 96, "s": [192.307, 175.314, 0], "to": [0, -0.025, 0], "ti": [0, 0.018, 0]}, {"i": {"x": 0.833, "y": 0.87}, "o": {"x": 0.167, "y": 0.208}, "t": 97, "s": [192.307, 175.25, 0], "to": [0, -0.018, 0], "ti": [0, 0.011, 0]}, {"i": {"x": 0.833, "y": 0.89}, "o": {"x": 0.167, "y": 0.231}, "t": 98, "s": [192.307, 175.207, 0], "to": [0, -0.011, 0], "ti": [0, 0.005, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.347}, "t": 99, "s": [192.307, 175.183, 0], "to": [0, -0.005, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.653}, "o": {"x": 0.167, "y": 0.167}, "t": 100, "s": [192.307, 175.176, 0], "to": [0, 0, 0], "ti": [0, -0.005, 0]}, {"i": {"x": 0.833, "y": 0.769}, "o": {"x": 0.167, "y": 0.11}, "t": 101, "s": [192.307, 175.183, 0], "to": [0, 0.005, 0], "ti": [0, -0.011, 0]}, {"i": {"x": 0.833, "y": 0.792}, "o": {"x": 0.167, "y": 0.13}, "t": 102, "s": [192.307, 175.207, 0], "to": [0, 0.011, 0], "ti": [0, -0.018, 0]}, {"i": {"x": 0.833, "y": 0.801}, "o": {"x": 0.167, "y": 0.139}, "t": 103, "s": [192.307, 175.25, 0], "to": [0, 0.018, 0], "ti": [0, -0.025, 0]}, {"i": {"x": 0.833, "y": 0.807}, "o": {"x": 0.167, "y": 0.144}, "t": 104, "s": [192.307, 175.314, 0], "to": [0, 0.025, 0], "ti": [0, -0.034, 0]}, {"i": {"x": 0.833, "y": 0.81}, "o": {"x": 0.167, "y": 0.146}, "t": 105, "s": [192.307, 175.402, 0], "to": [0, 0.034, 0], "ti": [0, -0.044, 0]}, {"i": {"x": 0.833, "y": 0.812}, "o": {"x": 0.167, "y": 0.148}, "t": 106, "s": [192.307, 175.519, 0], "to": [0, 0.044, 0], "ti": [0, -0.056, 0]}, {"i": {"x": 0.833, "y": 0.813}, "o": {"x": 0.167, "y": 0.15}, "t": 107, "s": [192.307, 175.669, 0], "to": [0, 0.056, 0], "ti": [0, -0.07, 0]}, {"i": {"x": 0.833, "y": 0.814}, "o": {"x": 0.167, "y": 0.151}, "t": 108, "s": [192.307, 175.857, 0], "to": [0, 0.07, 0], "ti": [0, -0.087, 0]}, {"i": {"x": 0.833, "y": 0.813}, "o": {"x": 0.167, "y": 0.151}, "t": 109, "s": [192.307, 176.09, 0], "to": [0, 0.087, 0], "ti": [0, -0.107, 0]}, {"i": {"x": 0.833, "y": 0.812}, "o": {"x": 0.167, "y": 0.151}, "t": 110, "s": [192.307, 176.377, 0], "to": [0, 0.107, 0], "ti": [0, -0.134, 0]}, {"i": {"x": 0.833, "y": 0.808}, "o": {"x": 0.167, "y": 0.15}, "t": 111, "s": [192.307, 176.734, 0], "to": [0, 0.134, 0], "ti": [0, -0.172, 0]}, {"i": {"x": 0.833, "y": 0.804}, "o": {"x": 0.167, "y": 0.147}, "t": 112, "s": [192.307, 177.183, 0], "to": [0, 0.172, 0], "ti": [0, -0.229, 0]}, {"i": {"x": 0.833, "y": 0.799}, "o": {"x": 0.167, "y": 0.145}, "t": 113, "s": [192.307, 177.768, 0], "to": [0, 0.229, 0], "ti": [0, -0.318, 0]}, {"i": {"x": 0.833, "y": 0.796}, "o": {"x": 0.167, "y": 0.142}, "t": 114, "s": [192.307, 178.558, 0], "to": [0, 0.318, 0], "ti": [0, -0.457, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.141}, "t": 115, "s": [192.307, 179.675, 0], "to": [0, 0.457, 0], "ti": [0, -0.541, 0]}, {"i": {"x": 0.833, "y": 0.874}, "o": {"x": 0.167, "y": 0.167}, "t": 116, "s": [192.307, 181.298, 0], "to": [0, 0.541, 0], "ti": [0, -0.411, 0]}, {"i": {"x": 0.833, "y": 0.884}, "o": {"x": 0.167, "y": 0.245}, "t": 117, "s": [192.307, 182.923, 0], "to": [0, 0.411, 0], "ti": [0, -0.195, 0]}, {"i": {"x": 0.833, "y": 0.897}, "o": {"x": 0.167, "y": 0.294}, "t": 118, "s": [192.307, 183.764, 0], "to": [0, 0.195, 0], "ti": [0, -0.069, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.429}, "t": 119, "s": [192.307, 184.096, 0], "to": [0, 0.069, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.571}, "o": {"x": 0.167, "y": 0.167}, "t": 120, "s": [192.307, 184.176, 0], "to": [0, 0, 0], "ti": [0, 0.069, 0]}, {"i": {"x": 0.833, "y": 0.706}, "o": {"x": 0.167, "y": 0.103}, "t": 121, "s": [192.307, 184.096, 0], "to": [0, -0.069, 0], "ti": [0, 0.195, 0]}, {"i": {"x": 0.833, "y": 0.755}, "o": {"x": 0.167, "y": 0.116}, "t": 122, "s": [192.307, 183.764, 0], "to": [0, -0.195, 0], "ti": [0, 0.411, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.126}, "t": 123, "s": [192.307, 182.923, 0], "to": [0, -0.411, 0], "ti": [0, 0.541, 0]}, {"i": {"x": 0.833, "y": 0.859}, "o": {"x": 0.167, "y": 0.167}, "t": 124, "s": [192.307, 181.298, 0], "to": [0, -0.541, 0], "ti": [0, 0.457, 0]}, {"i": {"x": 0.833, "y": 0.858}, "o": {"x": 0.167, "y": 0.204}, "t": 125, "s": [192.307, 179.675, 0], "to": [0, -0.457, 0], "ti": [0, 0.318, 0]}, {"i": {"x": 0.833, "y": 0.855}, "o": {"x": 0.167, "y": 0.201}, "t": 126, "s": [192.307, 178.558, 0], "to": [0, -0.318, 0], "ti": [0, 0.229, 0]}, {"i": {"x": 0.833, "y": 0.853}, "o": {"x": 0.167, "y": 0.196}, "t": 127, "s": [192.307, 177.768, 0], "to": [0, -0.229, 0], "ti": [0, 0.172, 0]}, {"i": {"x": 0.833, "y": 0.85}, "o": {"x": 0.167, "y": 0.192}, "t": 128, "s": [192.307, 177.183, 0], "to": [0, -0.172, 0], "ti": [0, 0.134, 0]}, {"i": {"x": 0.833, "y": 0.849}, "o": {"x": 0.167, "y": 0.188}, "t": 129, "s": [192.307, 176.734, 0], "to": [0, -0.134, 0], "ti": [0, 0.107, 0]}, {"i": {"x": 0.833, "y": 0.849}, "o": {"x": 0.167, "y": 0.187}, "t": 130, "s": [192.307, 176.377, 0], "to": [0, -0.107, 0], "ti": [0, 0.087, 0]}, {"i": {"x": 0.833, "y": 0.849}, "o": {"x": 0.167, "y": 0.186}, "t": 131, "s": [192.307, 176.09, 0], "to": [0, -0.087, 0], "ti": [0, 0.07, 0]}, {"i": {"x": 0.833, "y": 0.85}, "o": {"x": 0.167, "y": 0.187}, "t": 132, "s": [192.307, 175.857, 0], "to": [0, -0.07, 0], "ti": [0, 0.056, 0]}, {"i": {"x": 0.833, "y": 0.852}, "o": {"x": 0.167, "y": 0.188}, "t": 133, "s": [192.307, 175.669, 0], "to": [0, -0.056, 0], "ti": [0, 0.044, 0]}, {"i": {"x": 0.833, "y": 0.854}, "o": {"x": 0.167, "y": 0.19}, "t": 134, "s": [192.307, 175.519, 0], "to": [0, -0.044, 0], "ti": [0, 0.034, 0]}, {"i": {"x": 0.833, "y": 0.856}, "o": {"x": 0.167, "y": 0.193}, "t": 135, "s": [192.307, 175.402, 0], "to": [0, -0.034, 0], "ti": [0, 0.025, 0]}, {"i": {"x": 0.833, "y": 0.861}, "o": {"x": 0.167, "y": 0.199}, "t": 136, "s": [192.307, 175.314, 0], "to": [0, -0.025, 0], "ti": [0, 0.018, 0]}, {"i": {"x": 0.833, "y": 0.87}, "o": {"x": 0.167, "y": 0.208}, "t": 137, "s": [192.307, 175.25, 0], "to": [0, -0.018, 0], "ti": [0, 0.011, 0]}, {"i": {"x": 0.833, "y": 0.89}, "o": {"x": 0.167, "y": 0.231}, "t": 138, "s": [192.307, 175.207, 0], "to": [0, -0.011, 0], "ti": [0, 0.005, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.347}, "t": 139, "s": [192.307, 175.183, 0], "to": [0, -0.005, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.653}, "o": {"x": 0.167, "y": 0.167}, "t": 140, "s": [192.307, 175.176, 0], "to": [0, 0, 0], "ti": [0, -0.005, 0]}, {"i": {"x": 0.833, "y": 0.769}, "o": {"x": 0.167, "y": 0.11}, "t": 141, "s": [192.307, 175.183, 0], "to": [0, 0.005, 0], "ti": [0, -0.011, 0]}, {"i": {"x": 0.833, "y": 0.792}, "o": {"x": 0.167, "y": 0.13}, "t": 142, "s": [192.307, 175.207, 0], "to": [0, 0.011, 0], "ti": [0, -0.018, 0]}, {"i": {"x": 0.833, "y": 0.801}, "o": {"x": 0.167, "y": 0.139}, "t": 143, "s": [192.307, 175.25, 0], "to": [0, 0.018, 0], "ti": [0, -0.025, 0]}, {"i": {"x": 0.833, "y": 0.807}, "o": {"x": 0.167, "y": 0.144}, "t": 144, "s": [192.307, 175.314, 0], "to": [0, 0.025, 0], "ti": [0, -0.034, 0]}, {"i": {"x": 0.833, "y": 0.81}, "o": {"x": 0.167, "y": 0.146}, "t": 145, "s": [192.307, 175.402, 0], "to": [0, 0.034, 0], "ti": [0, -0.044, 0]}, {"i": {"x": 0.833, "y": 0.812}, "o": {"x": 0.167, "y": 0.148}, "t": 146, "s": [192.307, 175.519, 0], "to": [0, 0.044, 0], "ti": [0, -0.056, 0]}, {"i": {"x": 0.833, "y": 0.813}, "o": {"x": 0.167, "y": 0.15}, "t": 147, "s": [192.307, 175.669, 0], "to": [0, 0.056, 0], "ti": [0, -0.07, 0]}, {"i": {"x": 0.833, "y": 0.814}, "o": {"x": 0.167, "y": 0.151}, "t": 148, "s": [192.307, 175.857, 0], "to": [0, 0.07, 0], "ti": [0, -0.087, 0]}, {"i": {"x": 0.833, "y": 0.813}, "o": {"x": 0.167, "y": 0.151}, "t": 149, "s": [192.307, 176.09, 0], "to": [0, 0.087, 0], "ti": [0, -0.107, 0]}, {"i": {"x": 0.833, "y": 0.812}, "o": {"x": 0.167, "y": 0.151}, "t": 150, "s": [192.307, 176.377, 0], "to": [0, 0.107, 0], "ti": [0, -0.134, 0]}, {"i": {"x": 0.833, "y": 0.808}, "o": {"x": 0.167, "y": 0.15}, "t": 151, "s": [192.307, 176.734, 0], "to": [0, 0.134, 0], "ti": [0, -0.172, 0]}, {"i": {"x": 0.833, "y": 0.804}, "o": {"x": 0.167, "y": 0.147}, "t": 152, "s": [192.307, 177.183, 0], "to": [0, 0.172, 0], "ti": [0, -0.229, 0]}, {"i": {"x": 0.833, "y": 0.799}, "o": {"x": 0.167, "y": 0.145}, "t": 153, "s": [192.307, 177.768, 0], "to": [0, 0.229, 0], "ti": [0, -0.318, 0]}, {"i": {"x": 0.833, "y": 0.796}, "o": {"x": 0.167, "y": 0.142}, "t": 154, "s": [192.307, 178.558, 0], "to": [0, 0.318, 0], "ti": [0, -0.457, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.141}, "t": 155, "s": [192.307, 179.675, 0], "to": [0, 0.457, 0], "ti": [0, -0.541, 0]}, {"i": {"x": 0.833, "y": 0.874}, "o": {"x": 0.167, "y": 0.167}, "t": 156, "s": [192.307, 181.298, 0], "to": [0, 0.541, 0], "ti": [0, -0.411, 0]}, {"i": {"x": 0.833, "y": 0.884}, "o": {"x": 0.167, "y": 0.245}, "t": 157, "s": [192.307, 182.923, 0], "to": [0, 0.411, 0], "ti": [0, -0.195, 0]}, {"i": {"x": 0.833, "y": 0.897}, "o": {"x": 0.167, "y": 0.294}, "t": 158, "s": [192.307, 183.764, 0], "to": [0, 0.195, 0], "ti": [0, -0.069, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.429}, "t": 159, "s": [192.307, 184.096, 0], "to": [0, 0.069, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.571}, "o": {"x": 0.167, "y": 0.167}, "t": 160, "s": [192.307, 184.176, 0], "to": [0, 0, 0], "ti": [0, 0.069, 0]}, {"i": {"x": 0.833, "y": 0.706}, "o": {"x": 0.167, "y": 0.103}, "t": 161, "s": [192.307, 184.096, 0], "to": [0, -0.069, 0], "ti": [0, 0.195, 0]}, {"i": {"x": 0.833, "y": 0.755}, "o": {"x": 0.167, "y": 0.116}, "t": 162, "s": [192.307, 183.764, 0], "to": [0, -0.195, 0], "ti": [0, 0.411, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.126}, "t": 163, "s": [192.307, 182.923, 0], "to": [0, -0.411, 0], "ti": [0, 0.541, 0]}, {"i": {"x": 0.833, "y": 0.859}, "o": {"x": 0.167, "y": 0.167}, "t": 164, "s": [192.307, 181.298, 0], "to": [0, -0.541, 0], "ti": [0, 0.457, 0]}, {"i": {"x": 0.833, "y": 0.858}, "o": {"x": 0.167, "y": 0.204}, "t": 165, "s": [192.307, 179.675, 0], "to": [0, -0.457, 0], "ti": [0, 0.318, 0]}, {"i": {"x": 0.833, "y": 0.855}, "o": {"x": 0.167, "y": 0.201}, "t": 166, "s": [192.307, 178.558, 0], "to": [0, -0.318, 0], "ti": [0, 0.229, 0]}, {"i": {"x": 0.833, "y": 0.853}, "o": {"x": 0.167, "y": 0.196}, "t": 167, "s": [192.307, 177.768, 0], "to": [0, -0.229, 0], "ti": [0, 0.172, 0]}, {"i": {"x": 0.833, "y": 0.85}, "o": {"x": 0.167, "y": 0.192}, "t": 168, "s": [192.307, 177.183, 0], "to": [0, -0.172, 0], "ti": [0, 0.134, 0]}, {"i": {"x": 0.833, "y": 0.849}, "o": {"x": 0.167, "y": 0.188}, "t": 169, "s": [192.307, 176.734, 0], "to": [0, -0.134, 0], "ti": [0, 0.107, 0]}, {"i": {"x": 0.833, "y": 0.849}, "o": {"x": 0.167, "y": 0.187}, "t": 170, "s": [192.307, 176.377, 0], "to": [0, -0.107, 0], "ti": [0, 0.087, 0]}, {"i": {"x": 0.833, "y": 0.849}, "o": {"x": 0.167, "y": 0.186}, "t": 171, "s": [192.307, 176.09, 0], "to": [0, -0.087, 0], "ti": [0, 0.07, 0]}, {"i": {"x": 0.833, "y": 0.85}, "o": {"x": 0.167, "y": 0.187}, "t": 172, "s": [192.307, 175.857, 0], "to": [0, -0.07, 0], "ti": [0, 0.056, 0]}, {"i": {"x": 0.833, "y": 0.852}, "o": {"x": 0.167, "y": 0.188}, "t": 173, "s": [192.307, 175.669, 0], "to": [0, -0.056, 0], "ti": [0, 0.044, 0]}, {"i": {"x": 0.833, "y": 0.854}, "o": {"x": 0.167, "y": 0.19}, "t": 174, "s": [192.307, 175.519, 0], "to": [0, -0.044, 0], "ti": [0, 0.034, 0]}, {"i": {"x": 0.833, "y": 0.856}, "o": {"x": 0.167, "y": 0.193}, "t": 175, "s": [192.307, 175.402, 0], "to": [0, -0.034, 0], "ti": [0, 0.025, 0]}, {"i": {"x": 0.833, "y": 0.861}, "o": {"x": 0.167, "y": 0.199}, "t": 176, "s": [192.307, 175.314, 0], "to": [0, -0.025, 0], "ti": [0, 0.018, 0]}, {"i": {"x": 0.833, "y": 0.87}, "o": {"x": 0.167, "y": 0.208}, "t": 177, "s": [192.307, 175.25, 0], "to": [0, -0.018, 0], "ti": [0, 0.011, 0]}, {"i": {"x": 0.833, "y": 0.89}, "o": {"x": 0.167, "y": 0.231}, "t": 178, "s": [192.307, 175.207, 0], "to": [0, -0.011, 0], "ti": [0, 0.005, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.347}, "t": 179, "s": [192.307, 175.183, 0], "to": [0, -0.005, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.653}, "o": {"x": 0.167, "y": 0.167}, "t": 180, "s": [192.307, 175.176, 0], "to": [0, 0, 0], "ti": [0, -0.005, 0]}, {"i": {"x": 0.833, "y": 0.769}, "o": {"x": 0.167, "y": 0.11}, "t": 181, "s": [192.307, 175.183, 0], "to": [0, 0.005, 0], "ti": [0, -0.011, 0]}, {"i": {"x": 0.833, "y": 0.792}, "o": {"x": 0.167, "y": 0.13}, "t": 182, "s": [192.307, 175.207, 0], "to": [0, 0.011, 0], "ti": [0, -0.018, 0]}, {"i": {"x": 0.833, "y": 0.801}, "o": {"x": 0.167, "y": 0.139}, "t": 183, "s": [192.307, 175.25, 0], "to": [0, 0.018, 0], "ti": [0, -0.025, 0]}, {"i": {"x": 0.833, "y": 0.807}, "o": {"x": 0.167, "y": 0.144}, "t": 184, "s": [192.307, 175.314, 0], "to": [0, 0.025, 0], "ti": [0, -0.034, 0]}, {"i": {"x": 0.833, "y": 0.81}, "o": {"x": 0.167, "y": 0.146}, "t": 185, "s": [192.307, 175.402, 0], "to": [0, 0.034, 0], "ti": [0, -0.044, 0]}, {"i": {"x": 0.833, "y": 0.812}, "o": {"x": 0.167, "y": 0.148}, "t": 186, "s": [192.307, 175.519, 0], "to": [0, 0.044, 0], "ti": [0, -0.056, 0]}, {"i": {"x": 0.833, "y": 0.813}, "o": {"x": 0.167, "y": 0.15}, "t": 187, "s": [192.307, 175.669, 0], "to": [0, 0.056, 0], "ti": [0, -0.07, 0]}, {"i": {"x": 0.833, "y": 0.814}, "o": {"x": 0.167, "y": 0.151}, "t": 188, "s": [192.307, 175.857, 0], "to": [0, 0.07, 0], "ti": [0, -0.087, 0]}, {"i": {"x": 0.833, "y": 0.813}, "o": {"x": 0.167, "y": 0.151}, "t": 189, "s": [192.307, 176.09, 0], "to": [0, 0.087, 0], "ti": [0, -0.107, 0]}, {"i": {"x": 0.833, "y": 0.812}, "o": {"x": 0.167, "y": 0.151}, "t": 190, "s": [192.307, 176.377, 0], "to": [0, 0.107, 0], "ti": [0, -0.134, 0]}, {"i": {"x": 0.833, "y": 0.808}, "o": {"x": 0.167, "y": 0.15}, "t": 191, "s": [192.307, 176.734, 0], "to": [0, 0.134, 0], "ti": [0, -0.172, 0]}, {"i": {"x": 0.833, "y": 0.804}, "o": {"x": 0.167, "y": 0.147}, "t": 192, "s": [192.307, 177.183, 0], "to": [0, 0.172, 0], "ti": [0, -0.229, 0]}, {"i": {"x": 0.833, "y": 0.799}, "o": {"x": 0.167, "y": 0.145}, "t": 193, "s": [192.307, 177.768, 0], "to": [0, 0.229, 0], "ti": [0, -0.318, 0]}, {"i": {"x": 0.833, "y": 0.796}, "o": {"x": 0.167, "y": 0.142}, "t": 194, "s": [192.307, 178.558, 0], "to": [0, 0.318, 0], "ti": [0, -0.457, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.141}, "t": 195, "s": [192.307, 179.675, 0], "to": [0, 0.457, 0], "ti": [0, -0.541, 0]}, {"i": {"x": 0.833, "y": 0.874}, "o": {"x": 0.167, "y": 0.167}, "t": 196, "s": [192.307, 181.298, 0], "to": [0, 0.541, 0], "ti": [0, -0.411, 0]}, {"i": {"x": 0.833, "y": 0.884}, "o": {"x": 0.167, "y": 0.245}, "t": 197, "s": [192.307, 182.923, 0], "to": [0, 0.411, 0], "ti": [0, -0.195, 0]}, {"i": {"x": 0.833, "y": 0.897}, "o": {"x": 0.167, "y": 0.294}, "t": 198, "s": [192.307, 183.764, 0], "to": [0, 0.195, 0], "ti": [0, -0.069, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.429}, "t": 199, "s": [192.307, 184.096, 0], "to": [0, 0.069, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.571}, "o": {"x": 0.167, "y": 0.167}, "t": 200, "s": [192.307, 184.176, 0], "to": [0, 0, 0], "ti": [0, 0.069, 0]}, {"i": {"x": 0.833, "y": 0.706}, "o": {"x": 0.167, "y": 0.103}, "t": 201, "s": [192.307, 184.096, 0], "to": [0, -0.069, 0], "ti": [0, 0.195, 0]}, {"i": {"x": 0.833, "y": 0.755}, "o": {"x": 0.167, "y": 0.116}, "t": 202, "s": [192.307, 183.764, 0], "to": [0, -0.195, 0], "ti": [0, 0.411, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.126}, "t": 203, "s": [192.307, 182.923, 0], "to": [0, -0.411, 0], "ti": [0, 0.541, 0]}, {"i": {"x": 0.833, "y": 0.859}, "o": {"x": 0.167, "y": 0.167}, "t": 204, "s": [192.307, 181.298, 0], "to": [0, -0.541, 0], "ti": [0, 0.457, 0]}, {"i": {"x": 0.833, "y": 0.858}, "o": {"x": 0.167, "y": 0.204}, "t": 205, "s": [192.307, 179.675, 0], "to": [0, -0.457, 0], "ti": [0, 0.318, 0]}, {"i": {"x": 0.833, "y": 0.855}, "o": {"x": 0.167, "y": 0.201}, "t": 206, "s": [192.307, 178.558, 0], "to": [0, -0.318, 0], "ti": [0, 0.229, 0]}, {"i": {"x": 0.833, "y": 0.853}, "o": {"x": 0.167, "y": 0.196}, "t": 207, "s": [192.307, 177.768, 0], "to": [0, -0.229, 0], "ti": [0, 0.172, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.192}, "t": 208, "s": [192.307, 177.183, 0], "to": [0, -0.172, 0], "ti": [0, 0.075, 0]}, {"t": 209.000008512745, "s": [192.307, 176.734, 0]}], "ix": 2}, "a": {"a": 0, "k": [31.728, 11.77, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-4.515, -10.354], [-7.992, 1.158], [-2.013, 0.362], [3.084, 4.178], [6.158, -0.165]], "o": [[0, 0], [7.993, -1.157], [0, 0], [0, 0], [-6.158, 0.164]], "v": [[-19.129, 2.442], [-8.498, 7.395], [21.032, -0.094], [20.561, -8.553], [1.694, -6.79]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.882000014361, 0.910000011968, 0.948999980852, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [23.894, 14.738], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-1.233, -0.231], [-1.294, 0.055], [-0.416, 0.418], [-0.488, 0.612], [0.115, 1.008], [-0.406, 0.51], [0.277, 0.804], [0.53, -0.246], [0.854, -0.654], [1.099, -0.125], [0, 0], [-1.058, 0.492], [-0.896, 0.288], [0.144, 0.448], [0.458, -0.053], [1.121, 0.058], [0.325, -0.408], [0.998, -0.206]], "o": [[0, 0], [1.233, 0.231], [1.293, -0.054], [0.417, -0.419], [0.487, -0.612], [-0.115, -1.008], [0.406, -0.511], [-0.278, -0.804], [-0.528, 0.246], [-0.853, 0.654], [-1.1, 0.125], [0, 0], [1.057, -0.492], [0.895, -0.288], [-0.144, -0.447], [-0.459, 0.052], [-1.121, -0.058], [-0.325, 0.409], [-0.998, 0.207]], "v": [[-8.78, 6.708], [-5.677, 5.426], [1.047, 5.773], [5.061, 2.717], [6.146, 0.829], [6.803, -0.731], [7.998, -3.281], [9.834, -5.904], [8.163, -5.899], [4.892, -2.834], [0.834, -0.979], [-1.305, -1.014], [-0.522, -1.474], [2.104, -2.052], [3.251, -3.39], [1.458, -3.649], [-2.86, -3.25], [-5.929, -0.858], [-9.113, 0.526]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.62400004069, 0.458999992819, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [53.096, 6.958], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 3, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 94.0000038286985, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "Layer 6 Outlines", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.65]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [-22]}, {"i": {"x": [0.833], "y": [0.768]}, "o": {"x": [0.167], "y": [0.109]}, "t": 1, "s": [-21.939]}, {"i": {"x": [0.833], "y": [0.793]}, "o": {"x": [0.167], "y": [0.13]}, "t": 2, "s": [-21.746]}, {"i": {"x": [0.833], "y": [0.805]}, "o": {"x": [0.167], "y": [0.14]}, "t": 3, "s": [-21.4]}, {"i": {"x": [0.833], "y": [0.813]}, "o": {"x": [0.167], "y": [0.146]}, "t": 4, "s": [-20.888]}, {"i": {"x": [0.833], "y": [0.82]}, "o": {"x": [0.167], "y": [0.15]}, "t": 5, "s": [-20.202]}, {"i": {"x": [0.833], "y": [0.826]}, "o": {"x": [0.167], "y": [0.155]}, "t": 6, "s": [-19.35]}, {"i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.16]}, "t": 7, "s": [-18.362]}, {"i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.165]}, "t": 8, "s": [-17.289]}, {"i": {"x": [0.833], "y": [0.838]}, "o": {"x": [0.167], "y": [0.169]}, "t": 9, "s": [-16.189]}, {"i": {"x": [0.833], "y": [0.84]}, "o": {"x": [0.167], "y": [0.172]}, "t": 10, "s": [-15.116]}, {"i": {"x": [0.833], "y": [0.842]}, "o": {"x": [0.167], "y": [0.174]}, "t": 11, "s": [-14.107]}, {"i": {"x": [0.833], "y": [0.843]}, "o": {"x": [0.167], "y": [0.176]}, "t": 12, "s": [-13.184]}, {"i": {"x": [0.833], "y": [0.843]}, "o": {"x": [0.167], "y": [0.177]}, "t": 13, "s": [-12.354]}, {"i": {"x": [0.833], "y": [0.844]}, "o": {"x": [0.167], "y": [0.178]}, "t": 14, "s": [-11.619]}, {"i": {"x": [0.833], "y": [0.845]}, "o": {"x": [0.167], "y": [0.179]}, "t": 15, "s": [-10.972]}, {"i": {"x": [0.833], "y": [0.845]}, "o": {"x": [0.167], "y": [0.18]}, "t": 16, "s": [-10.41]}, {"i": {"x": [0.833], "y": [0.846]}, "o": {"x": [0.167], "y": [0.181]}, "t": 17, "s": [-9.923]}, {"i": {"x": [0.833], "y": [0.847]}, "o": {"x": [0.167], "y": [0.181]}, "t": 18, "s": [-9.506]}, {"i": {"x": [0.833], "y": [0.848]}, "o": {"x": [0.167], "y": [0.183]}, "t": 19, "s": [-9.151]}, {"i": {"x": [0.833], "y": [0.849]}, "o": {"x": [0.167], "y": [0.184]}, "t": 20, "s": [-8.854]}, {"i": {"x": [0.833], "y": [0.851]}, "o": {"x": [0.167], "y": [0.187]}, "t": 21, "s": [-8.608]}, {"i": {"x": [0.833], "y": [0.855]}, "o": {"x": [0.167], "y": [0.19]}, "t": 22, "s": [-8.41]}, {"i": {"x": [0.833], "y": [0.86]}, "o": {"x": [0.167], "y": [0.195]}, "t": 23, "s": [-8.255]}, {"i": {"x": [0.833], "y": [0.869]}, "o": {"x": [0.167], "y": [0.205]}, "t": 24, "s": [-8.14]}, {"i": {"x": [0.833], "y": [0.89]}, "o": {"x": [0.167], "y": [0.228]}, "t": 25, "s": [-8.06]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0.342]}, "t": 26, "s": [-8.015]}, {"i": {"x": [0.833], "y": [0.658]}, "o": {"x": [0.167], "y": [0]}, "t": 27, "s": [-8]}, {"i": {"x": [0.833], "y": [0.772]}, "o": {"x": [0.167], "y": [0.11]}, "t": 28, "s": [-8.015]}, {"i": {"x": [0.833], "y": [0.795]}, "o": {"x": [0.167], "y": [0.131]}, "t": 29, "s": [-8.06]}, {"i": {"x": [0.833], "y": [0.805]}, "o": {"x": [0.167], "y": [0.14]}, "t": 30, "s": [-8.14]}, {"i": {"x": [0.833], "y": [0.81]}, "o": {"x": [0.167], "y": [0.145]}, "t": 31, "s": [-8.255]}, {"i": {"x": [0.833], "y": [0.813]}, "o": {"x": [0.167], "y": [0.149]}, "t": 32, "s": [-8.41]}, {"i": {"x": [0.833], "y": [0.816]}, "o": {"x": [0.167], "y": [0.151]}, "t": 33, "s": [-8.608]}, {"i": {"x": [0.833], "y": [0.817]}, "o": {"x": [0.167], "y": [0.152]}, "t": 34, "s": [-8.854]}, {"i": {"x": [0.833], "y": [0.819]}, "o": {"x": [0.167], "y": [0.153]}, "t": 35, "s": [-9.151]}, {"i": {"x": [0.833], "y": [0.819]}, "o": {"x": [0.167], "y": [0.154]}, "t": 36, "s": [-9.506]}, {"i": {"x": [0.833], "y": [0.82]}, "o": {"x": [0.167], "y": [0.155]}, "t": 37, "s": [-9.923]}, {"i": {"x": [0.833], "y": [0.821]}, "o": {"x": [0.167], "y": [0.155]}, "t": 38, "s": [-10.41]}, {"i": {"x": [0.833], "y": [0.822]}, "o": {"x": [0.167], "y": [0.156]}, "t": 39, "s": [-10.972]}, {"i": {"x": [0.833], "y": [0.823]}, "o": {"x": [0.167], "y": [0.157]}, "t": 40, "s": [-11.619]}, {"i": {"x": [0.833], "y": [0.824]}, "o": {"x": [0.167], "y": [0.157]}, "t": 41, "s": [-12.354]}, {"i": {"x": [0.833], "y": [0.826]}, "o": {"x": [0.167], "y": [0.158]}, "t": 42, "s": [-13.184]}, {"i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.16]}, "t": 43, "s": [-14.107]}, {"i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.162]}, "t": 44, "s": [-15.116]}, {"i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.165]}, "t": 45, "s": [-16.189]}, {"i": {"x": [0.833], "y": [0.84]}, "o": {"x": [0.167], "y": [0.169]}, "t": 46, "s": [-17.289]}, {"i": {"x": [0.833], "y": [0.845]}, "o": {"x": [0.167], "y": [0.174]}, "t": 47, "s": [-18.362]}, {"i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.18]}, "t": 48, "s": [-19.35]}, {"i": {"x": [0.833], "y": [0.854]}, "o": {"x": [0.167], "y": [0.187]}, "t": 49, "s": [-20.202]}, {"i": {"x": [0.833], "y": [0.86]}, "o": {"x": [0.167], "y": [0.195]}, "t": 50, "s": [-20.888]}, {"i": {"x": [0.833], "y": [0.87]}, "o": {"x": [0.167], "y": [0.207]}, "t": 51, "s": [-21.4]}, {"i": {"x": [0.833], "y": [0.891]}, "o": {"x": [0.167], "y": [0.232]}, "t": 52, "s": [-21.746]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0.35]}, "t": 53, "s": [-21.939]}, {"i": {"x": [0.833], "y": [0.65]}, "o": {"x": [0.167], "y": [0]}, "t": 54, "s": [-22]}, {"i": {"x": [0.833], "y": [0.768]}, "o": {"x": [0.167], "y": [0.109]}, "t": 55, "s": [-21.939]}, {"i": {"x": [0.833], "y": [0.793]}, "o": {"x": [0.167], "y": [0.13]}, "t": 56, "s": [-21.746]}, {"i": {"x": [0.833], "y": [0.805]}, "o": {"x": [0.167], "y": [0.14]}, "t": 57, "s": [-21.4]}, {"i": {"x": [0.833], "y": [0.813]}, "o": {"x": [0.167], "y": [0.146]}, "t": 58, "s": [-20.888]}, {"i": {"x": [0.833], "y": [0.82]}, "o": {"x": [0.167], "y": [0.15]}, "t": 59, "s": [-20.202]}, {"i": {"x": [0.833], "y": [0.826]}, "o": {"x": [0.167], "y": [0.155]}, "t": 60, "s": [-19.35]}, {"i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.16]}, "t": 61, "s": [-18.362]}, {"i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.165]}, "t": 62, "s": [-17.289]}, {"i": {"x": [0.833], "y": [0.838]}, "o": {"x": [0.167], "y": [0.169]}, "t": 63, "s": [-16.189]}, {"i": {"x": [0.833], "y": [0.84]}, "o": {"x": [0.167], "y": [0.172]}, "t": 64, "s": [-15.116]}, {"i": {"x": [0.833], "y": [0.842]}, "o": {"x": [0.167], "y": [0.174]}, "t": 65, "s": [-14.107]}, {"i": {"x": [0.833], "y": [0.843]}, "o": {"x": [0.167], "y": [0.176]}, "t": 66, "s": [-13.184]}, {"i": {"x": [0.833], "y": [0.843]}, "o": {"x": [0.167], "y": [0.177]}, "t": 67, "s": [-12.354]}, {"i": {"x": [0.833], "y": [0.844]}, "o": {"x": [0.167], "y": [0.178]}, "t": 68, "s": [-11.619]}, {"i": {"x": [0.833], "y": [0.845]}, "o": {"x": [0.167], "y": [0.179]}, "t": 69, "s": [-10.972]}, {"i": {"x": [0.833], "y": [0.845]}, "o": {"x": [0.167], "y": [0.18]}, "t": 70, "s": [-10.41]}, {"i": {"x": [0.833], "y": [0.846]}, "o": {"x": [0.167], "y": [0.181]}, "t": 71, "s": [-9.923]}, {"i": {"x": [0.833], "y": [0.847]}, "o": {"x": [0.167], "y": [0.181]}, "t": 72, "s": [-9.506]}, {"i": {"x": [0.833], "y": [0.848]}, "o": {"x": [0.167], "y": [0.183]}, "t": 73, "s": [-9.151]}, {"i": {"x": [0.833], "y": [0.849]}, "o": {"x": [0.167], "y": [0.184]}, "t": 74, "s": [-8.854]}, {"i": {"x": [0.833], "y": [0.851]}, "o": {"x": [0.167], "y": [0.187]}, "t": 75, "s": [-8.608]}, {"i": {"x": [0.833], "y": [0.855]}, "o": {"x": [0.167], "y": [0.19]}, "t": 76, "s": [-8.41]}, {"i": {"x": [0.833], "y": [0.86]}, "o": {"x": [0.167], "y": [0.195]}, "t": 77, "s": [-8.255]}, {"i": {"x": [0.833], "y": [0.869]}, "o": {"x": [0.167], "y": [0.205]}, "t": 78, "s": [-8.14]}, {"i": {"x": [0.833], "y": [0.89]}, "o": {"x": [0.167], "y": [0.228]}, "t": 79, "s": [-8.06]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0.342]}, "t": 80, "s": [-8.015]}, {"i": {"x": [0.833], "y": [0.658]}, "o": {"x": [0.167], "y": [0]}, "t": 81, "s": [-8]}, {"i": {"x": [0.833], "y": [0.772]}, "o": {"x": [0.167], "y": [0.11]}, "t": 82, "s": [-8.015]}, {"i": {"x": [0.833], "y": [0.795]}, "o": {"x": [0.167], "y": [0.131]}, "t": 83, "s": [-8.06]}, {"i": {"x": [0.833], "y": [0.805]}, "o": {"x": [0.167], "y": [0.14]}, "t": 84, "s": [-8.14]}, {"i": {"x": [0.833], "y": [0.81]}, "o": {"x": [0.167], "y": [0.145]}, "t": 85, "s": [-8.255]}, {"i": {"x": [0.833], "y": [0.813]}, "o": {"x": [0.167], "y": [0.149]}, "t": 86, "s": [-8.41]}, {"i": {"x": [0.833], "y": [0.816]}, "o": {"x": [0.167], "y": [0.151]}, "t": 87, "s": [-8.608]}, {"i": {"x": [0.833], "y": [0.817]}, "o": {"x": [0.167], "y": [0.152]}, "t": 88, "s": [-8.854]}, {"i": {"x": [0.833], "y": [0.819]}, "o": {"x": [0.167], "y": [0.153]}, "t": 89, "s": [-9.151]}, {"i": {"x": [0.833], "y": [0.819]}, "o": {"x": [0.167], "y": [0.154]}, "t": 90, "s": [-9.506]}, {"i": {"x": [0.833], "y": [0.82]}, "o": {"x": [0.167], "y": [0.155]}, "t": 91, "s": [-9.923]}, {"i": {"x": [0.833], "y": [0.821]}, "o": {"x": [0.167], "y": [0.155]}, "t": 92, "s": [-10.41]}, {"i": {"x": [0.833], "y": [0.822]}, "o": {"x": [0.167], "y": [0.156]}, "t": 93, "s": [-10.972]}, {"i": {"x": [0.833], "y": [0.823]}, "o": {"x": [0.167], "y": [0.157]}, "t": 94, "s": [-11.619]}, {"i": {"x": [0.833], "y": [0.824]}, "o": {"x": [0.167], "y": [0.157]}, "t": 95, "s": [-12.354]}, {"i": {"x": [0.833], "y": [0.826]}, "o": {"x": [0.167], "y": [0.158]}, "t": 96, "s": [-13.184]}, {"i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.16]}, "t": 97, "s": [-14.107]}, {"i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.162]}, "t": 98, "s": [-15.116]}, {"i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.165]}, "t": 99, "s": [-16.189]}, {"i": {"x": [0.833], "y": [0.84]}, "o": {"x": [0.167], "y": [0.169]}, "t": 100, "s": [-17.289]}, {"i": {"x": [0.833], "y": [0.845]}, "o": {"x": [0.167], "y": [0.174]}, "t": 101, "s": [-18.362]}, {"i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.18]}, "t": 102, "s": [-19.35]}, {"i": {"x": [0.833], "y": [0.854]}, "o": {"x": [0.167], "y": [0.187]}, "t": 103, "s": [-20.202]}, {"i": {"x": [0.833], "y": [0.86]}, "o": {"x": [0.167], "y": [0.195]}, "t": 104, "s": [-20.888]}, {"i": {"x": [0.833], "y": [0.87]}, "o": {"x": [0.167], "y": [0.207]}, "t": 105, "s": [-21.4]}, {"i": {"x": [0.833], "y": [0.891]}, "o": {"x": [0.167], "y": [0.232]}, "t": 106, "s": [-21.746]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0.35]}, "t": 107, "s": [-21.939]}, {"i": {"x": [0.833], "y": [0.65]}, "o": {"x": [0.167], "y": [0]}, "t": 108, "s": [-22]}, {"i": {"x": [0.833], "y": [0.768]}, "o": {"x": [0.167], "y": [0.109]}, "t": 109, "s": [-21.939]}, {"i": {"x": [0.833], "y": [0.793]}, "o": {"x": [0.167], "y": [0.13]}, "t": 110, "s": [-21.746]}, {"i": {"x": [0.833], "y": [0.805]}, "o": {"x": [0.167], "y": [0.14]}, "t": 111, "s": [-21.4]}, {"i": {"x": [0.833], "y": [0.813]}, "o": {"x": [0.167], "y": [0.146]}, "t": 112, "s": [-20.888]}, {"i": {"x": [0.833], "y": [0.82]}, "o": {"x": [0.167], "y": [0.15]}, "t": 113, "s": [-20.202]}, {"i": {"x": [0.833], "y": [0.826]}, "o": {"x": [0.167], "y": [0.155]}, "t": 114, "s": [-19.35]}, {"i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.16]}, "t": 115, "s": [-18.362]}, {"i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.165]}, "t": 116, "s": [-17.289]}, {"i": {"x": [0.833], "y": [0.838]}, "o": {"x": [0.167], "y": [0.169]}, "t": 117, "s": [-16.189]}, {"i": {"x": [0.833], "y": [0.84]}, "o": {"x": [0.167], "y": [0.172]}, "t": 118, "s": [-15.116]}, {"i": {"x": [0.833], "y": [0.842]}, "o": {"x": [0.167], "y": [0.174]}, "t": 119, "s": [-14.107]}, {"i": {"x": [0.833], "y": [0.843]}, "o": {"x": [0.167], "y": [0.176]}, "t": 120, "s": [-13.184]}, {"i": {"x": [0.833], "y": [0.843]}, "o": {"x": [0.167], "y": [0.177]}, "t": 121, "s": [-12.354]}, {"i": {"x": [0.833], "y": [0.844]}, "o": {"x": [0.167], "y": [0.178]}, "t": 122, "s": [-11.619]}, {"i": {"x": [0.833], "y": [0.845]}, "o": {"x": [0.167], "y": [0.179]}, "t": 123, "s": [-10.972]}, {"i": {"x": [0.833], "y": [0.845]}, "o": {"x": [0.167], "y": [0.18]}, "t": 124, "s": [-10.41]}, {"i": {"x": [0.833], "y": [0.846]}, "o": {"x": [0.167], "y": [0.181]}, "t": 125, "s": [-9.923]}, {"i": {"x": [0.833], "y": [0.847]}, "o": {"x": [0.167], "y": [0.181]}, "t": 126, "s": [-9.506]}, {"i": {"x": [0.833], "y": [0.848]}, "o": {"x": [0.167], "y": [0.183]}, "t": 127, "s": [-9.151]}, {"i": {"x": [0.833], "y": [0.849]}, "o": {"x": [0.167], "y": [0.184]}, "t": 128, "s": [-8.854]}, {"i": {"x": [0.833], "y": [0.851]}, "o": {"x": [0.167], "y": [0.187]}, "t": 129, "s": [-8.608]}, {"i": {"x": [0.833], "y": [0.855]}, "o": {"x": [0.167], "y": [0.19]}, "t": 130, "s": [-8.41]}, {"i": {"x": [0.833], "y": [0.86]}, "o": {"x": [0.167], "y": [0.195]}, "t": 131, "s": [-8.255]}, {"i": {"x": [0.833], "y": [0.869]}, "o": {"x": [0.167], "y": [0.205]}, "t": 132, "s": [-8.14]}, {"i": {"x": [0.833], "y": [0.89]}, "o": {"x": [0.167], "y": [0.228]}, "t": 133, "s": [-8.06]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0.342]}, "t": 134, "s": [-8.015]}, {"i": {"x": [0.833], "y": [0.658]}, "o": {"x": [0.167], "y": [0]}, "t": 135, "s": [-8]}, {"i": {"x": [0.833], "y": [0.772]}, "o": {"x": [0.167], "y": [0.11]}, "t": 136, "s": [-8.015]}, {"i": {"x": [0.833], "y": [0.795]}, "o": {"x": [0.167], "y": [0.131]}, "t": 137, "s": [-8.06]}, {"i": {"x": [0.833], "y": [0.805]}, "o": {"x": [0.167], "y": [0.14]}, "t": 138, "s": [-8.14]}, {"i": {"x": [0.833], "y": [0.81]}, "o": {"x": [0.167], "y": [0.145]}, "t": 139, "s": [-8.255]}, {"i": {"x": [0.833], "y": [0.813]}, "o": {"x": [0.167], "y": [0.149]}, "t": 140, "s": [-8.41]}, {"i": {"x": [0.833], "y": [0.816]}, "o": {"x": [0.167], "y": [0.151]}, "t": 141, "s": [-8.608]}, {"i": {"x": [0.833], "y": [0.817]}, "o": {"x": [0.167], "y": [0.152]}, "t": 142, "s": [-8.854]}, {"i": {"x": [0.833], "y": [0.819]}, "o": {"x": [0.167], "y": [0.153]}, "t": 143, "s": [-9.151]}, {"i": {"x": [0.833], "y": [0.819]}, "o": {"x": [0.167], "y": [0.154]}, "t": 144, "s": [-9.506]}, {"i": {"x": [0.833], "y": [0.82]}, "o": {"x": [0.167], "y": [0.155]}, "t": 145, "s": [-9.923]}, {"i": {"x": [0.833], "y": [0.821]}, "o": {"x": [0.167], "y": [0.155]}, "t": 146, "s": [-10.41]}, {"i": {"x": [0.833], "y": [0.822]}, "o": {"x": [0.167], "y": [0.156]}, "t": 147, "s": [-10.972]}, {"i": {"x": [0.833], "y": [0.823]}, "o": {"x": [0.167], "y": [0.157]}, "t": 148, "s": [-11.619]}, {"i": {"x": [0.833], "y": [0.824]}, "o": {"x": [0.167], "y": [0.157]}, "t": 149, "s": [-12.354]}, {"i": {"x": [0.833], "y": [0.826]}, "o": {"x": [0.167], "y": [0.158]}, "t": 150, "s": [-13.184]}, {"i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.16]}, "t": 151, "s": [-14.107]}, {"i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.162]}, "t": 152, "s": [-15.116]}, {"i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.165]}, "t": 153, "s": [-16.189]}, {"i": {"x": [0.833], "y": [0.84]}, "o": {"x": [0.167], "y": [0.169]}, "t": 154, "s": [-17.289]}, {"i": {"x": [0.833], "y": [0.845]}, "o": {"x": [0.167], "y": [0.174]}, "t": 155, "s": [-18.362]}, {"i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.18]}, "t": 156, "s": [-19.35]}, {"i": {"x": [0.833], "y": [0.854]}, "o": {"x": [0.167], "y": [0.187]}, "t": 157, "s": [-20.202]}, {"i": {"x": [0.833], "y": [0.86]}, "o": {"x": [0.167], "y": [0.195]}, "t": 158, "s": [-20.888]}, {"i": {"x": [0.833], "y": [0.87]}, "o": {"x": [0.167], "y": [0.207]}, "t": 159, "s": [-21.4]}, {"i": {"x": [0.833], "y": [0.891]}, "o": {"x": [0.167], "y": [0.232]}, "t": 160, "s": [-21.746]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0.35]}, "t": 161, "s": [-21.939]}, {"i": {"x": [0.833], "y": [0.65]}, "o": {"x": [0.167], "y": [0]}, "t": 162, "s": [-22]}, {"i": {"x": [0.833], "y": [0.768]}, "o": {"x": [0.167], "y": [0.109]}, "t": 163, "s": [-21.939]}, {"i": {"x": [0.833], "y": [0.793]}, "o": {"x": [0.167], "y": [0.13]}, "t": 164, "s": [-21.746]}, {"i": {"x": [0.833], "y": [0.805]}, "o": {"x": [0.167], "y": [0.14]}, "t": 165, "s": [-21.4]}, {"i": {"x": [0.833], "y": [0.813]}, "o": {"x": [0.167], "y": [0.146]}, "t": 166, "s": [-20.888]}, {"i": {"x": [0.833], "y": [0.82]}, "o": {"x": [0.167], "y": [0.15]}, "t": 167, "s": [-20.202]}, {"i": {"x": [0.833], "y": [0.826]}, "o": {"x": [0.167], "y": [0.155]}, "t": 168, "s": [-19.35]}, {"i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.16]}, "t": 169, "s": [-18.362]}, {"i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.165]}, "t": 170, "s": [-17.289]}, {"i": {"x": [0.833], "y": [0.838]}, "o": {"x": [0.167], "y": [0.169]}, "t": 171, "s": [-16.189]}, {"i": {"x": [0.833], "y": [0.84]}, "o": {"x": [0.167], "y": [0.172]}, "t": 172, "s": [-15.116]}, {"i": {"x": [0.833], "y": [0.842]}, "o": {"x": [0.167], "y": [0.174]}, "t": 173, "s": [-14.107]}, {"i": {"x": [0.833], "y": [0.843]}, "o": {"x": [0.167], "y": [0.176]}, "t": 174, "s": [-13.184]}, {"i": {"x": [0.833], "y": [0.843]}, "o": {"x": [0.167], "y": [0.177]}, "t": 175, "s": [-12.354]}, {"i": {"x": [0.833], "y": [0.844]}, "o": {"x": [0.167], "y": [0.178]}, "t": 176, "s": [-11.619]}, {"i": {"x": [0.833], "y": [0.845]}, "o": {"x": [0.167], "y": [0.179]}, "t": 177, "s": [-10.972]}, {"i": {"x": [0.833], "y": [0.845]}, "o": {"x": [0.167], "y": [0.18]}, "t": 178, "s": [-10.41]}, {"i": {"x": [0.833], "y": [0.846]}, "o": {"x": [0.167], "y": [0.181]}, "t": 179, "s": [-9.923]}, {"i": {"x": [0.833], "y": [0.847]}, "o": {"x": [0.167], "y": [0.181]}, "t": 180, "s": [-9.506]}, {"i": {"x": [0.833], "y": [0.848]}, "o": {"x": [0.167], "y": [0.183]}, "t": 181, "s": [-9.151]}, {"i": {"x": [0.833], "y": [0.849]}, "o": {"x": [0.167], "y": [0.184]}, "t": 182, "s": [-8.854]}, {"i": {"x": [0.833], "y": [0.851]}, "o": {"x": [0.167], "y": [0.187]}, "t": 183, "s": [-8.608]}, {"i": {"x": [0.833], "y": [0.855]}, "o": {"x": [0.167], "y": [0.19]}, "t": 184, "s": [-8.41]}, {"i": {"x": [0.833], "y": [0.86]}, "o": {"x": [0.167], "y": [0.195]}, "t": 185, "s": [-8.255]}, {"i": {"x": [0.833], "y": [0.869]}, "o": {"x": [0.167], "y": [0.205]}, "t": 186, "s": [-8.14]}, {"i": {"x": [0.833], "y": [0.89]}, "o": {"x": [0.167], "y": [0.228]}, "t": 187, "s": [-8.06]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0.342]}, "t": 188, "s": [-8.015]}, {"i": {"x": [0.833], "y": [0.658]}, "o": {"x": [0.167], "y": [0]}, "t": 189, "s": [-8]}, {"i": {"x": [0.833], "y": [0.772]}, "o": {"x": [0.167], "y": [0.11]}, "t": 190, "s": [-8.015]}, {"i": {"x": [0.833], "y": [0.795]}, "o": {"x": [0.167], "y": [0.131]}, "t": 191, "s": [-8.06]}, {"i": {"x": [0.833], "y": [0.805]}, "o": {"x": [0.167], "y": [0.14]}, "t": 192, "s": [-8.14]}, {"i": {"x": [0.833], "y": [0.81]}, "o": {"x": [0.167], "y": [0.145]}, "t": 193, "s": [-8.255]}, {"i": {"x": [0.833], "y": [0.813]}, "o": {"x": [0.167], "y": [0.149]}, "t": 194, "s": [-8.41]}, {"i": {"x": [0.833], "y": [0.816]}, "o": {"x": [0.167], "y": [0.151]}, "t": 195, "s": [-8.608]}, {"i": {"x": [0.833], "y": [0.817]}, "o": {"x": [0.167], "y": [0.152]}, "t": 196, "s": [-8.854]}, {"i": {"x": [0.833], "y": [0.819]}, "o": {"x": [0.167], "y": [0.153]}, "t": 197, "s": [-9.151]}, {"i": {"x": [0.833], "y": [0.819]}, "o": {"x": [0.167], "y": [0.154]}, "t": 198, "s": [-9.506]}, {"i": {"x": [0.833], "y": [0.82]}, "o": {"x": [0.167], "y": [0.155]}, "t": 199, "s": [-9.923]}, {"i": {"x": [0.833], "y": [0.821]}, "o": {"x": [0.167], "y": [0.155]}, "t": 200, "s": [-10.41]}, {"i": {"x": [0.833], "y": [0.822]}, "o": {"x": [0.167], "y": [0.156]}, "t": 201, "s": [-10.972]}, {"i": {"x": [0.833], "y": [0.823]}, "o": {"x": [0.167], "y": [0.157]}, "t": 202, "s": [-11.619]}, {"i": {"x": [0.833], "y": [0.824]}, "o": {"x": [0.167], "y": [0.157]}, "t": 203, "s": [-12.354]}, {"i": {"x": [0.833], "y": [0.826]}, "o": {"x": [0.167], "y": [0.158]}, "t": 204, "s": [-13.184]}, {"i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.16]}, "t": 205, "s": [-14.107]}, {"i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.162]}, "t": 206, "s": [-15.116]}, {"i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.165]}, "t": 207, "s": [-16.189]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.169]}, "t": 208, "s": [-17.289]}, {"t": 209.000008512745, "s": [-18.362]}], "ix": 10}, "p": {"a": 0, "k": [338.202, 241.553, 0], "ix": 2}, "a": {"a": 0, "k": [16.438, 23.91, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-1.868, -7.161], [-0.311, -5.915], [-0.622, -3.112], [-0.312, -4.358], [-3.423, -1.245], [-1.556, 2.178], [-1.868, 4.982], [1.556, 4.359], [0, 0], [3.736, 0.623]], "o": [[0, 0], [0.311, 5.915], [0.623, 3.114], [0.312, 4.358], [3.425, 1.246], [1.556, -2.18], [1.868, -4.981], [-1.557, -4.359], [0, 0], [-3.736, -0.622]], "v": [[-9.651, -14.477], [-14.633, -4.515], [-15.566, 5.448], [-9.651, 13.854], [0.621, 21.014], [11.207, 16.034], [14.32, 3.269], [13.698, -12.298], [9.029, -17.589], [3.736, -21.637]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.301999978458, 0.172999991623, 0.388000009574, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [16.439, 22.51], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-5.915, -7.472], [-3.736, 1.868], [-1.245, 9.028], [6.537, 2.802], [4.048, -6.849]], "o": [[0, 0], [5.915, 7.471], [3.735, -1.868], [1.245, -9.028], [-6.538, -2.802], [-4.047, 6.85]], "v": [[-13.698, -4.047], [-8.717, 14.633], [6.85, 15.878], [13.387, 1.868], [8.095, -18.679], [-9.651, -15.255]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.666999966491, 0.513999968884, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [16.127, 25.467], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 3, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 94.0000038286985, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "Layer 4 Outlines", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.61]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [0]}, {"i": {"x": [0.833], "y": [0.734]}, "o": {"x": [0.167], "y": [0.106]}, "t": 1, "s": [0.024]}, {"i": {"x": [0.833], "y": [0.755]}, "o": {"x": [0.167], "y": [0.121]}, "t": 2, "s": [0.11]}, {"i": {"x": [0.833], "y": [0.774]}, "o": {"x": [0.167], "y": [0.126]}, "t": 3, "s": [0.299]}, {"i": {"x": [0.833], "y": [0.819]}, "o": {"x": [0.167], "y": [0.132]}, "t": 4, "s": [0.667]}, {"i": {"x": [0.833], "y": [0.849]}, "o": {"x": [0.167], "y": [0.154]}, "t": 5, "s": [1.296]}, {"i": {"x": [0.833], "y": [0.854]}, "o": {"x": [0.167], "y": [0.186]}, "t": 6, "s": [2.035]}, {"i": {"x": [0.833], "y": [0.853]}, "o": {"x": [0.167], "y": [0.195]}, "t": 7, "s": [2.634]}, {"i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.192]}, "t": 8, "s": [3.083]}, {"i": {"x": [0.833], "y": [0.849]}, "o": {"x": [0.167], "y": [0.188]}, "t": 9, "s": [3.429]}, {"i": {"x": [0.833], "y": [0.848]}, "o": {"x": [0.167], "y": [0.186]}, "t": 10, "s": [3.704]}, {"i": {"x": [0.833], "y": [0.847]}, "o": {"x": [0.167], "y": [0.184]}, "t": 11, "s": [3.928]}, {"i": {"x": [0.833], "y": [0.846]}, "o": {"x": [0.167], "y": [0.183]}, "t": 12, "s": [4.114]}, {"i": {"x": [0.833], "y": [0.846]}, "o": {"x": [0.167], "y": [0.182]}, "t": 13, "s": [4.269]}, {"i": {"x": [0.833], "y": [0.846]}, "o": {"x": [0.167], "y": [0.181]}, "t": 14, "s": [4.401]}, {"i": {"x": [0.833], "y": [0.846]}, "o": {"x": [0.167], "y": [0.181]}, "t": 15, "s": [4.513]}, {"i": {"x": [0.833], "y": [0.846]}, "o": {"x": [0.167], "y": [0.181]}, "t": 16, "s": [4.608]}, {"i": {"x": [0.833], "y": [0.847]}, "o": {"x": [0.167], "y": [0.182]}, "t": 17, "s": [4.689]}, {"i": {"x": [0.833], "y": [0.847]}, "o": {"x": [0.167], "y": [0.182]}, "t": 18, "s": [4.758]}, {"i": {"x": [0.833], "y": [0.848]}, "o": {"x": [0.167], "y": [0.183]}, "t": 19, "s": [4.815]}, {"i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.185]}, "t": 20, "s": [4.863]}, {"i": {"x": [0.833], "y": [0.852]}, "o": {"x": [0.167], "y": [0.187]}, "t": 21, "s": [4.903]}, {"i": {"x": [0.833], "y": [0.855]}, "o": {"x": [0.167], "y": [0.19]}, "t": 22, "s": [4.935]}, {"i": {"x": [0.833], "y": [0.86]}, "o": {"x": [0.167], "y": [0.195]}, "t": 23, "s": [4.959]}, {"i": {"x": [0.833], "y": [0.869]}, "o": {"x": [0.167], "y": [0.205]}, "t": 24, "s": [4.978]}, {"i": {"x": [0.833], "y": [0.89]}, "o": {"x": [0.167], "y": [0.228]}, "t": 25, "s": [4.99]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0.342]}, "t": 26, "s": [4.998]}, {"i": {"x": [0.833], "y": [0.658]}, "o": {"x": [0.167], "y": [0]}, "t": 27, "s": [5]}, {"i": {"x": [0.833], "y": [0.772]}, "o": {"x": [0.167], "y": [0.11]}, "t": 28, "s": [4.998]}, {"i": {"x": [0.833], "y": [0.795]}, "o": {"x": [0.167], "y": [0.131]}, "t": 29, "s": [4.99]}, {"i": {"x": [0.833], "y": [0.805]}, "o": {"x": [0.167], "y": [0.14]}, "t": 30, "s": [4.978]}, {"i": {"x": [0.833], "y": [0.81]}, "o": {"x": [0.167], "y": [0.145]}, "t": 31, "s": [4.959]}, {"i": {"x": [0.833], "y": [0.813]}, "o": {"x": [0.167], "y": [0.148]}, "t": 32, "s": [4.935]}, {"i": {"x": [0.833], "y": [0.815]}, "o": {"x": [0.167], "y": [0.15]}, "t": 33, "s": [4.903]}, {"i": {"x": [0.833], "y": [0.817]}, "o": {"x": [0.167], "y": [0.152]}, "t": 34, "s": [4.863]}, {"i": {"x": [0.833], "y": [0.818]}, "o": {"x": [0.167], "y": [0.153]}, "t": 35, "s": [4.815]}, {"i": {"x": [0.833], "y": [0.818]}, "o": {"x": [0.167], "y": [0.153]}, "t": 36, "s": [4.758]}, {"i": {"x": [0.833], "y": [0.819]}, "o": {"x": [0.167], "y": [0.154]}, "t": 37, "s": [4.689]}, {"i": {"x": [0.833], "y": [0.819]}, "o": {"x": [0.167], "y": [0.154]}, "t": 38, "s": [4.608]}, {"i": {"x": [0.833], "y": [0.819]}, "o": {"x": [0.167], "y": [0.154]}, "t": 39, "s": [4.513]}, {"i": {"x": [0.833], "y": [0.818]}, "o": {"x": [0.167], "y": [0.154]}, "t": 40, "s": [4.401]}, {"i": {"x": [0.833], "y": [0.817]}, "o": {"x": [0.167], "y": [0.154]}, "t": 41, "s": [4.269]}, {"i": {"x": [0.833], "y": [0.816]}, "o": {"x": [0.167], "y": [0.153]}, "t": 42, "s": [4.114]}, {"i": {"x": [0.833], "y": [0.814]}, "o": {"x": [0.167], "y": [0.152]}, "t": 43, "s": [3.928]}, {"i": {"x": [0.833], "y": [0.812]}, "o": {"x": [0.167], "y": [0.151]}, "t": 44, "s": [3.704]}, {"i": {"x": [0.833], "y": [0.808]}, "o": {"x": [0.167], "y": [0.15]}, "t": 45, "s": [3.429]}, {"i": {"x": [0.833], "y": [0.805]}, "o": {"x": [0.167], "y": [0.147]}, "t": 46, "s": [3.083]}, {"i": {"x": [0.833], "y": [0.814]}, "o": {"x": [0.167], "y": [0.146]}, "t": 47, "s": [2.634]}, {"i": {"x": [0.833], "y": [0.846]}, "o": {"x": [0.167], "y": [0.151]}, "t": 48, "s": [2.035]}, {"i": {"x": [0.833], "y": [0.868]}, "o": {"x": [0.167], "y": [0.181]}, "t": 49, "s": [1.296]}, {"i": {"x": [0.833], "y": [0.874]}, "o": {"x": [0.167], "y": [0.226]}, "t": 50, "s": [0.667]}, {"i": {"x": [0.833], "y": [0.879]}, "o": {"x": [0.167], "y": [0.245]}, "t": 51, "s": [0.299]}, {"i": {"x": [0.833], "y": [0.894]}, "o": {"x": [0.167], "y": [0.266]}, "t": 52, "s": [0.11]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0.39]}, "t": 53, "s": [0.024]}, {"i": {"x": [0.833], "y": [0.61]}, "o": {"x": [0.167], "y": [0]}, "t": 54, "s": [0]}, {"i": {"x": [0.833], "y": [0.734]}, "o": {"x": [0.167], "y": [0.106]}, "t": 55, "s": [0.024]}, {"i": {"x": [0.833], "y": [0.755]}, "o": {"x": [0.167], "y": [0.121]}, "t": 56, "s": [0.11]}, {"i": {"x": [0.833], "y": [0.774]}, "o": {"x": [0.167], "y": [0.126]}, "t": 57, "s": [0.299]}, {"i": {"x": [0.833], "y": [0.819]}, "o": {"x": [0.167], "y": [0.132]}, "t": 58, "s": [0.667]}, {"i": {"x": [0.833], "y": [0.849]}, "o": {"x": [0.167], "y": [0.154]}, "t": 59, "s": [1.296]}, {"i": {"x": [0.833], "y": [0.854]}, "o": {"x": [0.167], "y": [0.186]}, "t": 60, "s": [2.035]}, {"i": {"x": [0.833], "y": [0.853]}, "o": {"x": [0.167], "y": [0.195]}, "t": 61, "s": [2.634]}, {"i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.192]}, "t": 62, "s": [3.083]}, {"i": {"x": [0.833], "y": [0.849]}, "o": {"x": [0.167], "y": [0.188]}, "t": 63, "s": [3.429]}, {"i": {"x": [0.833], "y": [0.848]}, "o": {"x": [0.167], "y": [0.186]}, "t": 64, "s": [3.704]}, {"i": {"x": [0.833], "y": [0.847]}, "o": {"x": [0.167], "y": [0.184]}, "t": 65, "s": [3.928]}, {"i": {"x": [0.833], "y": [0.846]}, "o": {"x": [0.167], "y": [0.183]}, "t": 66, "s": [4.114]}, {"i": {"x": [0.833], "y": [0.846]}, "o": {"x": [0.167], "y": [0.182]}, "t": 67, "s": [4.269]}, {"i": {"x": [0.833], "y": [0.846]}, "o": {"x": [0.167], "y": [0.181]}, "t": 68, "s": [4.401]}, {"i": {"x": [0.833], "y": [0.846]}, "o": {"x": [0.167], "y": [0.181]}, "t": 69, "s": [4.513]}, {"i": {"x": [0.833], "y": [0.846]}, "o": {"x": [0.167], "y": [0.181]}, "t": 70, "s": [4.608]}, {"i": {"x": [0.833], "y": [0.847]}, "o": {"x": [0.167], "y": [0.182]}, "t": 71, "s": [4.689]}, {"i": {"x": [0.833], "y": [0.847]}, "o": {"x": [0.167], "y": [0.182]}, "t": 72, "s": [4.758]}, {"i": {"x": [0.833], "y": [0.848]}, "o": {"x": [0.167], "y": [0.183]}, "t": 73, "s": [4.815]}, {"i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.185]}, "t": 74, "s": [4.863]}, {"i": {"x": [0.833], "y": [0.852]}, "o": {"x": [0.167], "y": [0.187]}, "t": 75, "s": [4.903]}, {"i": {"x": [0.833], "y": [0.855]}, "o": {"x": [0.167], "y": [0.19]}, "t": 76, "s": [4.935]}, {"i": {"x": [0.833], "y": [0.86]}, "o": {"x": [0.167], "y": [0.195]}, "t": 77, "s": [4.959]}, {"i": {"x": [0.833], "y": [0.869]}, "o": {"x": [0.167], "y": [0.205]}, "t": 78, "s": [4.978]}, {"i": {"x": [0.833], "y": [0.89]}, "o": {"x": [0.167], "y": [0.228]}, "t": 79, "s": [4.99]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0.342]}, "t": 80, "s": [4.998]}, {"i": {"x": [0.833], "y": [0.658]}, "o": {"x": [0.167], "y": [0]}, "t": 81, "s": [5]}, {"i": {"x": [0.833], "y": [0.772]}, "o": {"x": [0.167], "y": [0.11]}, "t": 82, "s": [4.998]}, {"i": {"x": [0.833], "y": [0.795]}, "o": {"x": [0.167], "y": [0.131]}, "t": 83, "s": [4.99]}, {"i": {"x": [0.833], "y": [0.805]}, "o": {"x": [0.167], "y": [0.14]}, "t": 84, "s": [4.978]}, {"i": {"x": [0.833], "y": [0.81]}, "o": {"x": [0.167], "y": [0.145]}, "t": 85, "s": [4.959]}, {"i": {"x": [0.833], "y": [0.813]}, "o": {"x": [0.167], "y": [0.148]}, "t": 86, "s": [4.935]}, {"i": {"x": [0.833], "y": [0.815]}, "o": {"x": [0.167], "y": [0.15]}, "t": 87, "s": [4.903]}, {"i": {"x": [0.833], "y": [0.817]}, "o": {"x": [0.167], "y": [0.152]}, "t": 88, "s": [4.863]}, {"i": {"x": [0.833], "y": [0.818]}, "o": {"x": [0.167], "y": [0.153]}, "t": 89, "s": [4.815]}, {"i": {"x": [0.833], "y": [0.818]}, "o": {"x": [0.167], "y": [0.153]}, "t": 90, "s": [4.758]}, {"i": {"x": [0.833], "y": [0.819]}, "o": {"x": [0.167], "y": [0.154]}, "t": 91, "s": [4.689]}, {"i": {"x": [0.833], "y": [0.819]}, "o": {"x": [0.167], "y": [0.154]}, "t": 92, "s": [4.608]}, {"i": {"x": [0.833], "y": [0.819]}, "o": {"x": [0.167], "y": [0.154]}, "t": 93, "s": [4.513]}, {"i": {"x": [0.833], "y": [0.818]}, "o": {"x": [0.167], "y": [0.154]}, "t": 94, "s": [4.401]}, {"i": {"x": [0.833], "y": [0.817]}, "o": {"x": [0.167], "y": [0.154]}, "t": 95, "s": [4.269]}, {"i": {"x": [0.833], "y": [0.816]}, "o": {"x": [0.167], "y": [0.153]}, "t": 96, "s": [4.114]}, {"i": {"x": [0.833], "y": [0.814]}, "o": {"x": [0.167], "y": [0.152]}, "t": 97, "s": [3.928]}, {"i": {"x": [0.833], "y": [0.812]}, "o": {"x": [0.167], "y": [0.151]}, "t": 98, "s": [3.704]}, {"i": {"x": [0.833], "y": [0.808]}, "o": {"x": [0.167], "y": [0.15]}, "t": 99, "s": [3.429]}, {"i": {"x": [0.833], "y": [0.805]}, "o": {"x": [0.167], "y": [0.147]}, "t": 100, "s": [3.083]}, {"i": {"x": [0.833], "y": [0.814]}, "o": {"x": [0.167], "y": [0.146]}, "t": 101, "s": [2.634]}, {"i": {"x": [0.833], "y": [0.846]}, "o": {"x": [0.167], "y": [0.151]}, "t": 102, "s": [2.035]}, {"i": {"x": [0.833], "y": [0.868]}, "o": {"x": [0.167], "y": [0.181]}, "t": 103, "s": [1.296]}, {"i": {"x": [0.833], "y": [0.874]}, "o": {"x": [0.167], "y": [0.226]}, "t": 104, "s": [0.667]}, {"i": {"x": [0.833], "y": [0.879]}, "o": {"x": [0.167], "y": [0.245]}, "t": 105, "s": [0.299]}, {"i": {"x": [0.833], "y": [0.894]}, "o": {"x": [0.167], "y": [0.266]}, "t": 106, "s": [0.11]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0.39]}, "t": 107, "s": [0.024]}, {"i": {"x": [0.833], "y": [0.61]}, "o": {"x": [0.167], "y": [0]}, "t": 108, "s": [0]}, {"i": {"x": [0.833], "y": [0.734]}, "o": {"x": [0.167], "y": [0.106]}, "t": 109, "s": [0.024]}, {"i": {"x": [0.833], "y": [0.755]}, "o": {"x": [0.167], "y": [0.121]}, "t": 110, "s": [0.11]}, {"i": {"x": [0.833], "y": [0.774]}, "o": {"x": [0.167], "y": [0.126]}, "t": 111, "s": [0.299]}, {"i": {"x": [0.833], "y": [0.819]}, "o": {"x": [0.167], "y": [0.132]}, "t": 112, "s": [0.667]}, {"i": {"x": [0.833], "y": [0.849]}, "o": {"x": [0.167], "y": [0.154]}, "t": 113, "s": [1.296]}, {"i": {"x": [0.833], "y": [0.854]}, "o": {"x": [0.167], "y": [0.186]}, "t": 114, "s": [2.035]}, {"i": {"x": [0.833], "y": [0.853]}, "o": {"x": [0.167], "y": [0.195]}, "t": 115, "s": [2.634]}, {"i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.192]}, "t": 116, "s": [3.083]}, {"i": {"x": [0.833], "y": [0.849]}, "o": {"x": [0.167], "y": [0.188]}, "t": 117, "s": [3.429]}, {"i": {"x": [0.833], "y": [0.848]}, "o": {"x": [0.167], "y": [0.186]}, "t": 118, "s": [3.704]}, {"i": {"x": [0.833], "y": [0.847]}, "o": {"x": [0.167], "y": [0.184]}, "t": 119, "s": [3.928]}, {"i": {"x": [0.833], "y": [0.846]}, "o": {"x": [0.167], "y": [0.183]}, "t": 120, "s": [4.114]}, {"i": {"x": [0.833], "y": [0.846]}, "o": {"x": [0.167], "y": [0.182]}, "t": 121, "s": [4.269]}, {"i": {"x": [0.833], "y": [0.846]}, "o": {"x": [0.167], "y": [0.181]}, "t": 122, "s": [4.401]}, {"i": {"x": [0.833], "y": [0.846]}, "o": {"x": [0.167], "y": [0.181]}, "t": 123, "s": [4.513]}, {"i": {"x": [0.833], "y": [0.846]}, "o": {"x": [0.167], "y": [0.181]}, "t": 124, "s": [4.608]}, {"i": {"x": [0.833], "y": [0.847]}, "o": {"x": [0.167], "y": [0.182]}, "t": 125, "s": [4.689]}, {"i": {"x": [0.833], "y": [0.847]}, "o": {"x": [0.167], "y": [0.182]}, "t": 126, "s": [4.758]}, {"i": {"x": [0.833], "y": [0.848]}, "o": {"x": [0.167], "y": [0.183]}, "t": 127, "s": [4.815]}, {"i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.185]}, "t": 128, "s": [4.863]}, {"i": {"x": [0.833], "y": [0.852]}, "o": {"x": [0.167], "y": [0.187]}, "t": 129, "s": [4.903]}, {"i": {"x": [0.833], "y": [0.855]}, "o": {"x": [0.167], "y": [0.19]}, "t": 130, "s": [4.935]}, {"i": {"x": [0.833], "y": [0.86]}, "o": {"x": [0.167], "y": [0.195]}, "t": 131, "s": [4.959]}, {"i": {"x": [0.833], "y": [0.869]}, "o": {"x": [0.167], "y": [0.205]}, "t": 132, "s": [4.978]}, {"i": {"x": [0.833], "y": [0.89]}, "o": {"x": [0.167], "y": [0.228]}, "t": 133, "s": [4.99]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0.342]}, "t": 134, "s": [4.998]}, {"i": {"x": [0.833], "y": [0.658]}, "o": {"x": [0.167], "y": [0]}, "t": 135, "s": [5]}, {"i": {"x": [0.833], "y": [0.772]}, "o": {"x": [0.167], "y": [0.11]}, "t": 136, "s": [4.998]}, {"i": {"x": [0.833], "y": [0.795]}, "o": {"x": [0.167], "y": [0.131]}, "t": 137, "s": [4.99]}, {"i": {"x": [0.833], "y": [0.805]}, "o": {"x": [0.167], "y": [0.14]}, "t": 138, "s": [4.978]}, {"i": {"x": [0.833], "y": [0.81]}, "o": {"x": [0.167], "y": [0.145]}, "t": 139, "s": [4.959]}, {"i": {"x": [0.833], "y": [0.813]}, "o": {"x": [0.167], "y": [0.148]}, "t": 140, "s": [4.935]}, {"i": {"x": [0.833], "y": [0.815]}, "o": {"x": [0.167], "y": [0.15]}, "t": 141, "s": [4.903]}, {"i": {"x": [0.833], "y": [0.817]}, "o": {"x": [0.167], "y": [0.152]}, "t": 142, "s": [4.863]}, {"i": {"x": [0.833], "y": [0.818]}, "o": {"x": [0.167], "y": [0.153]}, "t": 143, "s": [4.815]}, {"i": {"x": [0.833], "y": [0.818]}, "o": {"x": [0.167], "y": [0.153]}, "t": 144, "s": [4.758]}, {"i": {"x": [0.833], "y": [0.819]}, "o": {"x": [0.167], "y": [0.154]}, "t": 145, "s": [4.689]}, {"i": {"x": [0.833], "y": [0.819]}, "o": {"x": [0.167], "y": [0.154]}, "t": 146, "s": [4.608]}, {"i": {"x": [0.833], "y": [0.819]}, "o": {"x": [0.167], "y": [0.154]}, "t": 147, "s": [4.513]}, {"i": {"x": [0.833], "y": [0.818]}, "o": {"x": [0.167], "y": [0.154]}, "t": 148, "s": [4.401]}, {"i": {"x": [0.833], "y": [0.817]}, "o": {"x": [0.167], "y": [0.154]}, "t": 149, "s": [4.269]}, {"i": {"x": [0.833], "y": [0.816]}, "o": {"x": [0.167], "y": [0.153]}, "t": 150, "s": [4.114]}, {"i": {"x": [0.833], "y": [0.814]}, "o": {"x": [0.167], "y": [0.152]}, "t": 151, "s": [3.928]}, {"i": {"x": [0.833], "y": [0.812]}, "o": {"x": [0.167], "y": [0.151]}, "t": 152, "s": [3.704]}, {"i": {"x": [0.833], "y": [0.808]}, "o": {"x": [0.167], "y": [0.15]}, "t": 153, "s": [3.429]}, {"i": {"x": [0.833], "y": [0.805]}, "o": {"x": [0.167], "y": [0.147]}, "t": 154, "s": [3.083]}, {"i": {"x": [0.833], "y": [0.814]}, "o": {"x": [0.167], "y": [0.146]}, "t": 155, "s": [2.634]}, {"i": {"x": [0.833], "y": [0.846]}, "o": {"x": [0.167], "y": [0.151]}, "t": 156, "s": [2.035]}, {"i": {"x": [0.833], "y": [0.868]}, "o": {"x": [0.167], "y": [0.181]}, "t": 157, "s": [1.296]}, {"i": {"x": [0.833], "y": [0.874]}, "o": {"x": [0.167], "y": [0.226]}, "t": 158, "s": [0.667]}, {"i": {"x": [0.833], "y": [0.879]}, "o": {"x": [0.167], "y": [0.245]}, "t": 159, "s": [0.299]}, {"i": {"x": [0.833], "y": [0.894]}, "o": {"x": [0.167], "y": [0.266]}, "t": 160, "s": [0.11]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0.39]}, "t": 161, "s": [0.024]}, {"i": {"x": [0.833], "y": [0.61]}, "o": {"x": [0.167], "y": [0]}, "t": 162, "s": [0]}, {"i": {"x": [0.833], "y": [0.734]}, "o": {"x": [0.167], "y": [0.106]}, "t": 163, "s": [0.024]}, {"i": {"x": [0.833], "y": [0.755]}, "o": {"x": [0.167], "y": [0.121]}, "t": 164, "s": [0.11]}, {"i": {"x": [0.833], "y": [0.774]}, "o": {"x": [0.167], "y": [0.126]}, "t": 165, "s": [0.299]}, {"i": {"x": [0.833], "y": [0.819]}, "o": {"x": [0.167], "y": [0.132]}, "t": 166, "s": [0.667]}, {"i": {"x": [0.833], "y": [0.849]}, "o": {"x": [0.167], "y": [0.154]}, "t": 167, "s": [1.296]}, {"i": {"x": [0.833], "y": [0.854]}, "o": {"x": [0.167], "y": [0.186]}, "t": 168, "s": [2.035]}, {"i": {"x": [0.833], "y": [0.853]}, "o": {"x": [0.167], "y": [0.195]}, "t": 169, "s": [2.634]}, {"i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.192]}, "t": 170, "s": [3.083]}, {"i": {"x": [0.833], "y": [0.849]}, "o": {"x": [0.167], "y": [0.188]}, "t": 171, "s": [3.429]}, {"i": {"x": [0.833], "y": [0.848]}, "o": {"x": [0.167], "y": [0.186]}, "t": 172, "s": [3.704]}, {"i": {"x": [0.833], "y": [0.847]}, "o": {"x": [0.167], "y": [0.184]}, "t": 173, "s": [3.928]}, {"i": {"x": [0.833], "y": [0.846]}, "o": {"x": [0.167], "y": [0.183]}, "t": 174, "s": [4.114]}, {"i": {"x": [0.833], "y": [0.846]}, "o": {"x": [0.167], "y": [0.182]}, "t": 175, "s": [4.269]}, {"i": {"x": [0.833], "y": [0.846]}, "o": {"x": [0.167], "y": [0.181]}, "t": 176, "s": [4.401]}, {"i": {"x": [0.833], "y": [0.846]}, "o": {"x": [0.167], "y": [0.181]}, "t": 177, "s": [4.513]}, {"i": {"x": [0.833], "y": [0.846]}, "o": {"x": [0.167], "y": [0.181]}, "t": 178, "s": [4.608]}, {"i": {"x": [0.833], "y": [0.847]}, "o": {"x": [0.167], "y": [0.182]}, "t": 179, "s": [4.689]}, {"i": {"x": [0.833], "y": [0.847]}, "o": {"x": [0.167], "y": [0.182]}, "t": 180, "s": [4.758]}, {"i": {"x": [0.833], "y": [0.848]}, "o": {"x": [0.167], "y": [0.183]}, "t": 181, "s": [4.815]}, {"i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.185]}, "t": 182, "s": [4.863]}, {"i": {"x": [0.833], "y": [0.852]}, "o": {"x": [0.167], "y": [0.187]}, "t": 183, "s": [4.903]}, {"i": {"x": [0.833], "y": [0.855]}, "o": {"x": [0.167], "y": [0.19]}, "t": 184, "s": [4.935]}, {"i": {"x": [0.833], "y": [0.86]}, "o": {"x": [0.167], "y": [0.195]}, "t": 185, "s": [4.959]}, {"i": {"x": [0.833], "y": [0.869]}, "o": {"x": [0.167], "y": [0.205]}, "t": 186, "s": [4.978]}, {"i": {"x": [0.833], "y": [0.89]}, "o": {"x": [0.167], "y": [0.228]}, "t": 187, "s": [4.99]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0.342]}, "t": 188, "s": [4.998]}, {"i": {"x": [0.833], "y": [0.658]}, "o": {"x": [0.167], "y": [0]}, "t": 189, "s": [5]}, {"i": {"x": [0.833], "y": [0.772]}, "o": {"x": [0.167], "y": [0.11]}, "t": 190, "s": [4.998]}, {"i": {"x": [0.833], "y": [0.795]}, "o": {"x": [0.167], "y": [0.131]}, "t": 191, "s": [4.99]}, {"i": {"x": [0.833], "y": [0.805]}, "o": {"x": [0.167], "y": [0.14]}, "t": 192, "s": [4.978]}, {"i": {"x": [0.833], "y": [0.81]}, "o": {"x": [0.167], "y": [0.145]}, "t": 193, "s": [4.959]}, {"i": {"x": [0.833], "y": [0.813]}, "o": {"x": [0.167], "y": [0.148]}, "t": 194, "s": [4.935]}, {"i": {"x": [0.833], "y": [0.815]}, "o": {"x": [0.167], "y": [0.15]}, "t": 195, "s": [4.903]}, {"i": {"x": [0.833], "y": [0.817]}, "o": {"x": [0.167], "y": [0.152]}, "t": 196, "s": [4.863]}, {"i": {"x": [0.833], "y": [0.818]}, "o": {"x": [0.167], "y": [0.153]}, "t": 197, "s": [4.815]}, {"i": {"x": [0.833], "y": [0.818]}, "o": {"x": [0.167], "y": [0.153]}, "t": 198, "s": [4.758]}, {"i": {"x": [0.833], "y": [0.819]}, "o": {"x": [0.167], "y": [0.154]}, "t": 199, "s": [4.689]}, {"i": {"x": [0.833], "y": [0.819]}, "o": {"x": [0.167], "y": [0.154]}, "t": 200, "s": [4.608]}, {"i": {"x": [0.833], "y": [0.819]}, "o": {"x": [0.167], "y": [0.154]}, "t": 201, "s": [4.513]}, {"i": {"x": [0.833], "y": [0.818]}, "o": {"x": [0.167], "y": [0.154]}, "t": 202, "s": [4.401]}, {"i": {"x": [0.833], "y": [0.817]}, "o": {"x": [0.167], "y": [0.154]}, "t": 203, "s": [4.269]}, {"i": {"x": [0.833], "y": [0.816]}, "o": {"x": [0.167], "y": [0.153]}, "t": 204, "s": [4.114]}, {"i": {"x": [0.833], "y": [0.814]}, "o": {"x": [0.167], "y": [0.152]}, "t": 205, "s": [3.928]}, {"i": {"x": [0.833], "y": [0.812]}, "o": {"x": [0.167], "y": [0.151]}, "t": 206, "s": [3.704]}, {"i": {"x": [0.833], "y": [0.808]}, "o": {"x": [0.167], "y": [0.15]}, "t": 207, "s": [3.429]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.147]}, "t": 208, "s": [3.083]}, {"t": 209.000008512745, "s": [2.634]}], "ix": 10}, "p": {"a": 0, "k": [336.1, 287.127, 0], "ix": 2}, "a": {"a": 0, "k": [68.352, 31.347, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[11.104, -2.076], [3.528, 7.265], [0.83, 1.868], [-5.189, -0.207], [-3.32, -5.189]], "o": [[0, 0], [-3.529, -7.263], [0, 0], [0, 0], [3.321, 5.188]], "v": [[4.67, 18.991], [-5.395, 12.971], [-15.773, -15.67], [-8.509, -20.029], [0.622, -3.424]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.745000023935, 0.238999998803, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [28.425, 36.208], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0.156, 1.09], [0.623, 2.49], [0.467, 1.712], [-0.934, -2.335], [0.934, -0.156], [1.089, 1.245], [0.623, -0.778], [-2.18, -2.335], [-0.155, -0.623], [-0.778, -1.868], [-1.401, -1.712], [-1.246, -1.09], [0, 0]], "o": [[0, 0], [-0.156, -1.089], [-0.622, -2.49], [-1.245, -1.556], [0.935, 2.335], [-0.934, 0.155], [-1.089, -1.245], [-0.623, 0.778], [2.179, 2.335], [0.155, 0.623], [0.779, 1.867], [1.401, 1.712], [0, 0], [0, 0]], "v": [[9.729, 6.848], [8.795, 4.202], [8.639, -0.779], [6.46, -7.16], [5.059, -3.58], [4.748, 0.312], [-1.167, -3.113], [-9.106, -9.652], [-7.549, -5.759], [-4.436, -1.712], [-3.658, 1.402], [-2.101, 6.227], [3.192, 9.34], [4.437, 10.43]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.666999966491, 0.513999968884, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [9.979, 10.68], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 3, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-3.113, 0.311], [-1.247, -5.293], [2.802, -1.557], [4.36, 0], [-2.802, 4.67]], "o": [[0, 0], [3.736, -0.312], [1.245, 5.292], [-2.803, 1.556], [-4.358, 0], [2.802, -4.67]], "v": [[-7.005, -2.024], [11.363, -15.099], [21.015, -8.873], [16.656, 2.024], [-15.1, 15.411], [-19.458, 7.004]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.745000023935, 0.238999998803, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [48.194, 39.788], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 3, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 94.0000038286985, "st": 0, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "Layer 13 Outlines", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.605}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [283.919, 251.998, 0], "to": [0.002, -0.004, 0], "ti": [-0.007, 0.018, 0]}, {"i": {"x": 0.833, "y": 0.732}, "o": {"x": 0.167, "y": 0.106}, "t": 1, "s": [283.928, 251.975, 0], "to": [0.007, -0.018, 0], "ti": [-0.019, 0.046, 0]}, {"i": {"x": 0.833, "y": 0.756}, "o": {"x": 0.167, "y": 0.121}, "t": 2, "s": [283.963, 251.889, 0], "to": [0.019, -0.046, 0], "ti": [-0.037, 0.094, 0]}, {"i": {"x": 0.833, "y": 0.776}, "o": {"x": 0.167, "y": 0.127}, "t": 3, "s": [284.039, 251.697, 0], "to": [0.037, -0.094, 0], "ti": [-0.066, 0.166, 0]}, {"i": {"x": 0.833, "y": 0.818}, "o": {"x": 0.167, "y": 0.133}, "t": 4, "s": [284.187, 251.327, 0], "to": [0.066, -0.166, 0], "ti": [-0.091, 0.227, 0]}, {"i": {"x": 0.833, "y": 0.849}, "o": {"x": 0.167, "y": 0.154}, "t": 5, "s": [284.437, 250.702, 0], "to": [0.091, -0.227, 0], "ti": [-0.089, 0.223, 0]}, {"i": {"x": 0.833, "y": 0.854}, "o": {"x": 0.167, "y": 0.186}, "t": 6, "s": [284.733, 249.963, 0], "to": [0.089, -0.223, 0], "ti": [-0.07, 0.174, 0]}, {"i": {"x": 0.833, "y": 0.852}, "o": {"x": 0.167, "y": 0.194}, "t": 7, "s": [284.972, 249.364, 0], "to": [0.07, -0.174, 0], "ti": [-0.053, 0.132, 0]}, {"i": {"x": 0.833, "y": 0.85}, "o": {"x": 0.167, "y": 0.191}, "t": 8, "s": [285.152, 248.916, 0], "to": [0.053, -0.132, 0], "ti": [-0.041, 0.104, 0]}, {"i": {"x": 0.833, "y": 0.849}, "o": {"x": 0.167, "y": 0.188}, "t": 9, "s": [285.29, 248.57, 0], "to": [0.041, -0.104, 0], "ti": [-0.033, 0.083, 0]}, {"i": {"x": 0.833, "y": 0.848}, "o": {"x": 0.167, "y": 0.186}, "t": 10, "s": [285.4, 248.295, 0], "to": [0.033, -0.083, 0], "ti": [-0.027, 0.068, 0]}, {"i": {"x": 0.833, "y": 0.847}, "o": {"x": 0.167, "y": 0.184}, "t": 11, "s": [285.489, 248.072, 0], "to": [0.027, -0.068, 0], "ti": [-0.023, 0.056, 0]}, {"i": {"x": 0.833, "y": 0.846}, "o": {"x": 0.167, "y": 0.182}, "t": 12, "s": [285.563, 247.889, 0], "to": [0.023, -0.056, 0], "ti": [-0.019, 0.048, 0]}, {"i": {"x": 0.833, "y": 0.845}, "o": {"x": 0.167, "y": 0.181}, "t": 13, "s": [285.624, 247.734, 0], "to": [0.019, -0.048, 0], "ti": [-0.016, 0.041, 0]}, {"i": {"x": 0.833, "y": 0.845}, "o": {"x": 0.167, "y": 0.181}, "t": 14, "s": [285.677, 247.603, 0], "to": [0.016, -0.041, 0], "ti": [-0.014, 0.035, 0]}, {"i": {"x": 0.833, "y": 0.846}, "o": {"x": 0.167, "y": 0.181}, "t": 15, "s": [285.722, 247.49, 0], "to": [0.014, -0.035, 0], "ti": [-0.012, 0.03, 0]}, {"i": {"x": 0.833, "y": 0.846}, "o": {"x": 0.167, "y": 0.181}, "t": 16, "s": [285.76, 247.394, 0], "to": [0.012, -0.03, 0], "ti": [-0.01, 0.025, 0]}, {"i": {"x": 0.833, "y": 0.847}, "o": {"x": 0.167, "y": 0.182}, "t": 17, "s": [285.793, 247.312, 0], "to": [0.01, -0.025, 0], "ti": [-0.009, 0.021, 0]}, {"i": {"x": 0.833, "y": 0.847}, "o": {"x": 0.167, "y": 0.182}, "t": 18, "s": [285.821, 247.242, 0], "to": [0.009, -0.021, 0], "ti": [-0.007, 0.018, 0]}, {"i": {"x": 0.833, "y": 0.849}, "o": {"x": 0.167, "y": 0.184}, "t": 19, "s": [285.845, 247.184, 0], "to": [0.007, -0.018, 0], "ti": [-0.006, 0.015, 0]}, {"i": {"x": 0.833, "y": 0.85}, "o": {"x": 0.167, "y": 0.185}, "t": 20, "s": [285.864, 247.135, 0], "to": [0.006, -0.015, 0], "ti": [-0.005, 0.012, 0]}, {"i": {"x": 0.833, "y": 0.852}, "o": {"x": 0.167, "y": 0.187}, "t": 21, "s": [285.88, 247.095, 0], "to": [0.005, -0.012, 0], "ti": [-0.004, 0.009, 0]}, {"i": {"x": 0.833, "y": 0.855}, "o": {"x": 0.167, "y": 0.191}, "t": 22, "s": [285.893, 247.063, 0], "to": [0.004, -0.009, 0], "ti": [-0.003, 0.007, 0]}, {"i": {"x": 0.833, "y": 0.86}, "o": {"x": 0.167, "y": 0.196}, "t": 23, "s": [285.903, 247.039, 0], "to": [0.003, -0.007, 0], "ti": [-0.002, 0.005, 0]}, {"i": {"x": 0.833, "y": 0.869}, "o": {"x": 0.167, "y": 0.206}, "t": 24, "s": [285.91, 247.02, 0], "to": [0.002, -0.005, 0], "ti": [-0.001, 0.003, 0]}, {"i": {"x": 0.833, "y": 0.89}, "o": {"x": 0.167, "y": 0.228}, "t": 25, "s": [285.915, 247.008, 0], "to": [0.001, -0.003, 0], "ti": [-0.001, 0.002, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.341}, "t": 26, "s": [285.918, 247.001, 0], "to": [0.001, -0.002, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.659}, "o": {"x": 0.167, "y": 0.166}, "t": 27, "s": [285.919, 246.998, 0], "to": [0, 0, 0], "ti": [0.001, -0.002, 0]}, {"i": {"x": 0.833, "y": 0.772}, "o": {"x": 0.167, "y": 0.11}, "t": 28, "s": [285.918, 247.001, 0], "to": [-0.001, 0.002, 0], "ti": [0.001, -0.003, 0]}, {"i": {"x": 0.833, "y": 0.794}, "o": {"x": 0.167, "y": 0.131}, "t": 29, "s": [285.915, 247.008, 0], "to": [-0.001, 0.003, 0], "ti": [0.002, -0.005, 0]}, {"i": {"x": 0.833, "y": 0.804}, "o": {"x": 0.167, "y": 0.14}, "t": 30, "s": [285.91, 247.02, 0], "to": [-0.002, 0.005, 0], "ti": [0.003, -0.007, 0]}, {"i": {"x": 0.833, "y": 0.809}, "o": {"x": 0.167, "y": 0.145}, "t": 31, "s": [285.903, 247.039, 0], "to": [-0.003, 0.007, 0], "ti": [0.004, -0.009, 0]}, {"i": {"x": 0.833, "y": 0.813}, "o": {"x": 0.167, "y": 0.148}, "t": 32, "s": [285.893, 247.063, 0], "to": [-0.004, 0.009, 0], "ti": [0.005, -0.012, 0]}, {"i": {"x": 0.833, "y": 0.815}, "o": {"x": 0.167, "y": 0.15}, "t": 33, "s": [285.88, 247.095, 0], "to": [-0.005, 0.012, 0], "ti": [0.006, -0.015, 0]}, {"i": {"x": 0.833, "y": 0.816}, "o": {"x": 0.167, "y": 0.151}, "t": 34, "s": [285.864, 247.135, 0], "to": [-0.006, 0.015, 0], "ti": [0.007, -0.018, 0]}, {"i": {"x": 0.833, "y": 0.818}, "o": {"x": 0.167, "y": 0.153}, "t": 35, "s": [285.845, 247.184, 0], "to": [-0.007, 0.018, 0], "ti": [0.009, -0.021, 0]}, {"i": {"x": 0.833, "y": 0.818}, "o": {"x": 0.167, "y": 0.153}, "t": 36, "s": [285.821, 247.242, 0], "to": [-0.009, 0.021, 0], "ti": [0.01, -0.025, 0]}, {"i": {"x": 0.833, "y": 0.819}, "o": {"x": 0.167, "y": 0.154}, "t": 37, "s": [285.793, 247.312, 0], "to": [-0.01, 0.025, 0], "ti": [0.012, -0.03, 0]}, {"i": {"x": 0.833, "y": 0.819}, "o": {"x": 0.167, "y": 0.154}, "t": 38, "s": [285.76, 247.394, 0], "to": [-0.012, 0.03, 0], "ti": [0.014, -0.035, 0]}, {"i": {"x": 0.833, "y": 0.819}, "o": {"x": 0.167, "y": 0.155}, "t": 39, "s": [285.722, 247.49, 0], "to": [-0.014, 0.035, 0], "ti": [0.016, -0.041, 0]}, {"i": {"x": 0.833, "y": 0.819}, "o": {"x": 0.167, "y": 0.155}, "t": 40, "s": [285.677, 247.603, 0], "to": [-0.016, 0.041, 0], "ti": [0.019, -0.048, 0]}, {"i": {"x": 0.833, "y": 0.818}, "o": {"x": 0.167, "y": 0.154}, "t": 41, "s": [285.624, 247.734, 0], "to": [-0.019, 0.048, 0], "ti": [0.023, -0.056, 0]}, {"i": {"x": 0.833, "y": 0.816}, "o": {"x": 0.167, "y": 0.153}, "t": 42, "s": [285.563, 247.889, 0], "to": [-0.023, 0.056, 0], "ti": [0.027, -0.068, 0]}, {"i": {"x": 0.833, "y": 0.814}, "o": {"x": 0.167, "y": 0.152}, "t": 43, "s": [285.489, 248.072, 0], "to": [-0.027, 0.068, 0], "ti": [0.033, -0.083, 0]}, {"i": {"x": 0.833, "y": 0.812}, "o": {"x": 0.167, "y": 0.151}, "t": 44, "s": [285.4, 248.295, 0], "to": [-0.033, 0.083, 0], "ti": [0.041, -0.104, 0]}, {"i": {"x": 0.833, "y": 0.809}, "o": {"x": 0.167, "y": 0.15}, "t": 45, "s": [285.29, 248.57, 0], "to": [-0.041, 0.104, 0], "ti": [0.053, -0.132, 0]}, {"i": {"x": 0.833, "y": 0.806}, "o": {"x": 0.167, "y": 0.148}, "t": 46, "s": [285.152, 248.916, 0], "to": [-0.053, 0.132, 0], "ti": [0.07, -0.174, 0]}, {"i": {"x": 0.833, "y": 0.814}, "o": {"x": 0.167, "y": 0.146}, "t": 47, "s": [284.972, 249.364, 0], "to": [-0.07, 0.174, 0], "ti": [0.089, -0.223, 0]}, {"i": {"x": 0.833, "y": 0.846}, "o": {"x": 0.167, "y": 0.151}, "t": 48, "s": [284.733, 249.963, 0], "to": [-0.089, 0.223, 0], "ti": [0.091, -0.227, 0]}, {"i": {"x": 0.833, "y": 0.867}, "o": {"x": 0.167, "y": 0.182}, "t": 49, "s": [284.437, 250.702, 0], "to": [-0.091, 0.227, 0], "ti": [0.066, -0.166, 0]}, {"i": {"x": 0.833, "y": 0.873}, "o": {"x": 0.167, "y": 0.224}, "t": 50, "s": [284.187, 251.327, 0], "to": [-0.066, 0.166, 0], "ti": [0.037, -0.094, 0]}, {"i": {"x": 0.833, "y": 0.879}, "o": {"x": 0.167, "y": 0.244}, "t": 51, "s": [284.039, 251.697, 0], "to": [-0.037, 0.094, 0], "ti": [0.019, -0.046, 0]}, {"i": {"x": 0.833, "y": 0.894}, "o": {"x": 0.167, "y": 0.268}, "t": 52, "s": [283.963, 251.889, 0], "to": [-0.019, 0.046, 0], "ti": [0.007, -0.018, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.395}, "t": 53, "s": [283.928, 251.975, 0], "to": [-0.007, 0.018, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.605}, "o": {"x": 0.167, "y": 0.167}, "t": 54, "s": [283.919, 251.998, 0], "to": [0, 0, 0], "ti": [-0.007, 0.018, 0]}, {"i": {"x": 0.833, "y": 0.732}, "o": {"x": 0.167, "y": 0.106}, "t": 55, "s": [283.928, 251.975, 0], "to": [0.007, -0.018, 0], "ti": [-0.019, 0.046, 0]}, {"i": {"x": 0.833, "y": 0.756}, "o": {"x": 0.167, "y": 0.121}, "t": 56, "s": [283.963, 251.889, 0], "to": [0.019, -0.046, 0], "ti": [-0.037, 0.094, 0]}, {"i": {"x": 0.833, "y": 0.776}, "o": {"x": 0.167, "y": 0.127}, "t": 57, "s": [284.039, 251.697, 0], "to": [0.037, -0.094, 0], "ti": [-0.066, 0.166, 0]}, {"i": {"x": 0.833, "y": 0.818}, "o": {"x": 0.167, "y": 0.133}, "t": 58, "s": [284.187, 251.327, 0], "to": [0.066, -0.166, 0], "ti": [-0.091, 0.227, 0]}, {"i": {"x": 0.833, "y": 0.849}, "o": {"x": 0.167, "y": 0.154}, "t": 59, "s": [284.437, 250.702, 0], "to": [0.091, -0.227, 0], "ti": [-0.089, 0.223, 0]}, {"i": {"x": 0.833, "y": 0.854}, "o": {"x": 0.167, "y": 0.186}, "t": 60, "s": [284.733, 249.963, 0], "to": [0.089, -0.223, 0], "ti": [-0.07, 0.174, 0]}, {"i": {"x": 0.833, "y": 0.852}, "o": {"x": 0.167, "y": 0.194}, "t": 61, "s": [284.972, 249.364, 0], "to": [0.07, -0.174, 0], "ti": [-0.053, 0.132, 0]}, {"i": {"x": 0.833, "y": 0.85}, "o": {"x": 0.167, "y": 0.191}, "t": 62, "s": [285.152, 248.916, 0], "to": [0.053, -0.132, 0], "ti": [-0.041, 0.104, 0]}, {"i": {"x": 0.833, "y": 0.849}, "o": {"x": 0.167, "y": 0.188}, "t": 63, "s": [285.29, 248.57, 0], "to": [0.041, -0.104, 0], "ti": [-0.033, 0.083, 0]}, {"i": {"x": 0.833, "y": 0.848}, "o": {"x": 0.167, "y": 0.186}, "t": 64, "s": [285.4, 248.295, 0], "to": [0.033, -0.083, 0], "ti": [-0.027, 0.068, 0]}, {"i": {"x": 0.833, "y": 0.847}, "o": {"x": 0.167, "y": 0.184}, "t": 65, "s": [285.489, 248.072, 0], "to": [0.027, -0.068, 0], "ti": [-0.023, 0.056, 0]}, {"i": {"x": 0.833, "y": 0.846}, "o": {"x": 0.167, "y": 0.182}, "t": 66, "s": [285.563, 247.889, 0], "to": [0.023, -0.056, 0], "ti": [-0.019, 0.048, 0]}, {"i": {"x": 0.833, "y": 0.845}, "o": {"x": 0.167, "y": 0.181}, "t": 67, "s": [285.624, 247.734, 0], "to": [0.019, -0.048, 0], "ti": [-0.016, 0.041, 0]}, {"i": {"x": 0.833, "y": 0.845}, "o": {"x": 0.167, "y": 0.181}, "t": 68, "s": [285.677, 247.603, 0], "to": [0.016, -0.041, 0], "ti": [-0.014, 0.035, 0]}, {"i": {"x": 0.833, "y": 0.846}, "o": {"x": 0.167, "y": 0.181}, "t": 69, "s": [285.722, 247.49, 0], "to": [0.014, -0.035, 0], "ti": [-0.012, 0.03, 0]}, {"i": {"x": 0.833, "y": 0.846}, "o": {"x": 0.167, "y": 0.181}, "t": 70, "s": [285.76, 247.394, 0], "to": [0.012, -0.03, 0], "ti": [-0.01, 0.025, 0]}, {"i": {"x": 0.833, "y": 0.847}, "o": {"x": 0.167, "y": 0.182}, "t": 71, "s": [285.793, 247.312, 0], "to": [0.01, -0.025, 0], "ti": [-0.009, 0.021, 0]}, {"i": {"x": 0.833, "y": 0.847}, "o": {"x": 0.167, "y": 0.182}, "t": 72, "s": [285.821, 247.242, 0], "to": [0.009, -0.021, 0], "ti": [-0.007, 0.018, 0]}, {"i": {"x": 0.833, "y": 0.849}, "o": {"x": 0.167, "y": 0.184}, "t": 73, "s": [285.845, 247.184, 0], "to": [0.007, -0.018, 0], "ti": [-0.006, 0.015, 0]}, {"i": {"x": 0.833, "y": 0.85}, "o": {"x": 0.167, "y": 0.185}, "t": 74, "s": [285.864, 247.135, 0], "to": [0.006, -0.015, 0], "ti": [-0.005, 0.012, 0]}, {"i": {"x": 0.833, "y": 0.852}, "o": {"x": 0.167, "y": 0.187}, "t": 75, "s": [285.88, 247.095, 0], "to": [0.005, -0.012, 0], "ti": [-0.004, 0.009, 0]}, {"i": {"x": 0.833, "y": 0.855}, "o": {"x": 0.167, "y": 0.191}, "t": 76, "s": [285.893, 247.063, 0], "to": [0.004, -0.009, 0], "ti": [-0.003, 0.007, 0]}, {"i": {"x": 0.833, "y": 0.86}, "o": {"x": 0.167, "y": 0.196}, "t": 77, "s": [285.903, 247.039, 0], "to": [0.003, -0.007, 0], "ti": [-0.002, 0.005, 0]}, {"i": {"x": 0.833, "y": 0.869}, "o": {"x": 0.167, "y": 0.206}, "t": 78, "s": [285.91, 247.02, 0], "to": [0.002, -0.005, 0], "ti": [-0.001, 0.003, 0]}, {"i": {"x": 0.833, "y": 0.89}, "o": {"x": 0.167, "y": 0.228}, "t": 79, "s": [285.915, 247.008, 0], "to": [0.001, -0.003, 0], "ti": [-0.001, 0.002, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.341}, "t": 80, "s": [285.918, 247.001, 0], "to": [0.001, -0.002, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.659}, "o": {"x": 0.167, "y": 0.166}, "t": 81, "s": [285.919, 246.998, 0], "to": [0, 0, 0], "ti": [0.001, -0.002, 0]}, {"i": {"x": 0.833, "y": 0.772}, "o": {"x": 0.167, "y": 0.11}, "t": 82, "s": [285.918, 247.001, 0], "to": [-0.001, 0.002, 0], "ti": [0.001, -0.003, 0]}, {"i": {"x": 0.833, "y": 0.794}, "o": {"x": 0.167, "y": 0.131}, "t": 83, "s": [285.915, 247.008, 0], "to": [-0.001, 0.003, 0], "ti": [0.002, -0.005, 0]}, {"i": {"x": 0.833, "y": 0.804}, "o": {"x": 0.167, "y": 0.14}, "t": 84, "s": [285.91, 247.02, 0], "to": [-0.002, 0.005, 0], "ti": [0.003, -0.007, 0]}, {"i": {"x": 0.833, "y": 0.809}, "o": {"x": 0.167, "y": 0.145}, "t": 85, "s": [285.903, 247.039, 0], "to": [-0.003, 0.007, 0], "ti": [0.004, -0.009, 0]}, {"i": {"x": 0.833, "y": 0.813}, "o": {"x": 0.167, "y": 0.148}, "t": 86, "s": [285.893, 247.063, 0], "to": [-0.004, 0.009, 0], "ti": [0.005, -0.012, 0]}, {"i": {"x": 0.833, "y": 0.815}, "o": {"x": 0.167, "y": 0.15}, "t": 87, "s": [285.88, 247.095, 0], "to": [-0.005, 0.012, 0], "ti": [0.006, -0.015, 0]}, {"i": {"x": 0.833, "y": 0.816}, "o": {"x": 0.167, "y": 0.151}, "t": 88, "s": [285.864, 247.135, 0], "to": [-0.006, 0.015, 0], "ti": [0.007, -0.018, 0]}, {"i": {"x": 0.833, "y": 0.818}, "o": {"x": 0.167, "y": 0.153}, "t": 89, "s": [285.845, 247.184, 0], "to": [-0.007, 0.018, 0], "ti": [0.009, -0.021, 0]}, {"i": {"x": 0.833, "y": 0.818}, "o": {"x": 0.167, "y": 0.153}, "t": 90, "s": [285.821, 247.242, 0], "to": [-0.009, 0.021, 0], "ti": [0.01, -0.025, 0]}, {"i": {"x": 0.833, "y": 0.819}, "o": {"x": 0.167, "y": 0.154}, "t": 91, "s": [285.793, 247.312, 0], "to": [-0.01, 0.025, 0], "ti": [0.012, -0.03, 0]}, {"i": {"x": 0.833, "y": 0.819}, "o": {"x": 0.167, "y": 0.154}, "t": 92, "s": [285.76, 247.394, 0], "to": [-0.012, 0.03, 0], "ti": [0.014, -0.035, 0]}, {"i": {"x": 0.833, "y": 0.819}, "o": {"x": 0.167, "y": 0.155}, "t": 93, "s": [285.722, 247.49, 0], "to": [-0.014, 0.035, 0], "ti": [0.016, -0.041, 0]}, {"i": {"x": 0.833, "y": 0.819}, "o": {"x": 0.167, "y": 0.155}, "t": 94, "s": [285.677, 247.603, 0], "to": [-0.016, 0.041, 0], "ti": [0.019, -0.048, 0]}, {"i": {"x": 0.833, "y": 0.818}, "o": {"x": 0.167, "y": 0.154}, "t": 95, "s": [285.624, 247.734, 0], "to": [-0.019, 0.048, 0], "ti": [0.023, -0.056, 0]}, {"i": {"x": 0.833, "y": 0.816}, "o": {"x": 0.167, "y": 0.153}, "t": 96, "s": [285.563, 247.889, 0], "to": [-0.023, 0.056, 0], "ti": [0.027, -0.068, 0]}, {"i": {"x": 0.833, "y": 0.814}, "o": {"x": 0.167, "y": 0.152}, "t": 97, "s": [285.489, 248.072, 0], "to": [-0.027, 0.068, 0], "ti": [0.033, -0.083, 0]}, {"i": {"x": 0.833, "y": 0.812}, "o": {"x": 0.167, "y": 0.151}, "t": 98, "s": [285.4, 248.295, 0], "to": [-0.033, 0.083, 0], "ti": [0.041, -0.104, 0]}, {"i": {"x": 0.833, "y": 0.809}, "o": {"x": 0.167, "y": 0.15}, "t": 99, "s": [285.29, 248.57, 0], "to": [-0.041, 0.104, 0], "ti": [0.053, -0.132, 0]}, {"i": {"x": 0.833, "y": 0.806}, "o": {"x": 0.167, "y": 0.148}, "t": 100, "s": [285.152, 248.916, 0], "to": [-0.053, 0.132, 0], "ti": [0.07, -0.174, 0]}, {"i": {"x": 0.833, "y": 0.814}, "o": {"x": 0.167, "y": 0.146}, "t": 101, "s": [284.972, 249.364, 0], "to": [-0.07, 0.174, 0], "ti": [0.089, -0.223, 0]}, {"i": {"x": 0.833, "y": 0.846}, "o": {"x": 0.167, "y": 0.151}, "t": 102, "s": [284.733, 249.963, 0], "to": [-0.089, 0.223, 0], "ti": [0.091, -0.227, 0]}, {"i": {"x": 0.833, "y": 0.867}, "o": {"x": 0.167, "y": 0.182}, "t": 103, "s": [284.437, 250.702, 0], "to": [-0.091, 0.227, 0], "ti": [0.066, -0.166, 0]}, {"i": {"x": 0.833, "y": 0.873}, "o": {"x": 0.167, "y": 0.224}, "t": 104, "s": [284.187, 251.327, 0], "to": [-0.066, 0.166, 0], "ti": [0.037, -0.094, 0]}, {"i": {"x": 0.833, "y": 0.879}, "o": {"x": 0.167, "y": 0.244}, "t": 105, "s": [284.039, 251.697, 0], "to": [-0.037, 0.094, 0], "ti": [0.019, -0.046, 0]}, {"i": {"x": 0.833, "y": 0.894}, "o": {"x": 0.167, "y": 0.268}, "t": 106, "s": [283.963, 251.889, 0], "to": [-0.019, 0.046, 0], "ti": [0.007, -0.018, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.395}, "t": 107, "s": [283.928, 251.975, 0], "to": [-0.007, 0.018, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.605}, "o": {"x": 0.167, "y": 0.167}, "t": 108, "s": [283.919, 251.998, 0], "to": [0, 0, 0], "ti": [-0.007, 0.018, 0]}, {"i": {"x": 0.833, "y": 0.732}, "o": {"x": 0.167, "y": 0.106}, "t": 109, "s": [283.928, 251.975, 0], "to": [0.007, -0.018, 0], "ti": [-0.019, 0.046, 0]}, {"i": {"x": 0.833, "y": 0.756}, "o": {"x": 0.167, "y": 0.121}, "t": 110, "s": [283.963, 251.889, 0], "to": [0.019, -0.046, 0], "ti": [-0.037, 0.094, 0]}, {"i": {"x": 0.833, "y": 0.776}, "o": {"x": 0.167, "y": 0.127}, "t": 111, "s": [284.039, 251.697, 0], "to": [0.037, -0.094, 0], "ti": [-0.066, 0.166, 0]}, {"i": {"x": 0.833, "y": 0.818}, "o": {"x": 0.167, "y": 0.133}, "t": 112, "s": [284.187, 251.327, 0], "to": [0.066, -0.166, 0], "ti": [-0.091, 0.227, 0]}, {"i": {"x": 0.833, "y": 0.849}, "o": {"x": 0.167, "y": 0.154}, "t": 113, "s": [284.437, 250.702, 0], "to": [0.091, -0.227, 0], "ti": [-0.089, 0.223, 0]}, {"i": {"x": 0.833, "y": 0.854}, "o": {"x": 0.167, "y": 0.186}, "t": 114, "s": [284.733, 249.963, 0], "to": [0.089, -0.223, 0], "ti": [-0.07, 0.174, 0]}, {"i": {"x": 0.833, "y": 0.852}, "o": {"x": 0.167, "y": 0.194}, "t": 115, "s": [284.972, 249.364, 0], "to": [0.07, -0.174, 0], "ti": [-0.053, 0.132, 0]}, {"i": {"x": 0.833, "y": 0.85}, "o": {"x": 0.167, "y": 0.191}, "t": 116, "s": [285.152, 248.916, 0], "to": [0.053, -0.132, 0], "ti": [-0.041, 0.104, 0]}, {"i": {"x": 0.833, "y": 0.849}, "o": {"x": 0.167, "y": 0.188}, "t": 117, "s": [285.29, 248.57, 0], "to": [0.041, -0.104, 0], "ti": [-0.033, 0.083, 0]}, {"i": {"x": 0.833, "y": 0.848}, "o": {"x": 0.167, "y": 0.186}, "t": 118, "s": [285.4, 248.295, 0], "to": [0.033, -0.083, 0], "ti": [-0.027, 0.068, 0]}, {"i": {"x": 0.833, "y": 0.847}, "o": {"x": 0.167, "y": 0.184}, "t": 119, "s": [285.489, 248.072, 0], "to": [0.027, -0.068, 0], "ti": [-0.023, 0.056, 0]}, {"i": {"x": 0.833, "y": 0.846}, "o": {"x": 0.167, "y": 0.182}, "t": 120, "s": [285.563, 247.889, 0], "to": [0.023, -0.056, 0], "ti": [-0.019, 0.048, 0]}, {"i": {"x": 0.833, "y": 0.845}, "o": {"x": 0.167, "y": 0.181}, "t": 121, "s": [285.624, 247.734, 0], "to": [0.019, -0.048, 0], "ti": [-0.016, 0.041, 0]}, {"i": {"x": 0.833, "y": 0.845}, "o": {"x": 0.167, "y": 0.181}, "t": 122, "s": [285.677, 247.603, 0], "to": [0.016, -0.041, 0], "ti": [-0.014, 0.035, 0]}, {"i": {"x": 0.833, "y": 0.846}, "o": {"x": 0.167, "y": 0.181}, "t": 123, "s": [285.722, 247.49, 0], "to": [0.014, -0.035, 0], "ti": [-0.012, 0.03, 0]}, {"i": {"x": 0.833, "y": 0.846}, "o": {"x": 0.167, "y": 0.181}, "t": 124, "s": [285.76, 247.394, 0], "to": [0.012, -0.03, 0], "ti": [-0.01, 0.025, 0]}, {"i": {"x": 0.833, "y": 0.847}, "o": {"x": 0.167, "y": 0.182}, "t": 125, "s": [285.793, 247.312, 0], "to": [0.01, -0.025, 0], "ti": [-0.009, 0.021, 0]}, {"i": {"x": 0.833, "y": 0.847}, "o": {"x": 0.167, "y": 0.182}, "t": 126, "s": [285.821, 247.242, 0], "to": [0.009, -0.021, 0], "ti": [-0.007, 0.018, 0]}, {"i": {"x": 0.833, "y": 0.849}, "o": {"x": 0.167, "y": 0.184}, "t": 127, "s": [285.845, 247.184, 0], "to": [0.007, -0.018, 0], "ti": [-0.006, 0.015, 0]}, {"i": {"x": 0.833, "y": 0.85}, "o": {"x": 0.167, "y": 0.185}, "t": 128, "s": [285.864, 247.135, 0], "to": [0.006, -0.015, 0], "ti": [-0.005, 0.012, 0]}, {"i": {"x": 0.833, "y": 0.852}, "o": {"x": 0.167, "y": 0.187}, "t": 129, "s": [285.88, 247.095, 0], "to": [0.005, -0.012, 0], "ti": [-0.004, 0.009, 0]}, {"i": {"x": 0.833, "y": 0.855}, "o": {"x": 0.167, "y": 0.191}, "t": 130, "s": [285.893, 247.063, 0], "to": [0.004, -0.009, 0], "ti": [-0.003, 0.007, 0]}, {"i": {"x": 0.833, "y": 0.86}, "o": {"x": 0.167, "y": 0.196}, "t": 131, "s": [285.903, 247.039, 0], "to": [0.003, -0.007, 0], "ti": [-0.002, 0.005, 0]}, {"i": {"x": 0.833, "y": 0.869}, "o": {"x": 0.167, "y": 0.206}, "t": 132, "s": [285.91, 247.02, 0], "to": [0.002, -0.005, 0], "ti": [-0.001, 0.003, 0]}, {"i": {"x": 0.833, "y": 0.89}, "o": {"x": 0.167, "y": 0.228}, "t": 133, "s": [285.915, 247.008, 0], "to": [0.001, -0.003, 0], "ti": [-0.001, 0.002, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.341}, "t": 134, "s": [285.918, 247.001, 0], "to": [0.001, -0.002, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.659}, "o": {"x": 0.167, "y": 0.166}, "t": 135, "s": [285.919, 246.998, 0], "to": [0, 0, 0], "ti": [0.001, -0.002, 0]}, {"i": {"x": 0.833, "y": 0.772}, "o": {"x": 0.167, "y": 0.11}, "t": 136, "s": [285.918, 247.001, 0], "to": [-0.001, 0.002, 0], "ti": [0.001, -0.003, 0]}, {"i": {"x": 0.833, "y": 0.794}, "o": {"x": 0.167, "y": 0.131}, "t": 137, "s": [285.915, 247.008, 0], "to": [-0.001, 0.003, 0], "ti": [0.002, -0.005, 0]}, {"i": {"x": 0.833, "y": 0.804}, "o": {"x": 0.167, "y": 0.14}, "t": 138, "s": [285.91, 247.02, 0], "to": [-0.002, 0.005, 0], "ti": [0.003, -0.007, 0]}, {"i": {"x": 0.833, "y": 0.809}, "o": {"x": 0.167, "y": 0.145}, "t": 139, "s": [285.903, 247.039, 0], "to": [-0.003, 0.007, 0], "ti": [0.004, -0.009, 0]}, {"i": {"x": 0.833, "y": 0.813}, "o": {"x": 0.167, "y": 0.148}, "t": 140, "s": [285.893, 247.063, 0], "to": [-0.004, 0.009, 0], "ti": [0.005, -0.012, 0]}, {"i": {"x": 0.833, "y": 0.815}, "o": {"x": 0.167, "y": 0.15}, "t": 141, "s": [285.88, 247.095, 0], "to": [-0.005, 0.012, 0], "ti": [0.006, -0.015, 0]}, {"i": {"x": 0.833, "y": 0.816}, "o": {"x": 0.167, "y": 0.151}, "t": 142, "s": [285.864, 247.135, 0], "to": [-0.006, 0.015, 0], "ti": [0.007, -0.018, 0]}, {"i": {"x": 0.833, "y": 0.818}, "o": {"x": 0.167, "y": 0.153}, "t": 143, "s": [285.845, 247.184, 0], "to": [-0.007, 0.018, 0], "ti": [0.009, -0.021, 0]}, {"i": {"x": 0.833, "y": 0.818}, "o": {"x": 0.167, "y": 0.153}, "t": 144, "s": [285.821, 247.242, 0], "to": [-0.009, 0.021, 0], "ti": [0.01, -0.025, 0]}, {"i": {"x": 0.833, "y": 0.819}, "o": {"x": 0.167, "y": 0.154}, "t": 145, "s": [285.793, 247.312, 0], "to": [-0.01, 0.025, 0], "ti": [0.012, -0.03, 0]}, {"i": {"x": 0.833, "y": 0.819}, "o": {"x": 0.167, "y": 0.154}, "t": 146, "s": [285.76, 247.394, 0], "to": [-0.012, 0.03, 0], "ti": [0.014, -0.035, 0]}, {"i": {"x": 0.833, "y": 0.819}, "o": {"x": 0.167, "y": 0.155}, "t": 147, "s": [285.722, 247.49, 0], "to": [-0.014, 0.035, 0], "ti": [0.016, -0.041, 0]}, {"i": {"x": 0.833, "y": 0.819}, "o": {"x": 0.167, "y": 0.155}, "t": 148, "s": [285.677, 247.603, 0], "to": [-0.016, 0.041, 0], "ti": [0.019, -0.048, 0]}, {"i": {"x": 0.833, "y": 0.818}, "o": {"x": 0.167, "y": 0.154}, "t": 149, "s": [285.624, 247.734, 0], "to": [-0.019, 0.048, 0], "ti": [0.023, -0.056, 0]}, {"i": {"x": 0.833, "y": 0.816}, "o": {"x": 0.167, "y": 0.153}, "t": 150, "s": [285.563, 247.889, 0], "to": [-0.023, 0.056, 0], "ti": [0.027, -0.068, 0]}, {"i": {"x": 0.833, "y": 0.814}, "o": {"x": 0.167, "y": 0.152}, "t": 151, "s": [285.489, 248.072, 0], "to": [-0.027, 0.068, 0], "ti": [0.033, -0.083, 0]}, {"i": {"x": 0.833, "y": 0.812}, "o": {"x": 0.167, "y": 0.151}, "t": 152, "s": [285.4, 248.295, 0], "to": [-0.033, 0.083, 0], "ti": [0.041, -0.104, 0]}, {"i": {"x": 0.833, "y": 0.809}, "o": {"x": 0.167, "y": 0.15}, "t": 153, "s": [285.29, 248.57, 0], "to": [-0.041, 0.104, 0], "ti": [0.053, -0.132, 0]}, {"i": {"x": 0.833, "y": 0.806}, "o": {"x": 0.167, "y": 0.148}, "t": 154, "s": [285.152, 248.916, 0], "to": [-0.053, 0.132, 0], "ti": [0.07, -0.174, 0]}, {"i": {"x": 0.833, "y": 0.814}, "o": {"x": 0.167, "y": 0.146}, "t": 155, "s": [284.972, 249.364, 0], "to": [-0.07, 0.174, 0], "ti": [0.089, -0.223, 0]}, {"i": {"x": 0.833, "y": 0.846}, "o": {"x": 0.167, "y": 0.151}, "t": 156, "s": [284.733, 249.963, 0], "to": [-0.089, 0.223, 0], "ti": [0.091, -0.227, 0]}, {"i": {"x": 0.833, "y": 0.867}, "o": {"x": 0.167, "y": 0.182}, "t": 157, "s": [284.437, 250.702, 0], "to": [-0.091, 0.227, 0], "ti": [0.066, -0.166, 0]}, {"i": {"x": 0.833, "y": 0.873}, "o": {"x": 0.167, "y": 0.224}, "t": 158, "s": [284.187, 251.327, 0], "to": [-0.066, 0.166, 0], "ti": [0.037, -0.094, 0]}, {"i": {"x": 0.833, "y": 0.879}, "o": {"x": 0.167, "y": 0.244}, "t": 159, "s": [284.039, 251.697, 0], "to": [-0.037, 0.094, 0], "ti": [0.019, -0.046, 0]}, {"i": {"x": 0.833, "y": 0.894}, "o": {"x": 0.167, "y": 0.268}, "t": 160, "s": [283.963, 251.889, 0], "to": [-0.019, 0.046, 0], "ti": [0.007, -0.018, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.395}, "t": 161, "s": [283.928, 251.975, 0], "to": [-0.007, 0.018, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.605}, "o": {"x": 0.167, "y": 0.167}, "t": 162, "s": [283.919, 251.998, 0], "to": [0, 0, 0], "ti": [-0.007, 0.018, 0]}, {"i": {"x": 0.833, "y": 0.732}, "o": {"x": 0.167, "y": 0.106}, "t": 163, "s": [283.928, 251.975, 0], "to": [0.007, -0.018, 0], "ti": [-0.019, 0.046, 0]}, {"i": {"x": 0.833, "y": 0.756}, "o": {"x": 0.167, "y": 0.121}, "t": 164, "s": [283.963, 251.889, 0], "to": [0.019, -0.046, 0], "ti": [-0.037, 0.094, 0]}, {"i": {"x": 0.833, "y": 0.776}, "o": {"x": 0.167, "y": 0.127}, "t": 165, "s": [284.039, 251.697, 0], "to": [0.037, -0.094, 0], "ti": [-0.066, 0.166, 0]}, {"i": {"x": 0.833, "y": 0.818}, "o": {"x": 0.167, "y": 0.133}, "t": 166, "s": [284.187, 251.327, 0], "to": [0.066, -0.166, 0], "ti": [-0.091, 0.227, 0]}, {"i": {"x": 0.833, "y": 0.849}, "o": {"x": 0.167, "y": 0.154}, "t": 167, "s": [284.437, 250.702, 0], "to": [0.091, -0.227, 0], "ti": [-0.089, 0.223, 0]}, {"i": {"x": 0.833, "y": 0.854}, "o": {"x": 0.167, "y": 0.186}, "t": 168, "s": [284.733, 249.963, 0], "to": [0.089, -0.223, 0], "ti": [-0.07, 0.174, 0]}, {"i": {"x": 0.833, "y": 0.852}, "o": {"x": 0.167, "y": 0.194}, "t": 169, "s": [284.972, 249.364, 0], "to": [0.07, -0.174, 0], "ti": [-0.053, 0.132, 0]}, {"i": {"x": 0.833, "y": 0.85}, "o": {"x": 0.167, "y": 0.191}, "t": 170, "s": [285.152, 248.916, 0], "to": [0.053, -0.132, 0], "ti": [-0.041, 0.104, 0]}, {"i": {"x": 0.833, "y": 0.849}, "o": {"x": 0.167, "y": 0.188}, "t": 171, "s": [285.29, 248.57, 0], "to": [0.041, -0.104, 0], "ti": [-0.033, 0.083, 0]}, {"i": {"x": 0.833, "y": 0.848}, "o": {"x": 0.167, "y": 0.186}, "t": 172, "s": [285.4, 248.295, 0], "to": [0.033, -0.083, 0], "ti": [-0.027, 0.068, 0]}, {"i": {"x": 0.833, "y": 0.847}, "o": {"x": 0.167, "y": 0.184}, "t": 173, "s": [285.489, 248.072, 0], "to": [0.027, -0.068, 0], "ti": [-0.023, 0.056, 0]}, {"i": {"x": 0.833, "y": 0.846}, "o": {"x": 0.167, "y": 0.182}, "t": 174, "s": [285.563, 247.889, 0], "to": [0.023, -0.056, 0], "ti": [-0.019, 0.048, 0]}, {"i": {"x": 0.833, "y": 0.845}, "o": {"x": 0.167, "y": 0.181}, "t": 175, "s": [285.624, 247.734, 0], "to": [0.019, -0.048, 0], "ti": [-0.016, 0.041, 0]}, {"i": {"x": 0.833, "y": 0.845}, "o": {"x": 0.167, "y": 0.181}, "t": 176, "s": [285.677, 247.603, 0], "to": [0.016, -0.041, 0], "ti": [-0.014, 0.035, 0]}, {"i": {"x": 0.833, "y": 0.846}, "o": {"x": 0.167, "y": 0.181}, "t": 177, "s": [285.722, 247.49, 0], "to": [0.014, -0.035, 0], "ti": [-0.012, 0.03, 0]}, {"i": {"x": 0.833, "y": 0.846}, "o": {"x": 0.167, "y": 0.181}, "t": 178, "s": [285.76, 247.394, 0], "to": [0.012, -0.03, 0], "ti": [-0.01, 0.025, 0]}, {"i": {"x": 0.833, "y": 0.847}, "o": {"x": 0.167, "y": 0.182}, "t": 179, "s": [285.793, 247.312, 0], "to": [0.01, -0.025, 0], "ti": [-0.009, 0.021, 0]}, {"i": {"x": 0.833, "y": 0.847}, "o": {"x": 0.167, "y": 0.182}, "t": 180, "s": [285.821, 247.242, 0], "to": [0.009, -0.021, 0], "ti": [-0.007, 0.018, 0]}, {"i": {"x": 0.833, "y": 0.849}, "o": {"x": 0.167, "y": 0.184}, "t": 181, "s": [285.845, 247.184, 0], "to": [0.007, -0.018, 0], "ti": [-0.006, 0.015, 0]}, {"i": {"x": 0.833, "y": 0.85}, "o": {"x": 0.167, "y": 0.185}, "t": 182, "s": [285.864, 247.135, 0], "to": [0.006, -0.015, 0], "ti": [-0.005, 0.012, 0]}, {"i": {"x": 0.833, "y": 0.852}, "o": {"x": 0.167, "y": 0.187}, "t": 183, "s": [285.88, 247.095, 0], "to": [0.005, -0.012, 0], "ti": [-0.004, 0.009, 0]}, {"i": {"x": 0.833, "y": 0.855}, "o": {"x": 0.167, "y": 0.191}, "t": 184, "s": [285.893, 247.063, 0], "to": [0.004, -0.009, 0], "ti": [-0.003, 0.007, 0]}, {"i": {"x": 0.833, "y": 0.86}, "o": {"x": 0.167, "y": 0.196}, "t": 185, "s": [285.903, 247.039, 0], "to": [0.003, -0.007, 0], "ti": [-0.002, 0.005, 0]}, {"i": {"x": 0.833, "y": 0.869}, "o": {"x": 0.167, "y": 0.206}, "t": 186, "s": [285.91, 247.02, 0], "to": [0.002, -0.005, 0], "ti": [-0.001, 0.003, 0]}, {"i": {"x": 0.833, "y": 0.89}, "o": {"x": 0.167, "y": 0.228}, "t": 187, "s": [285.915, 247.008, 0], "to": [0.001, -0.003, 0], "ti": [-0.001, 0.002, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.341}, "t": 188, "s": [285.918, 247.001, 0], "to": [0.001, -0.002, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.659}, "o": {"x": 0.167, "y": 0.166}, "t": 189, "s": [285.919, 246.998, 0], "to": [0, 0, 0], "ti": [0.001, -0.002, 0]}, {"i": {"x": 0.833, "y": 0.772}, "o": {"x": 0.167, "y": 0.11}, "t": 190, "s": [285.918, 247.001, 0], "to": [-0.001, 0.002, 0], "ti": [0.001, -0.003, 0]}, {"i": {"x": 0.833, "y": 0.794}, "o": {"x": 0.167, "y": 0.131}, "t": 191, "s": [285.915, 247.008, 0], "to": [-0.001, 0.003, 0], "ti": [0.002, -0.005, 0]}, {"i": {"x": 0.833, "y": 0.804}, "o": {"x": 0.167, "y": 0.14}, "t": 192, "s": [285.91, 247.02, 0], "to": [-0.002, 0.005, 0], "ti": [0.003, -0.007, 0]}, {"i": {"x": 0.833, "y": 0.809}, "o": {"x": 0.167, "y": 0.145}, "t": 193, "s": [285.903, 247.039, 0], "to": [-0.003, 0.007, 0], "ti": [0.004, -0.009, 0]}, {"i": {"x": 0.833, "y": 0.813}, "o": {"x": 0.167, "y": 0.148}, "t": 194, "s": [285.893, 247.063, 0], "to": [-0.004, 0.009, 0], "ti": [0.005, -0.012, 0]}, {"i": {"x": 0.833, "y": 0.815}, "o": {"x": 0.167, "y": 0.15}, "t": 195, "s": [285.88, 247.095, 0], "to": [-0.005, 0.012, 0], "ti": [0.006, -0.015, 0]}, {"i": {"x": 0.833, "y": 0.816}, "o": {"x": 0.167, "y": 0.151}, "t": 196, "s": [285.864, 247.135, 0], "to": [-0.006, 0.015, 0], "ti": [0.007, -0.018, 0]}, {"i": {"x": 0.833, "y": 0.818}, "o": {"x": 0.167, "y": 0.153}, "t": 197, "s": [285.845, 247.184, 0], "to": [-0.007, 0.018, 0], "ti": [0.009, -0.021, 0]}, {"i": {"x": 0.833, "y": 0.818}, "o": {"x": 0.167, "y": 0.153}, "t": 198, "s": [285.821, 247.242, 0], "to": [-0.009, 0.021, 0], "ti": [0.01, -0.025, 0]}, {"i": {"x": 0.833, "y": 0.819}, "o": {"x": 0.167, "y": 0.154}, "t": 199, "s": [285.793, 247.312, 0], "to": [-0.01, 0.025, 0], "ti": [0.012, -0.03, 0]}, {"i": {"x": 0.833, "y": 0.819}, "o": {"x": 0.167, "y": 0.154}, "t": 200, "s": [285.76, 247.394, 0], "to": [-0.012, 0.03, 0], "ti": [0.014, -0.035, 0]}, {"i": {"x": 0.833, "y": 0.819}, "o": {"x": 0.167, "y": 0.155}, "t": 201, "s": [285.722, 247.49, 0], "to": [-0.014, 0.035, 0], "ti": [0.016, -0.041, 0]}, {"i": {"x": 0.833, "y": 0.819}, "o": {"x": 0.167, "y": 0.155}, "t": 202, "s": [285.677, 247.603, 0], "to": [-0.016, 0.041, 0], "ti": [0.019, -0.048, 0]}, {"i": {"x": 0.833, "y": 0.818}, "o": {"x": 0.167, "y": 0.154}, "t": 203, "s": [285.624, 247.734, 0], "to": [-0.019, 0.048, 0], "ti": [0.023, -0.056, 0]}, {"i": {"x": 0.833, "y": 0.816}, "o": {"x": 0.167, "y": 0.153}, "t": 204, "s": [285.563, 247.889, 0], "to": [-0.023, 0.056, 0], "ti": [0.027, -0.068, 0]}, {"i": {"x": 0.833, "y": 0.814}, "o": {"x": 0.167, "y": 0.152}, "t": 205, "s": [285.489, 248.072, 0], "to": [-0.027, 0.068, 0], "ti": [0.033, -0.083, 0]}, {"i": {"x": 0.833, "y": 0.812}, "o": {"x": 0.167, "y": 0.151}, "t": 206, "s": [285.4, 248.295, 0], "to": [-0.033, 0.083, 0], "ti": [0.041, -0.104, 0]}, {"i": {"x": 0.833, "y": 0.809}, "o": {"x": 0.167, "y": 0.15}, "t": 207, "s": [285.29, 248.57, 0], "to": [-0.041, 0.104, 0], "ti": [0.053, -0.132, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.148}, "t": 208, "s": [285.152, 248.916, 0], "to": [-0.053, 0.132, 0], "ti": [0.03, -0.075, 0]}, {"t": 209.000008512745, "s": [284.972, 249.364, 0]}], "ix": 2}, "a": {"a": 0, "k": [31.556, 20.472, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ef": [{"ty": 34, "nm": "<PERSON><PERSON><PERSON>", "np": 6, "mn": "ADBE FreePin3", "ix": 1, "en": 1, "ef": [{"ty": 7, "nm": "Puppet Engine", "mn": "ADBE FreePin3 Puppet Engine", "ix": 1, "v": {"a": 0, "k": 2, "ix": 1}}, {"ty": 0, "nm": "Mesh Rotation Refinement", "mn": "ADBE FreePin3 Auto Rotate Pins", "ix": 2, "v": {"a": 0, "k": 20, "ix": 2}}, {"ty": 7, "nm": "On Transparent", "mn": "ADBE FreePin3 On Transparent", "ix": 3, "v": {"a": 0, "k": 0, "ix": 3}}, {"ty": 29.9700012207031, "nm": "arap", "np": 3, "mn": "ADBE FreePin3 ARAP Group", "ix": 4, "en": 1, "ef": [{"ty": 6, "nm": "Auto-traced Shapes", "mn": "ADBE FreePin3 Outlines", "ix": 1, "v": 0}, {"ty": 1, "nm": "<PERSON><PERSON>", "np": 1, "mn": "ADBE FreePin3 Mesh Group", "ix": 2, "en": 1, "ef": []}]}]}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-1.966, -1.7], [0, 0], [-3.803, 1.757]], "o": [[-2.132, 1.112], [0, 0], [-3.358, -2.849], [0, 0]], "v": [[3.377, -1.974], [3.022, 2.909], [-0.019, 4.06], [0.842, -4.06]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.243000000598, 0.670999983245, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [18.127, 11.809], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0.359, -0.137], [3.706, 2.843], [0, 0], [-2.623, 0.991], [-0.279, 0.18]], "o": [[-0.309, 0.162], [-4.302, 1.626], [0, 0], [2.31, 1.697], [0.364, -0.138], [0, 0]], "v": [[7.668, 0.419], [6.666, 0.868], [-7.668, -1.343], [-4.624, -2.494], [4.145, -1.207], [5.108, -1.687]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.243000000598, 0.670999983245, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [7.918, 11.94], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [2.998, -1.825], [0, 0], [1.568, 1.453]], "o": [[2.951, 2.611], [0, 0], [1.374, -1.13], [0, 0]], "v": [[0.047, -3.783], [-0.171, 3.783], [-2.772, 1.643], [-2.998, -2.632]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.885999971278, 0.929000016755, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [16.165, 8.344], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 3, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-3.681, -2.939], [0, 0], [2.686, -1.015], [0.054, -0.023], [0, 0], [-0.056, 0.021]], "o": [[0, 0], [-2.288, -1.793], [-0.054, 0.021], [0, 0], [0.054, -0.022], [4.364, -1.651]], "v": [[7.33, 1.419], [4.288, 2.569], [-4.645, 1.157], [-4.807, 1.223], [-7.33, -0.854], [-7.166, -0.918]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.885999971278, 0.929000016755, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [8.497, 2.819], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 3, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [28.293, 13.854], "ix": 2}, "a": {"a": 0, "k": [9.293, 7.354], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group2", "np": 3, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-31.306, -12.201], [0.457, 20.222], [31.306, 12.201], [-0.457, -20.222]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [31.556, 20.472], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "ss", "np": 2, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 94.0000038286985, "st": 0, "bm": 0}, {"ddd": 1, "ind": 6, "ty": 4, "nm": "Layer 5 Outlines", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "rx": {"a": 0, "k": 0, "ix": 8}, "ry": {"a": 0, "k": 0, "ix": 9}, "rz": {"a": 0, "k": 0, "ix": 10}, "or": {"a": 0, "k": [0, 0, 0], "ix": 7}, "p": {"a": 0, "k": [354.342, 359.439, 0], "ix": 2}, "a": {"a": 0, "k": [55.214, 71.708, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.144, -0.289], [3.324, -11.695], [3.026, 1.786], [0, 0], [-0.75, -4.493], [0, 0], [-0.19, -0.419], [-0.27, -0.411], [-0.548, -0.315], [0, 0], [-5.184, -0.766], [6.492, 4.87], [0.748, 0.242]], "o": [[-2.09, 4.179], [-0.961, 3.379], [0, 0], [-3.924, -2.314], [0, 0], [0.077, 0.464], [0.263, 0.414], [0.397, 0.472], [0, 0], [4.676, 2.686], [17.903, -13.892], [-0.962, -0.72], [-0.405, 0.353]], "v": [[18.647, -38.114], [6.482, 1.104], [-1.4, 4.244], [-22.026, -7.92], [-30.042, -2.419], [-26.729, 17.452], [-26.31, 18.773], [-25.513, 20.011], [-24.111, 21.223], [-2.04, 33.901], [12.888, 39.079], [22.043, -37.667], [19.486, -39.079]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.226999993418, 0.681999954523, 0.936999990426, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [31.041, 61.537], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [3.41, -1.196], [2.192, -4.385], [3.324, -11.694], [3.027, 1.785], [0, 0], [-0.748, -4.493], [0, 0], [-1.393, -0.8], [0, 0], [-12.724, 6.363], [0, 0], [0.607, 16.625]], "o": [[-0.132, -3.611], [-14.89, 5.222], [-2.09, 4.179], [-0.961, 3.379], [0, 0], [-3.924, -2.315], [0, 0], [0.264, 1.586], [0, 0], [12.334, 7.086], [0, 0], [14.881, -7.44], [0, 0]], "v": [[52.809, -45.291], [45.684, -50.159], [-5.527, -28.182], [-17.692, 11.035], [-25.573, 14.177], [-46.2, 2.013], [-54.215, 7.514], [-50.903, 27.385], [-48.284, 31.154], [-26.213, 43.833], [13.909, 44.992], [30.922, 36.485], [54.356, -3.027]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.195999998205, 0.470999983245, 0.8, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [55.215, 51.605], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 3, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[4.572, 0], [0.015, 2.648], [-4.572, 0], [-0.015, -2.648]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.301999978458, 0.647000002394, 0.862999949736, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [50.328, 95.25], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 2, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[2.331, -11.7], [2.257, 14.348], [-2.331, 11.7], [-2.257, -14.348]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.481999984442, 0.681999954523, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [48.013, 109.598], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 4", "np": 2, "cix": 2, "bm": 0, "ix": 4, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[2.316, -14.348], [2.242, 11.701], [-2.316, 14.348], [-2.242, -11.7]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.779999976065, 0.847000002394, 0.948999980852, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [52.584, 109.597], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 5", "np": 2, "cix": 2, "bm": 0, "ix": 5, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-11.675, -6.74], [11.599, -6.74], [11.674, 6.74], [-11.599, 6.74]], "o": [[11.674, 6.74], [-11.597, 6.74], [-11.675, -6.74], [11.598, -6.74]], "v": [[20.989, -12.205], [21.139, 12.205], [-21.015, 12.205], [-21.139, -12.205]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.885999971278, 0.929000016755, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [49.597, 121.079], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 6", "np": 3, "cix": 2, "bm": 0, "ix": 6, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.037, -0.289], [5.334, -2.299], [6.303, 0.081], [5.799, 2.854], [-0.017, 6.144], [-3.224, -3.493], [-5.879, -1.214], [-8.712, 8.83], [-0.012, 4.725]], "o": [[-0.565, 6.281], [-5.808, 2.503], [-6.431, -0.083], [-5.062, -2.49], [-0.012, 4.474], [4.15, 4.496], [11.468, 2.368], [3.506, -3.551], [-0.011, 0.291]], "v": [[29.733, -10.041], [17.922, 2.805], [-0.575, 6.236], [-19.373, 2.069], [-29.789, -11.082], [-26.264, 0.582], [-9.986, 8.714], [25.809, 1.108], [29.806, -10.912]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.677999997606, 0.804000016755, 0.987999949736, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [49.582, 132.083], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 7", "np": 3, "cix": 2, "bm": 0, "ix": 7, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 94.0000038286985, "st": 0, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "Layer 7 Outlines", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [350.136, 299.821, 0], "ix": 2}, "a": {"a": 0, "k": [28.892, 46.898, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-10.897, 1.557], [0.934, 0.623], [0, 0], [4.048, -1.557], [0, 0]], "o": [[0, 0], [10.896, -1.557], [-0.934, -0.622], [0, 0], [-4.047, 1.556], [0, 0]], "v": [[-13.387, 4.514], [1.868, 5.76], [12.453, -4.203], [9.651, -7.316], [-5.915, -0.466], [-12.141, 2.023]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [23.495, 15.56], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[3.32, 2.49], [10.377, -4.567], [4.567, -7.472], [0.514, -1.943], [-6.056, -14.534], [12.564, 12.534], [-0.511, -1.915], [-17.019, 2.905], [-1.246, 3.736], [-4.152, 14.529]], "o": [[-3.321, -2.491], [0, 0], [-1.106, 1.812], [6.555, -8.52], [5.651, 13.563], [-0.528, 2.614], [1.66, 6.227], [17.018, -2.905], [1.245, -3.736], [4.151, -14.528]], "v": [[16.029, -36.736], [-4.311, -37.98], [-25.066, -25.113], [-27.47, -19.499], [-5.556, -17.225], [-26.847, 23.672], [-27.141, 30.51], [3.161, 39.643], [22.671, 26.359], [23.501, -9.755]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.885999971278, 0.626999978458, 0.161000001197, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [29.882, 50.998], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 3, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[10.377, -4.566], [4.567, -7.472], [-3.321, -11.623], [-1.661, -6.226], [-17.019, 2.905], [-1.245, 3.736], [-4.152, 14.528], [3.32, 2.49]], "o": [[0, 0], [-4.566, 7.472], [3.32, 11.622], [1.66, 6.227], [17.018, -2.905], [1.246, -3.736], [4.151, -14.529], [-3.321, -2.491]], "v": [[-3.321, -37.981], [-24.076, -25.113], [-24.491, 2.284], [-26.151, 30.51], [4.151, 39.642], [23.66, 26.359], [24.491, -9.754], [17.019, -36.736]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.745000023935, 0.238999998803, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [28.892, 50.999], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 3, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-3.113, 0.311], [-1.247, -5.293], [2.803, -1.557], [4.359, 0], [-2.802, 4.67]], "o": [[0, 0], [3.736, -0.312], [1.245, 5.292], [-2.802, 1.556], [-4.358, 0], [2.802, -4.669]], "v": [[-7.005, -2.024], [11.363, -15.099], [21.015, -8.873], [16.655, 2.024], [-15.1, 15.411], [-19.458, 7.004]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.885999971278, 0.626999978458, 0.161000001197, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [27.906, 23.55], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 4", "np": 3, "cix": 2, "bm": 0, "ix": 4, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[19.095, 6.227], [0.271, 5.511], [0, 0], [-6.96, -1.681], [-4.863, -3.737]], "o": [[0, 0], [-1.252, -5.426], [0, 0], [0, 0], [0, 0]], "v": [[-11.491, 5.968], [-7.637, -1.071], [-10.403, -6.862], [2.328, -10.514], [11.491, 1.946]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.808000033509, 0.486000001197, 0.337000020345, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [23.364, 12.445], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 5", "np": 3, "cix": 2, "bm": 0, "ix": 5, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 94.0000038286985, "st": 0, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 4, "nm": "Layer 8 Outlines", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.665]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [0]}, {"i": {"x": [0.833], "y": [0.777]}, "o": {"x": [0.167], "y": [0.111]}, "t": 1, "s": [-0.029]}, {"i": {"x": [0.833], "y": [0.8]}, "o": {"x": [0.167], "y": [0.133]}, "t": 2, "s": [-0.117]}, {"i": {"x": [0.833], "y": [0.811]}, "o": {"x": [0.167], "y": [0.143]}, "t": 3, "s": [-0.264]}, {"i": {"x": [0.833], "y": [0.817]}, "o": {"x": [0.167], "y": [0.149]}, "t": 4, "s": [-0.469]}, {"i": {"x": [0.833], "y": [0.821]}, "o": {"x": [0.167], "y": [0.153]}, "t": 5, "s": [-0.73]}, {"i": {"x": [0.833], "y": [0.825]}, "o": {"x": [0.167], "y": [0.156]}, "t": 6, "s": [-1.042]}, {"i": {"x": [0.833], "y": [0.827]}, "o": {"x": [0.167], "y": [0.159]}, "t": 7, "s": [-1.398]}, {"i": {"x": [0.833], "y": [0.83]}, "o": {"x": [0.167], "y": [0.161]}, "t": 8, "s": [-1.791]}, {"i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.163]}, "t": 9, "s": [-2.212]}, {"i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.165]}, "t": 10, "s": [-2.65]}, {"i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.167]}, "t": 11, "s": [-3.096]}, {"i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.169]}, "t": 12, "s": [-3.54]}, {"i": {"x": [0.833], "y": [0.838]}, "o": {"x": [0.167], "y": [0.17]}, "t": 13, "s": [-3.973]}, {"i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.172]}, "t": 14, "s": [-4.389]}, {"i": {"x": [0.833], "y": [0.84]}, "o": {"x": [0.167], "y": [0.173]}, "t": 15, "s": [-4.781]}, {"i": {"x": [0.833], "y": [0.842]}, "o": {"x": [0.167], "y": [0.174]}, "t": 16, "s": [-5.145]}, {"i": {"x": [0.833], "y": [0.843]}, "o": {"x": [0.167], "y": [0.176]}, "t": 17, "s": [-5.478]}, {"i": {"x": [0.833], "y": [0.844]}, "o": {"x": [0.167], "y": [0.177]}, "t": 18, "s": [-5.779]}, {"i": {"x": [0.833], "y": [0.846]}, "o": {"x": [0.167], "y": [0.179]}, "t": 19, "s": [-6.046]}, {"i": {"x": [0.833], "y": [0.847]}, "o": {"x": [0.167], "y": [0.181]}, "t": 20, "s": [-6.278]}, {"i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.184]}, "t": 21, "s": [-6.476]}, {"i": {"x": [0.833], "y": [0.853]}, "o": {"x": [0.167], "y": [0.187]}, "t": 22, "s": [-6.641]}, {"i": {"x": [0.833], "y": [0.858]}, "o": {"x": [0.167], "y": [0.193]}, "t": 23, "s": [-6.774]}, {"i": {"x": [0.833], "y": [0.868]}, "o": {"x": [0.167], "y": [0.203]}, "t": 24, "s": [-6.875]}, {"i": {"x": [0.833], "y": [0.889]}, "o": {"x": [0.167], "y": [0.225]}, "t": 25, "s": [-6.945]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0.338]}, "t": 26, "s": [-6.986]}, {"i": {"x": [0.833], "y": [0.662]}, "o": {"x": [0.167], "y": [0]}, "t": 27, "s": [-7]}, {"i": {"x": [0.833], "y": [0.775]}, "o": {"x": [0.167], "y": [0.111]}, "t": 28, "s": [-6.986]}, {"i": {"x": [0.833], "y": [0.797]}, "o": {"x": [0.167], "y": [0.132]}, "t": 29, "s": [-6.945]}, {"i": {"x": [0.833], "y": [0.807]}, "o": {"x": [0.167], "y": [0.142]}, "t": 30, "s": [-6.875]}, {"i": {"x": [0.833], "y": [0.813]}, "o": {"x": [0.167], "y": [0.147]}, "t": 31, "s": [-6.774]}, {"i": {"x": [0.833], "y": [0.816]}, "o": {"x": [0.167], "y": [0.15]}, "t": 32, "s": [-6.641]}, {"i": {"x": [0.833], "y": [0.819]}, "o": {"x": [0.167], "y": [0.153]}, "t": 33, "s": [-6.476]}, {"i": {"x": [0.833], "y": [0.821]}, "o": {"x": [0.167], "y": [0.154]}, "t": 34, "s": [-6.278]}, {"i": {"x": [0.833], "y": [0.823]}, "o": {"x": [0.167], "y": [0.156]}, "t": 35, "s": [-6.046]}, {"i": {"x": [0.833], "y": [0.824]}, "o": {"x": [0.167], "y": [0.157]}, "t": 36, "s": [-5.779]}, {"i": {"x": [0.833], "y": [0.826]}, "o": {"x": [0.167], "y": [0.158]}, "t": 37, "s": [-5.478]}, {"i": {"x": [0.833], "y": [0.827]}, "o": {"x": [0.167], "y": [0.16]}, "t": 38, "s": [-5.145]}, {"i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.161]}, "t": 39, "s": [-4.781]}, {"i": {"x": [0.833], "y": [0.83]}, "o": {"x": [0.167], "y": [0.162]}, "t": 40, "s": [-4.389]}, {"i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.163]}, "t": 41, "s": [-3.973]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.165]}, "t": 42, "s": [-3.54]}, {"i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.166]}, "t": 43, "s": [-3.096]}, {"i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.168]}, "t": 44, "s": [-2.65]}, {"i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.17]}, "t": 45, "s": [-2.212]}, {"i": {"x": [0.833], "y": [0.841]}, "o": {"x": [0.167], "y": [0.173]}, "t": 46, "s": [-1.791]}, {"i": {"x": [0.833], "y": [0.844]}, "o": {"x": [0.167], "y": [0.175]}, "t": 47, "s": [-1.398]}, {"i": {"x": [0.833], "y": [0.847]}, "o": {"x": [0.167], "y": [0.179]}, "t": 48, "s": [-1.042]}, {"i": {"x": [0.833], "y": [0.851]}, "o": {"x": [0.167], "y": [0.183]}, "t": 49, "s": [-0.73]}, {"i": {"x": [0.833], "y": [0.857]}, "o": {"x": [0.167], "y": [0.189]}, "t": 50, "s": [-0.469]}, {"i": {"x": [0.833], "y": [0.867]}, "o": {"x": [0.167], "y": [0.2]}, "t": 51, "s": [-0.264]}, {"i": {"x": [0.833], "y": [0.889]}, "o": {"x": [0.167], "y": [0.223]}, "t": 52, "s": [-0.117]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0.335]}, "t": 53, "s": [-0.029]}, {"i": {"x": [0.833], "y": [0.665]}, "o": {"x": [0.167], "y": [0]}, "t": 54, "s": [0]}, {"i": {"x": [0.833], "y": [0.777]}, "o": {"x": [0.167], "y": [0.111]}, "t": 55, "s": [-0.029]}, {"i": {"x": [0.833], "y": [0.8]}, "o": {"x": [0.167], "y": [0.133]}, "t": 56, "s": [-0.117]}, {"i": {"x": [0.833], "y": [0.811]}, "o": {"x": [0.167], "y": [0.143]}, "t": 57, "s": [-0.264]}, {"i": {"x": [0.833], "y": [0.817]}, "o": {"x": [0.167], "y": [0.149]}, "t": 58, "s": [-0.469]}, {"i": {"x": [0.833], "y": [0.821]}, "o": {"x": [0.167], "y": [0.153]}, "t": 59, "s": [-0.73]}, {"i": {"x": [0.833], "y": [0.825]}, "o": {"x": [0.167], "y": [0.156]}, "t": 60, "s": [-1.042]}, {"i": {"x": [0.833], "y": [0.827]}, "o": {"x": [0.167], "y": [0.159]}, "t": 61, "s": [-1.398]}, {"i": {"x": [0.833], "y": [0.83]}, "o": {"x": [0.167], "y": [0.161]}, "t": 62, "s": [-1.791]}, {"i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.163]}, "t": 63, "s": [-2.212]}, {"i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.165]}, "t": 64, "s": [-2.65]}, {"i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.167]}, "t": 65, "s": [-3.096]}, {"i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.169]}, "t": 66, "s": [-3.54]}, {"i": {"x": [0.833], "y": [0.838]}, "o": {"x": [0.167], "y": [0.17]}, "t": 67, "s": [-3.973]}, {"i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.172]}, "t": 68, "s": [-4.389]}, {"i": {"x": [0.833], "y": [0.84]}, "o": {"x": [0.167], "y": [0.173]}, "t": 69, "s": [-4.781]}, {"i": {"x": [0.833], "y": [0.842]}, "o": {"x": [0.167], "y": [0.174]}, "t": 70, "s": [-5.145]}, {"i": {"x": [0.833], "y": [0.843]}, "o": {"x": [0.167], "y": [0.176]}, "t": 71, "s": [-5.478]}, {"i": {"x": [0.833], "y": [0.844]}, "o": {"x": [0.167], "y": [0.177]}, "t": 72, "s": [-5.779]}, {"i": {"x": [0.833], "y": [0.846]}, "o": {"x": [0.167], "y": [0.179]}, "t": 73, "s": [-6.046]}, {"i": {"x": [0.833], "y": [0.847]}, "o": {"x": [0.167], "y": [0.181]}, "t": 74, "s": [-6.278]}, {"i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.184]}, "t": 75, "s": [-6.476]}, {"i": {"x": [0.833], "y": [0.853]}, "o": {"x": [0.167], "y": [0.187]}, "t": 76, "s": [-6.641]}, {"i": {"x": [0.833], "y": [0.858]}, "o": {"x": [0.167], "y": [0.193]}, "t": 77, "s": [-6.774]}, {"i": {"x": [0.833], "y": [0.868]}, "o": {"x": [0.167], "y": [0.203]}, "t": 78, "s": [-6.875]}, {"i": {"x": [0.833], "y": [0.889]}, "o": {"x": [0.167], "y": [0.225]}, "t": 79, "s": [-6.945]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0.338]}, "t": 80, "s": [-6.986]}, {"i": {"x": [0.833], "y": [0.662]}, "o": {"x": [0.167], "y": [0]}, "t": 81, "s": [-7]}, {"i": {"x": [0.833], "y": [0.775]}, "o": {"x": [0.167], "y": [0.111]}, "t": 82, "s": [-6.986]}, {"i": {"x": [0.833], "y": [0.797]}, "o": {"x": [0.167], "y": [0.132]}, "t": 83, "s": [-6.945]}, {"i": {"x": [0.833], "y": [0.807]}, "o": {"x": [0.167], "y": [0.142]}, "t": 84, "s": [-6.875]}, {"i": {"x": [0.833], "y": [0.813]}, "o": {"x": [0.167], "y": [0.147]}, "t": 85, "s": [-6.774]}, {"i": {"x": [0.833], "y": [0.816]}, "o": {"x": [0.167], "y": [0.15]}, "t": 86, "s": [-6.641]}, {"i": {"x": [0.833], "y": [0.819]}, "o": {"x": [0.167], "y": [0.153]}, "t": 87, "s": [-6.476]}, {"i": {"x": [0.833], "y": [0.821]}, "o": {"x": [0.167], "y": [0.154]}, "t": 88, "s": [-6.278]}, {"i": {"x": [0.833], "y": [0.823]}, "o": {"x": [0.167], "y": [0.156]}, "t": 89, "s": [-6.046]}, {"i": {"x": [0.833], "y": [0.824]}, "o": {"x": [0.167], "y": [0.157]}, "t": 90, "s": [-5.779]}, {"i": {"x": [0.833], "y": [0.826]}, "o": {"x": [0.167], "y": [0.158]}, "t": 91, "s": [-5.478]}, {"i": {"x": [0.833], "y": [0.827]}, "o": {"x": [0.167], "y": [0.16]}, "t": 92, "s": [-5.145]}, {"i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.161]}, "t": 93, "s": [-4.781]}, {"i": {"x": [0.833], "y": [0.83]}, "o": {"x": [0.167], "y": [0.162]}, "t": 94, "s": [-4.389]}, {"i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.163]}, "t": 95, "s": [-3.973]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.165]}, "t": 96, "s": [-3.54]}, {"i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.166]}, "t": 97, "s": [-3.096]}, {"i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.168]}, "t": 98, "s": [-2.65]}, {"i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.17]}, "t": 99, "s": [-2.212]}, {"i": {"x": [0.833], "y": [0.841]}, "o": {"x": [0.167], "y": [0.173]}, "t": 100, "s": [-1.791]}, {"i": {"x": [0.833], "y": [0.844]}, "o": {"x": [0.167], "y": [0.175]}, "t": 101, "s": [-1.398]}, {"i": {"x": [0.833], "y": [0.847]}, "o": {"x": [0.167], "y": [0.179]}, "t": 102, "s": [-1.042]}, {"i": {"x": [0.833], "y": [0.851]}, "o": {"x": [0.167], "y": [0.183]}, "t": 103, "s": [-0.73]}, {"i": {"x": [0.833], "y": [0.857]}, "o": {"x": [0.167], "y": [0.189]}, "t": 104, "s": [-0.469]}, {"i": {"x": [0.833], "y": [0.867]}, "o": {"x": [0.167], "y": [0.2]}, "t": 105, "s": [-0.264]}, {"i": {"x": [0.833], "y": [0.889]}, "o": {"x": [0.167], "y": [0.223]}, "t": 106, "s": [-0.117]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0.335]}, "t": 107, "s": [-0.029]}, {"i": {"x": [0.833], "y": [0.665]}, "o": {"x": [0.167], "y": [0]}, "t": 108, "s": [0]}, {"i": {"x": [0.833], "y": [0.777]}, "o": {"x": [0.167], "y": [0.111]}, "t": 109, "s": [-0.029]}, {"i": {"x": [0.833], "y": [0.8]}, "o": {"x": [0.167], "y": [0.133]}, "t": 110, "s": [-0.117]}, {"i": {"x": [0.833], "y": [0.811]}, "o": {"x": [0.167], "y": [0.143]}, "t": 111, "s": [-0.264]}, {"i": {"x": [0.833], "y": [0.817]}, "o": {"x": [0.167], "y": [0.149]}, "t": 112, "s": [-0.469]}, {"i": {"x": [0.833], "y": [0.821]}, "o": {"x": [0.167], "y": [0.153]}, "t": 113, "s": [-0.73]}, {"i": {"x": [0.833], "y": [0.825]}, "o": {"x": [0.167], "y": [0.156]}, "t": 114, "s": [-1.042]}, {"i": {"x": [0.833], "y": [0.827]}, "o": {"x": [0.167], "y": [0.159]}, "t": 115, "s": [-1.398]}, {"i": {"x": [0.833], "y": [0.83]}, "o": {"x": [0.167], "y": [0.161]}, "t": 116, "s": [-1.791]}, {"i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.163]}, "t": 117, "s": [-2.212]}, {"i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.165]}, "t": 118, "s": [-2.65]}, {"i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.167]}, "t": 119, "s": [-3.096]}, {"i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.169]}, "t": 120, "s": [-3.54]}, {"i": {"x": [0.833], "y": [0.838]}, "o": {"x": [0.167], "y": [0.17]}, "t": 121, "s": [-3.973]}, {"i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.172]}, "t": 122, "s": [-4.389]}, {"i": {"x": [0.833], "y": [0.84]}, "o": {"x": [0.167], "y": [0.173]}, "t": 123, "s": [-4.781]}, {"i": {"x": [0.833], "y": [0.842]}, "o": {"x": [0.167], "y": [0.174]}, "t": 124, "s": [-5.145]}, {"i": {"x": [0.833], "y": [0.843]}, "o": {"x": [0.167], "y": [0.176]}, "t": 125, "s": [-5.478]}, {"i": {"x": [0.833], "y": [0.844]}, "o": {"x": [0.167], "y": [0.177]}, "t": 126, "s": [-5.779]}, {"i": {"x": [0.833], "y": [0.846]}, "o": {"x": [0.167], "y": [0.179]}, "t": 127, "s": [-6.046]}, {"i": {"x": [0.833], "y": [0.847]}, "o": {"x": [0.167], "y": [0.181]}, "t": 128, "s": [-6.278]}, {"i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.184]}, "t": 129, "s": [-6.476]}, {"i": {"x": [0.833], "y": [0.853]}, "o": {"x": [0.167], "y": [0.187]}, "t": 130, "s": [-6.641]}, {"i": {"x": [0.833], "y": [0.858]}, "o": {"x": [0.167], "y": [0.193]}, "t": 131, "s": [-6.774]}, {"i": {"x": [0.833], "y": [0.868]}, "o": {"x": [0.167], "y": [0.203]}, "t": 132, "s": [-6.875]}, {"i": {"x": [0.833], "y": [0.889]}, "o": {"x": [0.167], "y": [0.225]}, "t": 133, "s": [-6.945]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0.338]}, "t": 134, "s": [-6.986]}, {"i": {"x": [0.833], "y": [0.662]}, "o": {"x": [0.167], "y": [0]}, "t": 135, "s": [-7]}, {"i": {"x": [0.833], "y": [0.775]}, "o": {"x": [0.167], "y": [0.111]}, "t": 136, "s": [-6.986]}, {"i": {"x": [0.833], "y": [0.797]}, "o": {"x": [0.167], "y": [0.132]}, "t": 137, "s": [-6.945]}, {"i": {"x": [0.833], "y": [0.807]}, "o": {"x": [0.167], "y": [0.142]}, "t": 138, "s": [-6.875]}, {"i": {"x": [0.833], "y": [0.813]}, "o": {"x": [0.167], "y": [0.147]}, "t": 139, "s": [-6.774]}, {"i": {"x": [0.833], "y": [0.816]}, "o": {"x": [0.167], "y": [0.15]}, "t": 140, "s": [-6.641]}, {"i": {"x": [0.833], "y": [0.819]}, "o": {"x": [0.167], "y": [0.153]}, "t": 141, "s": [-6.476]}, {"i": {"x": [0.833], "y": [0.821]}, "o": {"x": [0.167], "y": [0.154]}, "t": 142, "s": [-6.278]}, {"i": {"x": [0.833], "y": [0.823]}, "o": {"x": [0.167], "y": [0.156]}, "t": 143, "s": [-6.046]}, {"i": {"x": [0.833], "y": [0.824]}, "o": {"x": [0.167], "y": [0.157]}, "t": 144, "s": [-5.779]}, {"i": {"x": [0.833], "y": [0.826]}, "o": {"x": [0.167], "y": [0.158]}, "t": 145, "s": [-5.478]}, {"i": {"x": [0.833], "y": [0.827]}, "o": {"x": [0.167], "y": [0.16]}, "t": 146, "s": [-5.145]}, {"i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.161]}, "t": 147, "s": [-4.781]}, {"i": {"x": [0.833], "y": [0.83]}, "o": {"x": [0.167], "y": [0.162]}, "t": 148, "s": [-4.389]}, {"i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.163]}, "t": 149, "s": [-3.973]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.165]}, "t": 150, "s": [-3.54]}, {"i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.166]}, "t": 151, "s": [-3.096]}, {"i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.168]}, "t": 152, "s": [-2.65]}, {"i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.17]}, "t": 153, "s": [-2.212]}, {"i": {"x": [0.833], "y": [0.841]}, "o": {"x": [0.167], "y": [0.173]}, "t": 154, "s": [-1.791]}, {"i": {"x": [0.833], "y": [0.844]}, "o": {"x": [0.167], "y": [0.175]}, "t": 155, "s": [-1.398]}, {"i": {"x": [0.833], "y": [0.847]}, "o": {"x": [0.167], "y": [0.179]}, "t": 156, "s": [-1.042]}, {"i": {"x": [0.833], "y": [0.851]}, "o": {"x": [0.167], "y": [0.183]}, "t": 157, "s": [-0.73]}, {"i": {"x": [0.833], "y": [0.857]}, "o": {"x": [0.167], "y": [0.189]}, "t": 158, "s": [-0.469]}, {"i": {"x": [0.833], "y": [0.867]}, "o": {"x": [0.167], "y": [0.2]}, "t": 159, "s": [-0.264]}, {"i": {"x": [0.833], "y": [0.889]}, "o": {"x": [0.167], "y": [0.223]}, "t": 160, "s": [-0.117]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0.335]}, "t": 161, "s": [-0.029]}, {"i": {"x": [0.833], "y": [0.665]}, "o": {"x": [0.167], "y": [0]}, "t": 162, "s": [0]}, {"i": {"x": [0.833], "y": [0.777]}, "o": {"x": [0.167], "y": [0.111]}, "t": 163, "s": [-0.029]}, {"i": {"x": [0.833], "y": [0.8]}, "o": {"x": [0.167], "y": [0.133]}, "t": 164, "s": [-0.117]}, {"i": {"x": [0.833], "y": [0.811]}, "o": {"x": [0.167], "y": [0.143]}, "t": 165, "s": [-0.264]}, {"i": {"x": [0.833], "y": [0.817]}, "o": {"x": [0.167], "y": [0.149]}, "t": 166, "s": [-0.469]}, {"i": {"x": [0.833], "y": [0.821]}, "o": {"x": [0.167], "y": [0.153]}, "t": 167, "s": [-0.73]}, {"i": {"x": [0.833], "y": [0.825]}, "o": {"x": [0.167], "y": [0.156]}, "t": 168, "s": [-1.042]}, {"i": {"x": [0.833], "y": [0.827]}, "o": {"x": [0.167], "y": [0.159]}, "t": 169, "s": [-1.398]}, {"i": {"x": [0.833], "y": [0.83]}, "o": {"x": [0.167], "y": [0.161]}, "t": 170, "s": [-1.791]}, {"i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.163]}, "t": 171, "s": [-2.212]}, {"i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.165]}, "t": 172, "s": [-2.65]}, {"i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.167]}, "t": 173, "s": [-3.096]}, {"i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.169]}, "t": 174, "s": [-3.54]}, {"i": {"x": [0.833], "y": [0.838]}, "o": {"x": [0.167], "y": [0.17]}, "t": 175, "s": [-3.973]}, {"i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.172]}, "t": 176, "s": [-4.389]}, {"i": {"x": [0.833], "y": [0.84]}, "o": {"x": [0.167], "y": [0.173]}, "t": 177, "s": [-4.781]}, {"i": {"x": [0.833], "y": [0.842]}, "o": {"x": [0.167], "y": [0.174]}, "t": 178, "s": [-5.145]}, {"i": {"x": [0.833], "y": [0.843]}, "o": {"x": [0.167], "y": [0.176]}, "t": 179, "s": [-5.478]}, {"i": {"x": [0.833], "y": [0.844]}, "o": {"x": [0.167], "y": [0.177]}, "t": 180, "s": [-5.779]}, {"i": {"x": [0.833], "y": [0.846]}, "o": {"x": [0.167], "y": [0.179]}, "t": 181, "s": [-6.046]}, {"i": {"x": [0.833], "y": [0.847]}, "o": {"x": [0.167], "y": [0.181]}, "t": 182, "s": [-6.278]}, {"i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.184]}, "t": 183, "s": [-6.476]}, {"i": {"x": [0.833], "y": [0.853]}, "o": {"x": [0.167], "y": [0.187]}, "t": 184, "s": [-6.641]}, {"i": {"x": [0.833], "y": [0.858]}, "o": {"x": [0.167], "y": [0.193]}, "t": 185, "s": [-6.774]}, {"i": {"x": [0.833], "y": [0.868]}, "o": {"x": [0.167], "y": [0.203]}, "t": 186, "s": [-6.875]}, {"i": {"x": [0.833], "y": [0.889]}, "o": {"x": [0.167], "y": [0.225]}, "t": 187, "s": [-6.945]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0.338]}, "t": 188, "s": [-6.986]}, {"i": {"x": [0.833], "y": [0.662]}, "o": {"x": [0.167], "y": [0]}, "t": 189, "s": [-7]}, {"i": {"x": [0.833], "y": [0.775]}, "o": {"x": [0.167], "y": [0.111]}, "t": 190, "s": [-6.986]}, {"i": {"x": [0.833], "y": [0.797]}, "o": {"x": [0.167], "y": [0.132]}, "t": 191, "s": [-6.945]}, {"i": {"x": [0.833], "y": [0.807]}, "o": {"x": [0.167], "y": [0.142]}, "t": 192, "s": [-6.875]}, {"i": {"x": [0.833], "y": [0.813]}, "o": {"x": [0.167], "y": [0.147]}, "t": 193, "s": [-6.774]}, {"i": {"x": [0.833], "y": [0.816]}, "o": {"x": [0.167], "y": [0.15]}, "t": 194, "s": [-6.641]}, {"i": {"x": [0.833], "y": [0.819]}, "o": {"x": [0.167], "y": [0.153]}, "t": 195, "s": [-6.476]}, {"i": {"x": [0.833], "y": [0.821]}, "o": {"x": [0.167], "y": [0.154]}, "t": 196, "s": [-6.278]}, {"i": {"x": [0.833], "y": [0.823]}, "o": {"x": [0.167], "y": [0.156]}, "t": 197, "s": [-6.046]}, {"i": {"x": [0.833], "y": [0.824]}, "o": {"x": [0.167], "y": [0.157]}, "t": 198, "s": [-5.779]}, {"i": {"x": [0.833], "y": [0.826]}, "o": {"x": [0.167], "y": [0.158]}, "t": 199, "s": [-5.478]}, {"i": {"x": [0.833], "y": [0.827]}, "o": {"x": [0.167], "y": [0.16]}, "t": 200, "s": [-5.145]}, {"i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.161]}, "t": 201, "s": [-4.781]}, {"i": {"x": [0.833], "y": [0.83]}, "o": {"x": [0.167], "y": [0.162]}, "t": 202, "s": [-4.389]}, {"i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.163]}, "t": 203, "s": [-3.973]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.165]}, "t": 204, "s": [-3.54]}, {"i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.166]}, "t": 205, "s": [-3.096]}, {"i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.168]}, "t": 206, "s": [-2.65]}, {"i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.17]}, "t": 207, "s": [-2.212]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.173]}, "t": 208, "s": [-1.791]}, {"t": 209.000008512745, "s": [-1.398]}], "ix": 10}, "p": {"a": 0, "k": [331.457, 365.148, 0], "ix": 2}, "a": {"a": 0, "k": [46.326, 40.93, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [6.849, -7.472], [-1.245, -9.962], [-0.83, -9.133], [-6.227, 0.83], [0, 0], [0.159, 1.28], [-0.654, 7.524], [0, 0], [-12.038, -4.98], [-4.567, 2.49], [0, 0], [-5.395, 4.565], [5.812, 7.056]], "o": [[0, 0], [0, 0], [0, 0], [1.245, 9.962], [0, 0], [0, 0], [1.192, -0.496], [-0.626, -5.036], [0.83, -9.548], [0, 0], [12.038, 4.981], [4.566, -2.491], [0, 0], [5.397, -4.567], [-5.81, -7.057]], "v": [[-7.471, -30.717], [-9.133, -27.396], [-41.51, -18.68], [-44.831, -7.887], [-37.775, 37.359], [-29.473, 39.85], [-26.518, 38.618], [-24.772, 35.619], [-26.152, 10.379], [-27.812, -4.982], [-4.566, 2.076], [22.415, -1.245], [28.641, -5.396], [39.848, -9.962], [40.263, -33.622]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.093999997307, 0.219999994016, 0.560999971278, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [46.326, 40.93], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "leg", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-0.623, -4.047], [3.58, 0.155], [1.245, 1.557], [1.4, 1.246], [-0.623, 0.934], [-2.491, -1.09], [-4.981, -0.312]], "o": [[0, 0], [0.155, 1.868], [-3.58, -0.157], [-1.245, -1.557], [-1.402, -1.245], [0.622, -0.934], [2.49, 1.09], [4.982, 0.311]], "v": [[10.664, -5.163], [12.375, 4.178], [6.461, 8.432], [-0.389, 5.631], [-10.818, -0.182], [-11.908, -6.096], [-6.46, -7.497], [3.035, -4.072]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.219999994016, 0.152999997606, 0.277999997606, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [8.492, 80.337], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 3, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-2.335, -4.047], [-5.293, -4.048], [-2.802, 0.311], [-0.467, 1.557], [0, 0]], "o": [[0, 0], [2.335, 4.048], [5.292, 4.047], [2.802, -0.312], [0.467, -1.556], [0, 0]], "v": [[-11.208, -8.094], [-11.363, -2.335], [-1.712, 3.269], [8.25, 7.783], [13.231, 4.514], [12.297, 0.311]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.579999976065, 0.638999968884, 0.752999997606, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [7.948, 83.114], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 3, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 94.0000038286985, "st": 0, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 4, "nm": "Layer 9 Outlines", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [319.471, 364.967, 0], "ix": 2}, "a": {"a": 0, "k": [13.948, 38.154, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-3.84, -16.708], [-0.83, -9.132], [-6.225, 0.83], [0, 0], [0.159, 1.279], [-0.655, 7.524], [0, 0], [6.227, 2.906]], "o": [[1.245, 9.962], [0, 0], [0, 0], [1.191, -0.496], [-0.626, -5.036], [0.83, -9.547], [0.207, -3.113], [0, 0]], "v": [[-8.189, -15.929], [-1.133, 29.316], [7.168, 31.807], [10.125, 30.576], [11.871, 27.577], [10.491, 2.335], [8.831, -13.024], [3.225, -29.835]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.078000005086, 0.204000001795, 0.486000001197, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [13.991, 32.887], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-0.623, -4.047], [3.58, 0.156], [1.246, 1.557], [1.4, 1.247], [-0.623, 0.935], [-2.49, -1.09], [-4.981, -0.311]], "o": [[0, 0], [0.155, 1.868], [-3.58, -0.156], [-1.245, -1.557], [-1.402, -1.245], [0.623, -0.933], [2.49, 1.089], [4.982, 0.311]], "v": [[10.664, -5.163], [12.375, 4.177], [6.461, 8.432], [-0.389, 5.63], [-10.818, -0.182], [-11.908, -6.098], [-6.46, -7.497], [3.035, -4.074]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.219999994016, 0.152999997606, 0.277999997606, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [14.492, 65.187], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 3, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-2.335, -4.047], [-5.293, -4.047], [-2.802, 0.311], [-0.466, 1.557], [0, 0]], "o": [[0, 0], [2.335, 4.047], [5.292, 4.048], [2.802, -0.312], [0.467, -1.556], [0, 0]], "v": [[-11.208, -8.095], [-11.363, -2.335], [-1.712, 3.269], [8.25, 7.784], [13.231, 4.514], [12.297, 0.312]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.579999976065, 0.638999968884, 0.752999997606, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [13.948, 67.963], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 3, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 94.0000038286985, "st": 0, "bm": 0}, {"ddd": 0, "ind": 10, "ty": 4, "nm": "Layer 10 Outlines", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [294.772, 406.632, 0], "ix": 2}, "a": {"a": 0, "k": [13.948, 9.979, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [], "ip": 0, "op": 94.0000038286985, "st": 0, "bm": 0}, {"ddd": 0, "ind": 11, "ty": 4, "nm": "Layer 11 Outlines", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [270.764, 243.765, 0], "ix": 2}, "a": {"a": 0, "k": [3.627, 4.31, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [], "ip": 0, "op": 94.0000038286985, "st": 0, "bm": 0}, {"ddd": 0, "ind": 12, "ty": 4, "nm": "Layer 14 Outlines", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.589}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [368.194, 203.706, 0], "to": [0, -0.007, 0], "ti": [0, 0.032, 0]}, {"i": {"x": 0.833, "y": 0.719}, "o": {"x": 0.167, "y": 0.104}, "t": 1, "s": [368.194, 203.667, 0], "to": [0, -0.032, 0], "ti": [0, 0.087, 0]}, {"i": {"x": 0.833, "y": 0.749}, "o": {"x": 0.167, "y": 0.118}, "t": 2, "s": [368.194, 203.512, 0], "to": [0, -0.087, 0], "ti": [0, 0.184, 0]}, {"i": {"x": 0.833, "y": 0.799}, "o": {"x": 0.167, "y": 0.125}, "t": 3, "s": [368.194, 203.146, 0], "to": [0, -0.184, 0], "ti": [0, 0.295, 0]}, {"i": {"x": 0.833, "y": 0.848}, "o": {"x": 0.167, "y": 0.142}, "t": 4, "s": [368.194, 202.41, 0], "to": [0, -0.295, 0], "ti": [0, 0.315, 0]}, {"i": {"x": 0.833, "y": 0.857}, "o": {"x": 0.167, "y": 0.185}, "t": 5, "s": [368.194, 201.373, 0], "to": [0, -0.315, 0], "ti": [0, 0.244, 0]}, {"i": {"x": 0.833, "y": 0.855}, "o": {"x": 0.167, "y": 0.2}, "t": 6, "s": [368.194, 200.52, 0], "to": [0, -0.244, 0], "ti": [0, 0.178, 0]}, {"i": {"x": 0.833, "y": 0.852}, "o": {"x": 0.167, "y": 0.195}, "t": 7, "s": [368.194, 199.909, 0], "to": [0, -0.178, 0], "ti": [0, 0.134, 0]}, {"i": {"x": 0.833, "y": 0.851}, "o": {"x": 0.167, "y": 0.191}, "t": 8, "s": [368.194, 199.454, 0], "to": [0, -0.134, 0], "ti": [0, 0.105, 0]}, {"i": {"x": 0.833, "y": 0.849}, "o": {"x": 0.167, "y": 0.189}, "t": 9, "s": [368.194, 199.104, 0], "to": [0, -0.105, 0], "ti": [0, 0.084, 0]}, {"i": {"x": 0.833, "y": 0.848}, "o": {"x": 0.167, "y": 0.186}, "t": 10, "s": [368.194, 198.827, 0], "to": [0, -0.084, 0], "ti": [0, 0.068, 0]}, {"i": {"x": 0.833, "y": 0.847}, "o": {"x": 0.167, "y": 0.184}, "t": 11, "s": [368.194, 198.603, 0], "to": [0, -0.068, 0], "ti": [0, 0.056, 0]}, {"i": {"x": 0.833, "y": 0.847}, "o": {"x": 0.167, "y": 0.184}, "t": 12, "s": [368.194, 198.418, 0], "to": [0, -0.056, 0], "ti": [0, 0.047, 0]}, {"i": {"x": 0.833, "y": 0.848}, "o": {"x": 0.167, "y": 0.183}, "t": 13, "s": [368.194, 198.264, 0], "to": [0, -0.047, 0], "ti": [0, 0.039, 0]}, {"i": {"x": 0.833, "y": 0.848}, "o": {"x": 0.167, "y": 0.184}, "t": 14, "s": [368.194, 198.136, 0], "to": [0, -0.039, 0], "ti": [0, 0.032, 0]}, {"i": {"x": 0.833, "y": 0.849}, "o": {"x": 0.167, "y": 0.185}, "t": 15, "s": [368.194, 198.03, 0], "to": [0, -0.032, 0], "ti": [0, 0.026, 0]}, {"i": {"x": 0.833, "y": 0.851}, "o": {"x": 0.167, "y": 0.186}, "t": 16, "s": [368.194, 197.943, 0], "to": [0, -0.026, 0], "ti": [0, 0.021, 0]}, {"i": {"x": 0.833, "y": 0.853}, "o": {"x": 0.167, "y": 0.189}, "t": 17, "s": [368.194, 197.872, 0], "to": [0, -0.021, 0], "ti": [0, 0.016, 0]}, {"i": {"x": 0.833, "y": 0.856}, "o": {"x": 0.167, "y": 0.192}, "t": 18, "s": [368.194, 197.817, 0], "to": [0, -0.016, 0], "ti": [0, 0.012, 0]}, {"i": {"x": 0.833, "y": 0.861}, "o": {"x": 0.167, "y": 0.197}, "t": 19, "s": [368.194, 197.774, 0], "to": [0, -0.012, 0], "ti": [0, 0.009, 0]}, {"i": {"x": 0.833, "y": 0.869}, "o": {"x": 0.167, "y": 0.207}, "t": 20, "s": [368.194, 197.743, 0], "to": [0, -0.009, 0], "ti": [0, 0.005, 0]}, {"i": {"x": 0.833, "y": 0.89}, "o": {"x": 0.167, "y": 0.23}, "t": 21, "s": [368.194, 197.722, 0], "to": [0, -0.005, 0], "ti": [0, 0.003, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.345}, "t": 22, "s": [368.194, 197.71, 0], "to": [0, -0.003, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.655}, "o": {"x": 0.167, "y": 0.167}, "t": 23, "s": [368.194, 197.706, 0], "to": [0, 0, 0], "ti": [0, -0.003, 0]}, {"i": {"x": 0.833, "y": 0.77}, "o": {"x": 0.167, "y": 0.11}, "t": 24, "s": [368.194, 197.71, 0], "to": [0, 0.003, 0], "ti": [0, -0.005, 0]}, {"i": {"x": 0.833, "y": 0.793}, "o": {"x": 0.167, "y": 0.131}, "t": 25, "s": [368.194, 197.722, 0], "to": [0, 0.005, 0], "ti": [0, -0.009, 0]}, {"i": {"x": 0.833, "y": 0.803}, "o": {"x": 0.167, "y": 0.139}, "t": 26, "s": [368.194, 197.743, 0], "to": [0, 0.009, 0], "ti": [0, -0.012, 0]}, {"i": {"x": 0.833, "y": 0.808}, "o": {"x": 0.167, "y": 0.144}, "t": 27, "s": [368.194, 197.774, 0], "to": [0, 0.012, 0], "ti": [0, -0.016, 0]}, {"i": {"x": 0.833, "y": 0.811}, "o": {"x": 0.167, "y": 0.147}, "t": 28, "s": [368.194, 197.817, 0], "to": [0, 0.016, 0], "ti": [0, -0.021, 0]}, {"i": {"x": 0.833, "y": 0.814}, "o": {"x": 0.167, "y": 0.149}, "t": 29, "s": [368.194, 197.872, 0], "to": [0, 0.021, 0], "ti": [0, -0.026, 0]}, {"i": {"x": 0.833, "y": 0.815}, "o": {"x": 0.167, "y": 0.151}, "t": 30, "s": [368.194, 197.943, 0], "to": [0, 0.026, 0], "ti": [0, -0.032, 0]}, {"i": {"x": 0.833, "y": 0.816}, "o": {"x": 0.167, "y": 0.152}, "t": 31, "s": [368.194, 198.03, 0], "to": [0, 0.032, 0], "ti": [0, -0.039, 0]}, {"i": {"x": 0.833, "y": 0.817}, "o": {"x": 0.167, "y": 0.152}, "t": 32, "s": [368.194, 198.136, 0], "to": [0, 0.039, 0], "ti": [0, -0.047, 0]}, {"i": {"x": 0.833, "y": 0.816}, "o": {"x": 0.167, "y": 0.153}, "t": 33, "s": [368.194, 198.264, 0], "to": [0, 0.047, 0], "ti": [0, -0.056, 0]}, {"i": {"x": 0.833, "y": 0.816}, "o": {"x": 0.167, "y": 0.153}, "t": 34, "s": [368.194, 198.418, 0], "to": [0, 0.056, 0], "ti": [0, -0.068, 0]}, {"i": {"x": 0.833, "y": 0.814}, "o": {"x": 0.167, "y": 0.152}, "t": 35, "s": [368.194, 198.603, 0], "to": [0, 0.068, 0], "ti": [0, -0.084, 0]}, {"i": {"x": 0.833, "y": 0.811}, "o": {"x": 0.167, "y": 0.151}, "t": 36, "s": [368.194, 198.827, 0], "to": [0, 0.084, 0], "ti": [0, -0.105, 0]}, {"i": {"x": 0.833, "y": 0.809}, "o": {"x": 0.167, "y": 0.149}, "t": 37, "s": [368.194, 199.104, 0], "to": [0, 0.105, 0], "ti": [0, -0.134, 0]}, {"i": {"x": 0.833, "y": 0.805}, "o": {"x": 0.167, "y": 0.148}, "t": 38, "s": [368.194, 199.454, 0], "to": [0, 0.134, 0], "ti": [0, -0.178, 0]}, {"i": {"x": 0.833, "y": 0.8}, "o": {"x": 0.167, "y": 0.145}, "t": 39, "s": [368.194, 199.909, 0], "to": [0, 0.178, 0], "ti": [0, -0.244, 0]}, {"i": {"x": 0.833, "y": 0.815}, "o": {"x": 0.167, "y": 0.143}, "t": 40, "s": [368.194, 200.52, 0], "to": [0, 0.244, 0], "ti": [0, -0.315, 0]}, {"i": {"x": 0.833, "y": 0.858}, "o": {"x": 0.167, "y": 0.152}, "t": 41, "s": [368.194, 201.373, 0], "to": [0, 0.315, 0], "ti": [0, -0.295, 0]}, {"i": {"x": 0.833, "y": 0.875}, "o": {"x": 0.167, "y": 0.201}, "t": 42, "s": [368.194, 202.41, 0], "to": [0, 0.295, 0], "ti": [0, -0.184, 0]}, {"i": {"x": 0.833, "y": 0.882}, "o": {"x": 0.167, "y": 0.251}, "t": 43, "s": [368.194, 203.146, 0], "to": [0, 0.184, 0], "ti": [0, -0.087, 0]}, {"i": {"x": 0.833, "y": 0.896}, "o": {"x": 0.167, "y": 0.281}, "t": 44, "s": [368.194, 203.512, 0], "to": [0, 0.087, 0], "ti": [0, -0.032, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.411}, "t": 45, "s": [368.194, 203.667, 0], "to": [0, 0.032, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.589}, "o": {"x": 0.167, "y": 0.167}, "t": 46, "s": [368.194, 203.706, 0], "to": [0, 0, 0], "ti": [0, 0.032, 0]}, {"i": {"x": 0.833, "y": 0.719}, "o": {"x": 0.167, "y": 0.104}, "t": 47, "s": [368.194, 203.667, 0], "to": [0, -0.032, 0], "ti": [0, 0.087, 0]}, {"i": {"x": 0.833, "y": 0.749}, "o": {"x": 0.167, "y": 0.118}, "t": 48, "s": [368.194, 203.512, 0], "to": [0, -0.087, 0], "ti": [0, 0.184, 0]}, {"i": {"x": 0.833, "y": 0.799}, "o": {"x": 0.167, "y": 0.125}, "t": 49, "s": [368.194, 203.146, 0], "to": [0, -0.184, 0], "ti": [0, 0.295, 0]}, {"i": {"x": 0.833, "y": 0.848}, "o": {"x": 0.167, "y": 0.142}, "t": 50, "s": [368.194, 202.41, 0], "to": [0, -0.295, 0], "ti": [0, 0.315, 0]}, {"i": {"x": 0.833, "y": 0.857}, "o": {"x": 0.167, "y": 0.185}, "t": 51, "s": [368.194, 201.373, 0], "to": [0, -0.315, 0], "ti": [0, 0.244, 0]}, {"i": {"x": 0.833, "y": 0.855}, "o": {"x": 0.167, "y": 0.2}, "t": 52, "s": [368.194, 200.52, 0], "to": [0, -0.244, 0], "ti": [0, 0.178, 0]}, {"i": {"x": 0.833, "y": 0.852}, "o": {"x": 0.167, "y": 0.195}, "t": 53, "s": [368.194, 199.909, 0], "to": [0, -0.178, 0], "ti": [0, 0.134, 0]}, {"i": {"x": 0.833, "y": 0.851}, "o": {"x": 0.167, "y": 0.191}, "t": 54, "s": [368.194, 199.454, 0], "to": [0, -0.134, 0], "ti": [0, 0.105, 0]}, {"i": {"x": 0.833, "y": 0.849}, "o": {"x": 0.167, "y": 0.189}, "t": 55, "s": [368.194, 199.104, 0], "to": [0, -0.105, 0], "ti": [0, 0.084, 0]}, {"i": {"x": 0.833, "y": 0.848}, "o": {"x": 0.167, "y": 0.186}, "t": 56, "s": [368.194, 198.827, 0], "to": [0, -0.084, 0], "ti": [0, 0.068, 0]}, {"i": {"x": 0.833, "y": 0.847}, "o": {"x": 0.167, "y": 0.184}, "t": 57, "s": [368.194, 198.603, 0], "to": [0, -0.068, 0], "ti": [0, 0.056, 0]}, {"i": {"x": 0.833, "y": 0.847}, "o": {"x": 0.167, "y": 0.184}, "t": 58, "s": [368.194, 198.418, 0], "to": [0, -0.056, 0], "ti": [0, 0.047, 0]}, {"i": {"x": 0.833, "y": 0.848}, "o": {"x": 0.167, "y": 0.183}, "t": 59, "s": [368.194, 198.264, 0], "to": [0, -0.047, 0], "ti": [0, 0.039, 0]}, {"i": {"x": 0.833, "y": 0.848}, "o": {"x": 0.167, "y": 0.184}, "t": 60, "s": [368.194, 198.136, 0], "to": [0, -0.039, 0], "ti": [0, 0.032, 0]}, {"i": {"x": 0.833, "y": 0.849}, "o": {"x": 0.167, "y": 0.185}, "t": 61, "s": [368.194, 198.03, 0], "to": [0, -0.032, 0], "ti": [0, 0.026, 0]}, {"i": {"x": 0.833, "y": 0.851}, "o": {"x": 0.167, "y": 0.186}, "t": 62, "s": [368.194, 197.943, 0], "to": [0, -0.026, 0], "ti": [0, 0.021, 0]}, {"i": {"x": 0.833, "y": 0.853}, "o": {"x": 0.167, "y": 0.189}, "t": 63, "s": [368.194, 197.872, 0], "to": [0, -0.021, 0], "ti": [0, 0.016, 0]}, {"i": {"x": 0.833, "y": 0.856}, "o": {"x": 0.167, "y": 0.192}, "t": 64, "s": [368.194, 197.817, 0], "to": [0, -0.016, 0], "ti": [0, 0.012, 0]}, {"i": {"x": 0.833, "y": 0.861}, "o": {"x": 0.167, "y": 0.197}, "t": 65, "s": [368.194, 197.774, 0], "to": [0, -0.012, 0], "ti": [0, 0.009, 0]}, {"i": {"x": 0.833, "y": 0.869}, "o": {"x": 0.167, "y": 0.207}, "t": 66, "s": [368.194, 197.743, 0], "to": [0, -0.009, 0], "ti": [0, 0.005, 0]}, {"i": {"x": 0.833, "y": 0.89}, "o": {"x": 0.167, "y": 0.23}, "t": 67, "s": [368.194, 197.722, 0], "to": [0, -0.005, 0], "ti": [0, 0.003, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.345}, "t": 68, "s": [368.194, 197.71, 0], "to": [0, -0.003, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.655}, "o": {"x": 0.167, "y": 0.167}, "t": 69, "s": [368.194, 197.706, 0], "to": [0, 0, 0], "ti": [0, -0.003, 0]}, {"i": {"x": 0.833, "y": 0.77}, "o": {"x": 0.167, "y": 0.11}, "t": 70, "s": [368.194, 197.71, 0], "to": [0, 0.003, 0], "ti": [0, -0.005, 0]}, {"i": {"x": 0.833, "y": 0.793}, "o": {"x": 0.167, "y": 0.131}, "t": 71, "s": [368.194, 197.722, 0], "to": [0, 0.005, 0], "ti": [0, -0.009, 0]}, {"i": {"x": 0.833, "y": 0.803}, "o": {"x": 0.167, "y": 0.139}, "t": 72, "s": [368.194, 197.743, 0], "to": [0, 0.009, 0], "ti": [0, -0.012, 0]}, {"i": {"x": 0.833, "y": 0.808}, "o": {"x": 0.167, "y": 0.144}, "t": 73, "s": [368.194, 197.774, 0], "to": [0, 0.012, 0], "ti": [0, -0.016, 0]}, {"i": {"x": 0.833, "y": 0.811}, "o": {"x": 0.167, "y": 0.147}, "t": 74, "s": [368.194, 197.817, 0], "to": [0, 0.016, 0], "ti": [0, -0.021, 0]}, {"i": {"x": 0.833, "y": 0.814}, "o": {"x": 0.167, "y": 0.149}, "t": 75, "s": [368.194, 197.872, 0], "to": [0, 0.021, 0], "ti": [0, -0.026, 0]}, {"i": {"x": 0.833, "y": 0.815}, "o": {"x": 0.167, "y": 0.151}, "t": 76, "s": [368.194, 197.943, 0], "to": [0, 0.026, 0], "ti": [0, -0.032, 0]}, {"i": {"x": 0.833, "y": 0.816}, "o": {"x": 0.167, "y": 0.152}, "t": 77, "s": [368.194, 198.03, 0], "to": [0, 0.032, 0], "ti": [0, -0.039, 0]}, {"i": {"x": 0.833, "y": 0.817}, "o": {"x": 0.167, "y": 0.152}, "t": 78, "s": [368.194, 198.136, 0], "to": [0, 0.039, 0], "ti": [0, -0.047, 0]}, {"i": {"x": 0.833, "y": 0.816}, "o": {"x": 0.167, "y": 0.153}, "t": 79, "s": [368.194, 198.264, 0], "to": [0, 0.047, 0], "ti": [0, -0.056, 0]}, {"i": {"x": 0.833, "y": 0.816}, "o": {"x": 0.167, "y": 0.153}, "t": 80, "s": [368.194, 198.418, 0], "to": [0, 0.056, 0], "ti": [0, -0.068, 0]}, {"i": {"x": 0.833, "y": 0.814}, "o": {"x": 0.167, "y": 0.152}, "t": 81, "s": [368.194, 198.603, 0], "to": [0, 0.068, 0], "ti": [0, -0.084, 0]}, {"i": {"x": 0.833, "y": 0.811}, "o": {"x": 0.167, "y": 0.151}, "t": 82, "s": [368.194, 198.827, 0], "to": [0, 0.084, 0], "ti": [0, -0.105, 0]}, {"i": {"x": 0.833, "y": 0.809}, "o": {"x": 0.167, "y": 0.149}, "t": 83, "s": [368.194, 199.104, 0], "to": [0, 0.105, 0], "ti": [0, -0.134, 0]}, {"i": {"x": 0.833, "y": 0.805}, "o": {"x": 0.167, "y": 0.148}, "t": 84, "s": [368.194, 199.454, 0], "to": [0, 0.134, 0], "ti": [0, -0.178, 0]}, {"i": {"x": 0.833, "y": 0.8}, "o": {"x": 0.167, "y": 0.145}, "t": 85, "s": [368.194, 199.909, 0], "to": [0, 0.178, 0], "ti": [0, -0.244, 0]}, {"i": {"x": 0.833, "y": 0.815}, "o": {"x": 0.167, "y": 0.143}, "t": 86, "s": [368.194, 200.52, 0], "to": [0, 0.244, 0], "ti": [0, -0.315, 0]}, {"i": {"x": 0.833, "y": 0.858}, "o": {"x": 0.167, "y": 0.152}, "t": 87, "s": [368.194, 201.373, 0], "to": [0, 0.315, 0], "ti": [0, -0.295, 0]}, {"i": {"x": 0.833, "y": 0.875}, "o": {"x": 0.167, "y": 0.201}, "t": 88, "s": [368.194, 202.41, 0], "to": [0, 0.295, 0], "ti": [0, -0.184, 0]}, {"i": {"x": 0.833, "y": 0.882}, "o": {"x": 0.167, "y": 0.251}, "t": 89, "s": [368.194, 203.146, 0], "to": [0, 0.184, 0], "ti": [0, -0.087, 0]}, {"i": {"x": 0.833, "y": 0.896}, "o": {"x": 0.167, "y": 0.281}, "t": 90, "s": [368.194, 203.512, 0], "to": [0, 0.087, 0], "ti": [0, -0.032, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.411}, "t": 91, "s": [368.194, 203.667, 0], "to": [0, 0.032, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.589}, "o": {"x": 0.167, "y": 0.167}, "t": 92, "s": [368.194, 203.706, 0], "to": [0, 0, 0], "ti": [0, 0.032, 0]}, {"i": {"x": 0.833, "y": 0.719}, "o": {"x": 0.167, "y": 0.104}, "t": 93, "s": [368.194, 203.667, 0], "to": [0, -0.032, 0], "ti": [0, 0.087, 0]}, {"i": {"x": 0.833, "y": 0.749}, "o": {"x": 0.167, "y": 0.118}, "t": 94, "s": [368.194, 203.512, 0], "to": [0, -0.087, 0], "ti": [0, 0.184, 0]}, {"i": {"x": 0.833, "y": 0.799}, "o": {"x": 0.167, "y": 0.125}, "t": 95, "s": [368.194, 203.146, 0], "to": [0, -0.184, 0], "ti": [0, 0.295, 0]}, {"i": {"x": 0.833, "y": 0.848}, "o": {"x": 0.167, "y": 0.142}, "t": 96, "s": [368.194, 202.41, 0], "to": [0, -0.295, 0], "ti": [0, 0.315, 0]}, {"i": {"x": 0.833, "y": 0.857}, "o": {"x": 0.167, "y": 0.185}, "t": 97, "s": [368.194, 201.373, 0], "to": [0, -0.315, 0], "ti": [0, 0.244, 0]}, {"i": {"x": 0.833, "y": 0.855}, "o": {"x": 0.167, "y": 0.2}, "t": 98, "s": [368.194, 200.52, 0], "to": [0, -0.244, 0], "ti": [0, 0.178, 0]}, {"i": {"x": 0.833, "y": 0.852}, "o": {"x": 0.167, "y": 0.195}, "t": 99, "s": [368.194, 199.909, 0], "to": [0, -0.178, 0], "ti": [0, 0.134, 0]}, {"i": {"x": 0.833, "y": 0.851}, "o": {"x": 0.167, "y": 0.191}, "t": 100, "s": [368.194, 199.454, 0], "to": [0, -0.134, 0], "ti": [0, 0.105, 0]}, {"i": {"x": 0.833, "y": 0.849}, "o": {"x": 0.167, "y": 0.189}, "t": 101, "s": [368.194, 199.104, 0], "to": [0, -0.105, 0], "ti": [0, 0.084, 0]}, {"i": {"x": 0.833, "y": 0.848}, "o": {"x": 0.167, "y": 0.186}, "t": 102, "s": [368.194, 198.827, 0], "to": [0, -0.084, 0], "ti": [0, 0.068, 0]}, {"i": {"x": 0.833, "y": 0.847}, "o": {"x": 0.167, "y": 0.184}, "t": 103, "s": [368.194, 198.603, 0], "to": [0, -0.068, 0], "ti": [0, 0.056, 0]}, {"i": {"x": 0.833, "y": 0.847}, "o": {"x": 0.167, "y": 0.184}, "t": 104, "s": [368.194, 198.418, 0], "to": [0, -0.056, 0], "ti": [0, 0.047, 0]}, {"i": {"x": 0.833, "y": 0.848}, "o": {"x": 0.167, "y": 0.183}, "t": 105, "s": [368.194, 198.264, 0], "to": [0, -0.047, 0], "ti": [0, 0.039, 0]}, {"i": {"x": 0.833, "y": 0.848}, "o": {"x": 0.167, "y": 0.184}, "t": 106, "s": [368.194, 198.136, 0], "to": [0, -0.039, 0], "ti": [0, 0.032, 0]}, {"i": {"x": 0.833, "y": 0.849}, "o": {"x": 0.167, "y": 0.185}, "t": 107, "s": [368.194, 198.03, 0], "to": [0, -0.032, 0], "ti": [0, 0.026, 0]}, {"i": {"x": 0.833, "y": 0.851}, "o": {"x": 0.167, "y": 0.186}, "t": 108, "s": [368.194, 197.943, 0], "to": [0, -0.026, 0], "ti": [0, 0.021, 0]}, {"i": {"x": 0.833, "y": 0.853}, "o": {"x": 0.167, "y": 0.189}, "t": 109, "s": [368.194, 197.872, 0], "to": [0, -0.021, 0], "ti": [0, 0.016, 0]}, {"i": {"x": 0.833, "y": 0.856}, "o": {"x": 0.167, "y": 0.192}, "t": 110, "s": [368.194, 197.817, 0], "to": [0, -0.016, 0], "ti": [0, 0.012, 0]}, {"i": {"x": 0.833, "y": 0.861}, "o": {"x": 0.167, "y": 0.197}, "t": 111, "s": [368.194, 197.774, 0], "to": [0, -0.012, 0], "ti": [0, 0.009, 0]}, {"i": {"x": 0.833, "y": 0.869}, "o": {"x": 0.167, "y": 0.207}, "t": 112, "s": [368.194, 197.743, 0], "to": [0, -0.009, 0], "ti": [0, 0.005, 0]}, {"i": {"x": 0.833, "y": 0.89}, "o": {"x": 0.167, "y": 0.23}, "t": 113, "s": [368.194, 197.722, 0], "to": [0, -0.005, 0], "ti": [0, 0.003, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.345}, "t": 114, "s": [368.194, 197.71, 0], "to": [0, -0.003, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.655}, "o": {"x": 0.167, "y": 0.167}, "t": 115, "s": [368.194, 197.706, 0], "to": [0, 0, 0], "ti": [0, -0.003, 0]}, {"i": {"x": 0.833, "y": 0.77}, "o": {"x": 0.167, "y": 0.11}, "t": 116, "s": [368.194, 197.71, 0], "to": [0, 0.003, 0], "ti": [0, -0.005, 0]}, {"i": {"x": 0.833, "y": 0.793}, "o": {"x": 0.167, "y": 0.131}, "t": 117, "s": [368.194, 197.722, 0], "to": [0, 0.005, 0], "ti": [0, -0.009, 0]}, {"i": {"x": 0.833, "y": 0.803}, "o": {"x": 0.167, "y": 0.139}, "t": 118, "s": [368.194, 197.743, 0], "to": [0, 0.009, 0], "ti": [0, -0.012, 0]}, {"i": {"x": 0.833, "y": 0.808}, "o": {"x": 0.167, "y": 0.144}, "t": 119, "s": [368.194, 197.774, 0], "to": [0, 0.012, 0], "ti": [0, -0.016, 0]}, {"i": {"x": 0.833, "y": 0.811}, "o": {"x": 0.167, "y": 0.147}, "t": 120, "s": [368.194, 197.817, 0], "to": [0, 0.016, 0], "ti": [0, -0.021, 0]}, {"i": {"x": 0.833, "y": 0.814}, "o": {"x": 0.167, "y": 0.149}, "t": 121, "s": [368.194, 197.872, 0], "to": [0, 0.021, 0], "ti": [0, -0.026, 0]}, {"i": {"x": 0.833, "y": 0.815}, "o": {"x": 0.167, "y": 0.151}, "t": 122, "s": [368.194, 197.943, 0], "to": [0, 0.026, 0], "ti": [0, -0.032, 0]}, {"i": {"x": 0.833, "y": 0.816}, "o": {"x": 0.167, "y": 0.152}, "t": 123, "s": [368.194, 198.03, 0], "to": [0, 0.032, 0], "ti": [0, -0.039, 0]}, {"i": {"x": 0.833, "y": 0.817}, "o": {"x": 0.167, "y": 0.152}, "t": 124, "s": [368.194, 198.136, 0], "to": [0, 0.039, 0], "ti": [0, -0.047, 0]}, {"i": {"x": 0.833, "y": 0.816}, "o": {"x": 0.167, "y": 0.153}, "t": 125, "s": [368.194, 198.264, 0], "to": [0, 0.047, 0], "ti": [0, -0.056, 0]}, {"i": {"x": 0.833, "y": 0.816}, "o": {"x": 0.167, "y": 0.153}, "t": 126, "s": [368.194, 198.418, 0], "to": [0, 0.056, 0], "ti": [0, -0.068, 0]}, {"i": {"x": 0.833, "y": 0.814}, "o": {"x": 0.167, "y": 0.152}, "t": 127, "s": [368.194, 198.603, 0], "to": [0, 0.068, 0], "ti": [0, -0.084, 0]}, {"i": {"x": 0.833, "y": 0.811}, "o": {"x": 0.167, "y": 0.151}, "t": 128, "s": [368.194, 198.827, 0], "to": [0, 0.084, 0], "ti": [0, -0.105, 0]}, {"i": {"x": 0.833, "y": 0.809}, "o": {"x": 0.167, "y": 0.149}, "t": 129, "s": [368.194, 199.104, 0], "to": [0, 0.105, 0], "ti": [0, -0.134, 0]}, {"i": {"x": 0.833, "y": 0.805}, "o": {"x": 0.167, "y": 0.148}, "t": 130, "s": [368.194, 199.454, 0], "to": [0, 0.134, 0], "ti": [0, -0.178, 0]}, {"i": {"x": 0.833, "y": 0.8}, "o": {"x": 0.167, "y": 0.145}, "t": 131, "s": [368.194, 199.909, 0], "to": [0, 0.178, 0], "ti": [0, -0.244, 0]}, {"i": {"x": 0.833, "y": 0.815}, "o": {"x": 0.167, "y": 0.143}, "t": 132, "s": [368.194, 200.52, 0], "to": [0, 0.244, 0], "ti": [0, -0.315, 0]}, {"i": {"x": 0.833, "y": 0.858}, "o": {"x": 0.167, "y": 0.152}, "t": 133, "s": [368.194, 201.373, 0], "to": [0, 0.315, 0], "ti": [0, -0.295, 0]}, {"i": {"x": 0.833, "y": 0.875}, "o": {"x": 0.167, "y": 0.201}, "t": 134, "s": [368.194, 202.41, 0], "to": [0, 0.295, 0], "ti": [0, -0.184, 0]}, {"i": {"x": 0.833, "y": 0.882}, "o": {"x": 0.167, "y": 0.251}, "t": 135, "s": [368.194, 203.146, 0], "to": [0, 0.184, 0], "ti": [0, -0.087, 0]}, {"i": {"x": 0.833, "y": 0.896}, "o": {"x": 0.167, "y": 0.281}, "t": 136, "s": [368.194, 203.512, 0], "to": [0, 0.087, 0], "ti": [0, -0.032, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.411}, "t": 137, "s": [368.194, 203.667, 0], "to": [0, 0.032, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.589}, "o": {"x": 0.167, "y": 0.167}, "t": 138, "s": [368.194, 203.706, 0], "to": [0, 0, 0], "ti": [0, 0.032, 0]}, {"i": {"x": 0.833, "y": 0.719}, "o": {"x": 0.167, "y": 0.104}, "t": 139, "s": [368.194, 203.667, 0], "to": [0, -0.032, 0], "ti": [0, 0.087, 0]}, {"i": {"x": 0.833, "y": 0.749}, "o": {"x": 0.167, "y": 0.118}, "t": 140, "s": [368.194, 203.512, 0], "to": [0, -0.087, 0], "ti": [0, 0.184, 0]}, {"i": {"x": 0.833, "y": 0.799}, "o": {"x": 0.167, "y": 0.125}, "t": 141, "s": [368.194, 203.146, 0], "to": [0, -0.184, 0], "ti": [0, 0.295, 0]}, {"i": {"x": 0.833, "y": 0.848}, "o": {"x": 0.167, "y": 0.142}, "t": 142, "s": [368.194, 202.41, 0], "to": [0, -0.295, 0], "ti": [0, 0.315, 0]}, {"i": {"x": 0.833, "y": 0.857}, "o": {"x": 0.167, "y": 0.185}, "t": 143, "s": [368.194, 201.373, 0], "to": [0, -0.315, 0], "ti": [0, 0.244, 0]}, {"i": {"x": 0.833, "y": 0.855}, "o": {"x": 0.167, "y": 0.2}, "t": 144, "s": [368.194, 200.52, 0], "to": [0, -0.244, 0], "ti": [0, 0.178, 0]}, {"i": {"x": 0.833, "y": 0.852}, "o": {"x": 0.167, "y": 0.195}, "t": 145, "s": [368.194, 199.909, 0], "to": [0, -0.178, 0], "ti": [0, 0.134, 0]}, {"i": {"x": 0.833, "y": 0.851}, "o": {"x": 0.167, "y": 0.191}, "t": 146, "s": [368.194, 199.454, 0], "to": [0, -0.134, 0], "ti": [0, 0.105, 0]}, {"i": {"x": 0.833, "y": 0.849}, "o": {"x": 0.167, "y": 0.189}, "t": 147, "s": [368.194, 199.104, 0], "to": [0, -0.105, 0], "ti": [0, 0.084, 0]}, {"i": {"x": 0.833, "y": 0.848}, "o": {"x": 0.167, "y": 0.186}, "t": 148, "s": [368.194, 198.827, 0], "to": [0, -0.084, 0], "ti": [0, 0.068, 0]}, {"i": {"x": 0.833, "y": 0.847}, "o": {"x": 0.167, "y": 0.184}, "t": 149, "s": [368.194, 198.603, 0], "to": [0, -0.068, 0], "ti": [0, 0.056, 0]}, {"i": {"x": 0.833, "y": 0.847}, "o": {"x": 0.167, "y": 0.184}, "t": 150, "s": [368.194, 198.418, 0], "to": [0, -0.056, 0], "ti": [0, 0.047, 0]}, {"i": {"x": 0.833, "y": 0.848}, "o": {"x": 0.167, "y": 0.183}, "t": 151, "s": [368.194, 198.264, 0], "to": [0, -0.047, 0], "ti": [0, 0.039, 0]}, {"i": {"x": 0.833, "y": 0.848}, "o": {"x": 0.167, "y": 0.184}, "t": 152, "s": [368.194, 198.136, 0], "to": [0, -0.039, 0], "ti": [0, 0.032, 0]}, {"i": {"x": 0.833, "y": 0.849}, "o": {"x": 0.167, "y": 0.185}, "t": 153, "s": [368.194, 198.03, 0], "to": [0, -0.032, 0], "ti": [0, 0.026, 0]}, {"i": {"x": 0.833, "y": 0.851}, "o": {"x": 0.167, "y": 0.186}, "t": 154, "s": [368.194, 197.943, 0], "to": [0, -0.026, 0], "ti": [0, 0.021, 0]}, {"i": {"x": 0.833, "y": 0.853}, "o": {"x": 0.167, "y": 0.189}, "t": 155, "s": [368.194, 197.872, 0], "to": [0, -0.021, 0], "ti": [0, 0.016, 0]}, {"i": {"x": 0.833, "y": 0.856}, "o": {"x": 0.167, "y": 0.192}, "t": 156, "s": [368.194, 197.817, 0], "to": [0, -0.016, 0], "ti": [0, 0.012, 0]}, {"i": {"x": 0.833, "y": 0.861}, "o": {"x": 0.167, "y": 0.197}, "t": 157, "s": [368.194, 197.774, 0], "to": [0, -0.012, 0], "ti": [0, 0.009, 0]}, {"i": {"x": 0.833, "y": 0.869}, "o": {"x": 0.167, "y": 0.207}, "t": 158, "s": [368.194, 197.743, 0], "to": [0, -0.009, 0], "ti": [0, 0.005, 0]}, {"i": {"x": 0.833, "y": 0.89}, "o": {"x": 0.167, "y": 0.23}, "t": 159, "s": [368.194, 197.722, 0], "to": [0, -0.005, 0], "ti": [0, 0.003, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.345}, "t": 160, "s": [368.194, 197.71, 0], "to": [0, -0.003, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.655}, "o": {"x": 0.167, "y": 0.167}, "t": 161, "s": [368.194, 197.706, 0], "to": [0, 0, 0], "ti": [0, -0.003, 0]}, {"i": {"x": 0.833, "y": 0.77}, "o": {"x": 0.167, "y": 0.11}, "t": 162, "s": [368.194, 197.71, 0], "to": [0, 0.003, 0], "ti": [0, -0.005, 0]}, {"i": {"x": 0.833, "y": 0.793}, "o": {"x": 0.167, "y": 0.131}, "t": 163, "s": [368.194, 197.722, 0], "to": [0, 0.005, 0], "ti": [0, -0.009, 0]}, {"i": {"x": 0.833, "y": 0.803}, "o": {"x": 0.167, "y": 0.139}, "t": 164, "s": [368.194, 197.743, 0], "to": [0, 0.009, 0], "ti": [0, -0.012, 0]}, {"i": {"x": 0.833, "y": 0.808}, "o": {"x": 0.167, "y": 0.144}, "t": 165, "s": [368.194, 197.774, 0], "to": [0, 0.012, 0], "ti": [0, -0.016, 0]}, {"i": {"x": 0.833, "y": 0.811}, "o": {"x": 0.167, "y": 0.147}, "t": 166, "s": [368.194, 197.817, 0], "to": [0, 0.016, 0], "ti": [0, -0.021, 0]}, {"i": {"x": 0.833, "y": 0.814}, "o": {"x": 0.167, "y": 0.149}, "t": 167, "s": [368.194, 197.872, 0], "to": [0, 0.021, 0], "ti": [0, -0.026, 0]}, {"i": {"x": 0.833, "y": 0.815}, "o": {"x": 0.167, "y": 0.151}, "t": 168, "s": [368.194, 197.943, 0], "to": [0, 0.026, 0], "ti": [0, -0.032, 0]}, {"i": {"x": 0.833, "y": 0.816}, "o": {"x": 0.167, "y": 0.152}, "t": 169, "s": [368.194, 198.03, 0], "to": [0, 0.032, 0], "ti": [0, -0.039, 0]}, {"i": {"x": 0.833, "y": 0.817}, "o": {"x": 0.167, "y": 0.152}, "t": 170, "s": [368.194, 198.136, 0], "to": [0, 0.039, 0], "ti": [0, -0.047, 0]}, {"i": {"x": 0.833, "y": 0.816}, "o": {"x": 0.167, "y": 0.153}, "t": 171, "s": [368.194, 198.264, 0], "to": [0, 0.047, 0], "ti": [0, -0.056, 0]}, {"i": {"x": 0.833, "y": 0.816}, "o": {"x": 0.167, "y": 0.153}, "t": 172, "s": [368.194, 198.418, 0], "to": [0, 0.056, 0], "ti": [0, -0.068, 0]}, {"i": {"x": 0.833, "y": 0.814}, "o": {"x": 0.167, "y": 0.152}, "t": 173, "s": [368.194, 198.603, 0], "to": [0, 0.068, 0], "ti": [0, -0.084, 0]}, {"i": {"x": 0.833, "y": 0.811}, "o": {"x": 0.167, "y": 0.151}, "t": 174, "s": [368.194, 198.827, 0], "to": [0, 0.084, 0], "ti": [0, -0.105, 0]}, {"i": {"x": 0.833, "y": 0.809}, "o": {"x": 0.167, "y": 0.149}, "t": 175, "s": [368.194, 199.104, 0], "to": [0, 0.105, 0], "ti": [0, -0.134, 0]}, {"i": {"x": 0.833, "y": 0.805}, "o": {"x": 0.167, "y": 0.148}, "t": 176, "s": [368.194, 199.454, 0], "to": [0, 0.134, 0], "ti": [0, -0.178, 0]}, {"i": {"x": 0.833, "y": 0.8}, "o": {"x": 0.167, "y": 0.145}, "t": 177, "s": [368.194, 199.909, 0], "to": [0, 0.178, 0], "ti": [0, -0.244, 0]}, {"i": {"x": 0.833, "y": 0.815}, "o": {"x": 0.167, "y": 0.143}, "t": 178, "s": [368.194, 200.52, 0], "to": [0, 0.244, 0], "ti": [0, -0.315, 0]}, {"i": {"x": 0.833, "y": 0.858}, "o": {"x": 0.167, "y": 0.152}, "t": 179, "s": [368.194, 201.373, 0], "to": [0, 0.315, 0], "ti": [0, -0.295, 0]}, {"i": {"x": 0.833, "y": 0.875}, "o": {"x": 0.167, "y": 0.201}, "t": 180, "s": [368.194, 202.41, 0], "to": [0, 0.295, 0], "ti": [0, -0.184, 0]}, {"i": {"x": 0.833, "y": 0.882}, "o": {"x": 0.167, "y": 0.251}, "t": 181, "s": [368.194, 203.146, 0], "to": [0, 0.184, 0], "ti": [0, -0.087, 0]}, {"i": {"x": 0.833, "y": 0.896}, "o": {"x": 0.167, "y": 0.281}, "t": 182, "s": [368.194, 203.512, 0], "to": [0, 0.087, 0], "ti": [0, -0.032, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.411}, "t": 183, "s": [368.194, 203.667, 0], "to": [0, 0.032, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.589}, "o": {"x": 0.167, "y": 0.167}, "t": 184, "s": [368.194, 203.706, 0], "to": [0, 0, 0], "ti": [0, 0.032, 0]}, {"i": {"x": 0.833, "y": 0.719}, "o": {"x": 0.167, "y": 0.104}, "t": 185, "s": [368.194, 203.667, 0], "to": [0, -0.032, 0], "ti": [0, 0.087, 0]}, {"i": {"x": 0.833, "y": 0.749}, "o": {"x": 0.167, "y": 0.118}, "t": 186, "s": [368.194, 203.512, 0], "to": [0, -0.087, 0], "ti": [0, 0.184, 0]}, {"i": {"x": 0.833, "y": 0.799}, "o": {"x": 0.167, "y": 0.125}, "t": 187, "s": [368.194, 203.146, 0], "to": [0, -0.184, 0], "ti": [0, 0.295, 0]}, {"i": {"x": 0.833, "y": 0.848}, "o": {"x": 0.167, "y": 0.142}, "t": 188, "s": [368.194, 202.41, 0], "to": [0, -0.295, 0], "ti": [0, 0.315, 0]}, {"i": {"x": 0.833, "y": 0.857}, "o": {"x": 0.167, "y": 0.185}, "t": 189, "s": [368.194, 201.373, 0], "to": [0, -0.315, 0], "ti": [0, 0.244, 0]}, {"i": {"x": 0.833, "y": 0.855}, "o": {"x": 0.167, "y": 0.2}, "t": 190, "s": [368.194, 200.52, 0], "to": [0, -0.244, 0], "ti": [0, 0.178, 0]}, {"i": {"x": 0.833, "y": 0.852}, "o": {"x": 0.167, "y": 0.195}, "t": 191, "s": [368.194, 199.909, 0], "to": [0, -0.178, 0], "ti": [0, 0.134, 0]}, {"i": {"x": 0.833, "y": 0.851}, "o": {"x": 0.167, "y": 0.191}, "t": 192, "s": [368.194, 199.454, 0], "to": [0, -0.134, 0], "ti": [0, 0.105, 0]}, {"i": {"x": 0.833, "y": 0.849}, "o": {"x": 0.167, "y": 0.189}, "t": 193, "s": [368.194, 199.104, 0], "to": [0, -0.105, 0], "ti": [0, 0.084, 0]}, {"i": {"x": 0.833, "y": 0.848}, "o": {"x": 0.167, "y": 0.186}, "t": 194, "s": [368.194, 198.827, 0], "to": [0, -0.084, 0], "ti": [0, 0.068, 0]}, {"i": {"x": 0.833, "y": 0.847}, "o": {"x": 0.167, "y": 0.184}, "t": 195, "s": [368.194, 198.603, 0], "to": [0, -0.068, 0], "ti": [0, 0.056, 0]}, {"i": {"x": 0.833, "y": 0.847}, "o": {"x": 0.167, "y": 0.184}, "t": 196, "s": [368.194, 198.418, 0], "to": [0, -0.056, 0], "ti": [0, 0.047, 0]}, {"i": {"x": 0.833, "y": 0.848}, "o": {"x": 0.167, "y": 0.183}, "t": 197, "s": [368.194, 198.264, 0], "to": [0, -0.047, 0], "ti": [0, 0.039, 0]}, {"i": {"x": 0.833, "y": 0.848}, "o": {"x": 0.167, "y": 0.184}, "t": 198, "s": [368.194, 198.136, 0], "to": [0, -0.039, 0], "ti": [0, 0.032, 0]}, {"i": {"x": 0.833, "y": 0.849}, "o": {"x": 0.167, "y": 0.185}, "t": 199, "s": [368.194, 198.03, 0], "to": [0, -0.032, 0], "ti": [0, 0.026, 0]}, {"i": {"x": 0.833, "y": 0.851}, "o": {"x": 0.167, "y": 0.186}, "t": 200, "s": [368.194, 197.943, 0], "to": [0, -0.026, 0], "ti": [0, 0.021, 0]}, {"i": {"x": 0.833, "y": 0.853}, "o": {"x": 0.167, "y": 0.189}, "t": 201, "s": [368.194, 197.872, 0], "to": [0, -0.021, 0], "ti": [0, 0.016, 0]}, {"i": {"x": 0.833, "y": 0.856}, "o": {"x": 0.167, "y": 0.192}, "t": 202, "s": [368.194, 197.817, 0], "to": [0, -0.016, 0], "ti": [0, 0.012, 0]}, {"i": {"x": 0.833, "y": 0.861}, "o": {"x": 0.167, "y": 0.197}, "t": 203, "s": [368.194, 197.774, 0], "to": [0, -0.012, 0], "ti": [0, 0.009, 0]}, {"i": {"x": 0.833, "y": 0.869}, "o": {"x": 0.167, "y": 0.207}, "t": 204, "s": [368.194, 197.743, 0], "to": [0, -0.009, 0], "ti": [0, 0.005, 0]}, {"i": {"x": 0.833, "y": 0.89}, "o": {"x": 0.167, "y": 0.23}, "t": 205, "s": [368.194, 197.722, 0], "to": [0, -0.005, 0], "ti": [0, 0.003, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.345}, "t": 206, "s": [368.194, 197.71, 0], "to": [0, -0.003, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.655}, "o": {"x": 0.167, "y": 0.167}, "t": 207, "s": [368.194, 197.706, 0], "to": [0, 0, 0], "ti": [0, -0.003, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.11}, "t": 208, "s": [368.194, 197.71, 0], "to": [0, 0.003, 0], "ti": [0, -0.002, 0]}, {"t": 209.000008512745, "s": [368.194, 197.722, 0]}], "ix": 2}, "a": {"a": 0, "k": [13.766, 24.068, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-0.178, -0.103], [-0.125, -0.244], [0, 0], [0.518, 0.299], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.334, 0.271], [0, 0]], "o": [[0.178, 0.103], [0, 0], [0.325, 0.651], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.524, -0.303], [0, 0], [0.125, -0.1]], "v": [[0.05, -22.691], [0.528, -22.155], [11.519, -0.273], [11.037, 0.603], [7.979, -1.161], [7.911, 22.809], [-8.145, 13.538], [-8.077, -10.432], [-11.033, -12.139], [-11.511, -13.57], [-0.43, -22.708]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0, 0.8, 0.255000005984, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [12.094, 25.078], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0.01], [0, 0], [0.001, 0.01], [0.001, 0.01], [0.001, 0.01], [0.002, 0.01], [0.002, 0.01], [0.002, 0.01], [0, 0.002], [0, 0.001], [0.002, 0.007], [0.003, 0.01], [0, 0], [0.004, 0.01], [0.004, 0.01], [0.004, 0.011], [0.005, 0.01], [0.003, 0.011], [0.005, 0.01], [0.005, 0.01], [0, 0], [0, 0], [0.005, 0.009], [0.01, 0.017], [0.019, 0.028], [0.013, 0.016], [0.009, 0.011], [0.008, 0.01], [0.008, 0.008], [0.004, 0.005], [0, 0], [0.004, 0.003], [0.009, 0.009], [0.009, 0.008], [0.009, 0.008], [0.01, 0.007], [0.009, 0.007], [0.009, 0.007], [0.01, 0.006], [0.01, 0.005], [0.009, 0.005], [0.002, 0.001], [0.007, 0.003], [0.008, 0.004], [0.009, 0.004], [0.008, 0.003], [0.009, 0.003], [0.009, 0.003], [0.007, 0.002], [0.009, 0.002], [0.01, 0.001], [0.004, 0], [0, 0], [0.005, 0], [0.01, 0], [0.01, 0], [0.012, -0.002], [0.014, -0.005], [0.017, -0.01], [0, 0], [-0.075, -0.007], [0, 0], [-0.08, -0.046], [-0.082, -0.091], [0, 0], [-0.063, -0.122], [0, 0], [-0.022, -0.097], [0, 0], [0.006, -0.081], [0, 0], [0.03, -0.058], [0.001, -0.001], [0.051, -0.03], [0, 0], [-0.029, 0.044], [-0.004, 0.01], [-0.001, 0.002], [0, 0], [-0.005, 0.014], [0, 0], [-0.002, 0.011], [-0.002, 0.01], [-0.001, 0.011], [-0.001, 0.009], [-0.001, 0.002], [0, 0], [0, 0.008]], "o": [[0, 0], [-0.001, -0.009], [-0.001, -0.009], [-0.001, -0.009], [-0.002, -0.01], [-0.002, -0.01], [-0.002, -0.01], [0, -0.002], [0, 0], [-0.002, -0.007], [-0.003, -0.011], [0, 0], [-0.004, -0.011], [-0.003, -0.01], [-0.004, -0.01], [-0.004, -0.01], [-0.004, -0.011], [-0.005, -0.01], [-0.005, -0.011], [0, 0], [0, 0], [-0.005, -0.009], [-0.009, -0.018], [-0.017, -0.03], [-0.011, -0.016], [-0.008, -0.012], [-0.009, -0.011], [-0.007, -0.009], [-0.004, -0.005], [0, 0], [-0.004, -0.003], [-0.008, -0.009], [-0.009, -0.009], [-0.009, -0.009], [-0.009, -0.008], [-0.009, -0.008], [-0.01, -0.008], [-0.009, -0.007], [-0.009, -0.007], [-0.009, -0.005], [-0.002, -0.001], [-0.007, -0.004], [-0.008, -0.005], [-0.008, -0.005], [-0.008, -0.003], [-0.009, -0.003], [-0.008, -0.003], [-0.009, -0.002], [-0.009, -0.002], [-0.008, -0.002], [-0.003, 0], [0, 0], [-0.006, -0.001], [-0.01, -0.001], [-0.01, 0], [-0.013, 0.001], [-0.015, 0.003], [-0.018, 0.007], [0, 0], [0.061, -0.035], [0, 0], [0.075, 0.009], [0.089, 0.052], [0, 0], [0.082, 0.09], [0, 0], [0.054, 0.107], [0, 0], [0.022, 0.097], [0, 0], [-0.006, 0.08], [-0.001, 0.001], [-0.03, 0.058], [0, 0], [0.043, -0.024], [0.006, -0.009], [0.001, -0.001], [0, 0], [0.006, -0.012], [0, 0], [0.003, -0.011], [0.003, -0.01], [0.001, -0.01], [0.002, -0.009], [0, 0], [0, 0], [0, -0.008], [0.001, -0.01]], "v": [[7.791, 10.287], [7.791, 10.258], [7.788, 10.229], [7.786, 10.201], [7.783, 10.171], [7.778, 10.141], [7.773, 10.111], [7.768, 10.079], [7.766, 10.072], [7.765, 10.07], [7.76, 10.048], [7.752, 10.016], [7.743, 9.984], [7.731, 9.952], [7.722, 9.921], [7.709, 9.891], [7.696, 9.861], [7.684, 9.828], [7.67, 9.797], [7.653, 9.766], [7.653, 9.764], [-3.338, -12.117], [-3.352, -12.144], [-3.381, -12.197], [-3.434, -12.283], [-3.471, -12.332], [-3.495, -12.366], [-3.52, -12.396], [-3.544, -12.423], [-3.557, -12.438], [-3.558, -12.44], [-3.567, -12.45], [-3.593, -12.477], [-3.619, -12.503], [-3.648, -12.529], [-3.675, -12.553], [-3.702, -12.576], [-3.731, -12.596], [-3.758, -12.616], [-3.785, -12.634], [-3.811, -12.65], [-3.816, -12.652], [-3.837, -12.664], [-3.861, -12.677], [-3.887, -12.69], [-3.912, -12.7], [-3.938, -12.71], [-3.963, -12.717], [-3.988, -12.725], [-4.015, -12.73], [-4.041, -12.734], [-4.051, -12.736], [-4.053, -12.736], [-4.068, -12.738], [-4.098, -12.738], [-4.13, -12.738], [-4.165, -12.733], [-4.208, -12.721], [-4.26, -12.697], [-7.791, -10.646], [-7.584, -10.685], [-7.583, -10.685], [-7.347, -10.602], [-7.089, -10.387], [-7.088, -10.385], [-6.868, -10.066], [4.122, 11.815], [4.234, 12.122], [4.234, 12.124], [4.257, 12.391], [4.257, 12.392], [4.201, 12.6], [4.2, 12.604], [4.076, 12.738], [7.606, 10.685], [7.714, 10.582], [7.73, 10.553], [7.732, 10.548], [7.739, 10.538], [7.755, 10.499], [7.766, 10.467], [7.773, 10.434], [7.781, 10.404], [7.785, 10.374], [7.787, 10.345], [7.788, 10.34], [7.788, 10.339], [7.79, 10.316]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.380000005984, 0.877999997606, 0.255000005984, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [19.491, 12.989], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 3, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-1.732, -10.959], [1.799, -13.011], [1.731, 10.959], [-1.799, 13.011]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0, 0.663000009574, 0.20800000359, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [21.805, 34.875], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 2, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 94.0000038286985, "st": 0, "bm": 0}, {"ddd": 0, "ind": 13, "ty": 4, "nm": "Layer 15 Outlines", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [290.879, 109.807, 0], "ix": 2}, "a": {"a": 0, "k": [14.206, 16.102, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -0.361], [0, 0], [0.672, 0.386], [0, 0], [0, 0.361], [0, 0], [-0.672, -0.385], [0, 0]], "o": [[0, 0], [0, 0.774], [0, 0], [-0.313, -0.179], [0, 0], [0, -0.775], [0, 0], [0.313, 0.18]], "v": [[11.947, 5.612], [11.948, 6.93], [10.439, 7.804], [-11.441, -4.739], [-11.947, -5.612], [-11.948, -6.93], [-10.439, -7.805], [11.441, 4.738]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 7, "s": [50]}, {"t": 41.0000016699642, "s": [0]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 7, "s": [50]}, {"t": 41.0000016699642, "s": [100]}], "ix": 2}, "o": {"a": 0, "k": 7, "ix": 3}, "m": 1, "ix": 3, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.109999997008, 0.898000021542, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [12.2, 21.534], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 4, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -0.361], [0, 0], [0.672, 0.385], [0, 0], [0, 0.361], [0, 0], [-0.67, -0.385], [0, 0]], "o": [[0, 0], [0.001, 0.774], [0, 0], [-0.313, -0.179], [0, 0], [-0.001, -0.775], [0, 0], [0.313, 0.18]], "v": [[8.616, 3.703], [8.616, 5.021], [7.108, 5.895], [-8.111, -2.829], [-8.617, -3.703], [-8.617, -5.021], [-7.11, -5.896], [8.11, 2.829]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.109999997008, 0.898000021542, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [50]}, {"t": 27.0000010997325, "s": [0]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [50]}, {"t": 27.0000010997325, "s": [100]}], "ix": 2}, "o": {"a": 0, "k": 7, "ix": 3}, "m": 1, "ix": 4, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [8.872, 26.173], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 4, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -0.361], [0, 0], [0.672, 0.386], [0, 0], [0, 0.361], [0, 0], [-0.672, -0.385], [0, 0]], "o": [[0, 0], [0, 0.774], [0, 0], [-0.313, -0.18], [0, 0], [0, -0.775], [0, 0], [0.314, 0.18]], "v": [[13.956, 6.763], [13.956, 8.081], [12.447, 8.955], [-13.449, -5.889], [-13.954, -6.763], [-13.956, -8.081], [-12.447, -8.956], [13.448, 5.889]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.109999997008, 0.898000021542, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 16, "s": [50]}, {"t": 45.0000018328876, "s": [0]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 16, "s": [50]}, {"t": 45.0000018328876, "s": [100]}], "ix": 2}, "o": {"a": 0, "k": 7, "ix": 3}, "m": 1, "ix": 4, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [14.206, 16.136], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 4, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -0.36], [0, 0], [0.672, 0.387], [0, 0], [0, 0.361], [0, 0], [-0.672, -0.387], [0, 0]], "o": [[0, 0], [0, 0.776], [0, 0], [-0.312, -0.18], [0, 0], [0, -0.775], [0, 0], [0.313, 0.18]], "v": [[4.115, 1.132], [4.115, 2.447], [2.604, 3.321], [-3.611, -0.26], [-4.115, -1.132], [-4.115, -2.447], [-2.605, -3.32], [3.61, 0.26]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.109999997008, 0.898000021542, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 29, "s": [50]}, {"t": 38.0000015477717, "s": [0]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 29, "s": [50]}, {"t": 38.0000015477717, "s": [100]}], "ix": 2}, "o": {"a": 0, "k": 7, "ix": 3}, "m": 1, "ix": 4, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [4.365, 3.958], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 4", "np": 4, "cix": 2, "bm": 0, "ix": 4, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 94.0000038286985, "st": 0, "bm": 0}, {"ddd": 0, "ind": 14, "ty": 4, "nm": "Layer 16 Outlines", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [337.146, 145.715, 0], "ix": 2}, "a": {"a": 0, "k": [75.187, 93.782, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[1.907, 1.102], [0.007, -2.192], [-1.908, -1.102], [-0.005, 2.192]], "o": [[-1.907, -1.101], [-0.006, 2.192], [1.907, 1.102], [0.006, -2.192]], "v": [[0.016, -3.971], [-3.455, -1.992], [-0.006, 3.969], [3.455, 1.996]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.128999986836, 0.161000001197, 0.451000019148, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [131.299, 101.428], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[4.22, 2.436], [0.014, -4.848], [-4.219, -2.436], [-0.013, 4.849]], "o": [[-4.219, -2.436], [-0.013, 4.849], [4.22, 2.436], [0.015, -4.848]], "v": [[0.034, -8.781], [-7.643, -4.407], [-0.016, 8.781], [7.641, 4.417]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.258999992819, 0.447000002394, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [131.3, 101.427], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 3, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [131.299, 101.428], "ix": 2}, "a": {"a": 0, "k": [131.299, 101.428], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0, 0], "y": [1, 1]}, "o": {"x": [0.333, 0.333], "y": [0, 0]}, "t": 72, "s": [0, 0]}, {"t": 83.0000033806593, "s": [100, 100]}], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 14", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.001, -0.202], [0, 0], [0.376, 0.217], [0, 0], [-0.001, 0.202], [0, 0], [-0.375, -0.217], [0, 0]], "o": [[0, 0], [-0.001, 0.434], [0, 0], [-0.175, -0.101], [0, 0], [0.001, -0.434], [0, 0], [0.175, 0.101]], "v": [[6.685, 3.165], [6.683, 3.904], [5.835, 4.391], [-6.402, -2.675], [-6.684, -3.165], [-6.682, -3.904], [-5.837, -4.391], [6.404, 2.675]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.344999994016, 0.685999971278, 0.984000052658, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 14, "s": [50]}, {"t": 48.0000019550801, "s": [0]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 14, "s": [50]}, {"t": 48.0000019550801, "s": [100]}], "ix": 2}, "o": {"a": 0, "k": 7, "ix": 3}, "m": 1, "ix": 4, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [89.98, 56.781], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 4, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -0.202], [0, 0], [0.375, 0.217], [0, 0], [0, 0.202], [0, 0], [-0.376, -0.217], [0, 0]], "o": [[0, 0], [-0.002, 0.434], [0, 0], [-0.175, -0.101], [0, 0], [0.002, -0.434], [0, 0], [0.175, 0.101]], "v": [[4.823, 2.089], [4.821, 2.829], [3.974, 3.316], [-4.539, -1.6], [-4.822, -2.089], [-4.82, -2.829], [-3.972, -3.316], [4.541, 1.6]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 41, "s": [50]}, {"t": 75.0000030548126, "s": [0]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 41, "s": [50]}, {"t": 75.0000030548126, "s": [100]}], "ix": 2}, "o": {"a": 0, "k": 7, "ix": 3}, "m": 1, "ix": 4, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [88.107, 59.375], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 4", "np": 4, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.001, -0.202], [0, 0], [0.376, 0.217], [0, 0], [-0.001, 0.202], [0, 0], [-0.376, -0.217], [0, 0]], "o": [[0, 0], [-0.001, 0.433], [0, 0], [-0.176, -0.101], [0, 0], [0.001, -0.434], [0, 0], [0.175, 0.102]], "v": [[7.808, 3.813], [7.806, 4.552], [6.958, 5.039], [-7.525, -3.324], [-7.807, -3.814], [-7.805, -4.553], [-6.96, -5.04], [7.526, 3.322]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 41, "s": [50]}, {"t": 75.0000030548126, "s": [0]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 41, "s": [50]}, {"t": 75.0000030548126, "s": [100]}], "ix": 2}, "o": {"a": 0, "k": 7, "ix": 3}, "m": 1, "ix": 4, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [91.114, 53.76], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 5", "np": 4, "cix": 2, "bm": 0, "ix": 4, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -0.202], [0, 0], [0.376, 0.218], [0, 0], [0, 0.202], [0, 0], [-0.376, -0.219], [0, 0]], "o": [[0, 0], [-0.001, 0.435], [0, 0], [-0.175, -0.101], [0, 0], [0.002, -0.435], [0, 0], [0.174, 0.102]], "v": [[2.304, 0.641], [2.301, 1.379], [1.453, 1.866], [-2.023, -0.152], [-2.304, -0.642], [-2.302, -1.379], [-1.454, -1.865], [2.023, 0.151]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 41, "s": [50]}, {"t": 75.0000030548126, "s": [0]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 41, "s": [50]}, {"t": 75.0000030548126, "s": [100]}], "ix": 2}, "o": {"a": 0, "k": 7, "ix": 3}, "m": 1, "ix": 4, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [85.62, 46.919], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 6", "np": 4, "cix": 2, "bm": 0, "ix": 5, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.163, 0.155], [0, 0], [-0.161, -0.167], [1.363, -3.173], [0.057, -0.107], [0.023, -0.038], [2.11, 0.847], [0.339, 0.195], [0.09, 0.056], [0.124, 0.084], [0, 0], [0.271, 0.232], [0.036, 0.03], [0.099, 0.093], [0.036, 0.037], [0.128, 0.131], [0.195, 0.225], [0, 0], [-0.206, -0.21], [-0.186, -0.175], [-0.054, -0.049], [-0.143, -0.125], [-0.048, -0.04], [-0.385, -0.269], [-0.031, -0.02], [-0.174, -0.111], [0, 0], [-0.119, -0.069], [-0.282, -0.134], [-0.2, -0.08], [-1.288, 3], [4.871, 5.02]], "o": [[0, 0], [0.163, 0.152], [3.436, 3.54], [-0.051, 0.113], [-0.02, 0.038], [-0.994, 1.749], [-0.329, -0.131], [-0.09, -0.052], [-0.165, -0.103], [0, 0], [-0.272, -0.192], [-0.033, -0.028], [-0.099, -0.087], [-0.037, -0.034], [-0.131, -0.122], [-0.208, -0.215], [0, 0], [0.197, 0.221], [0.185, 0.189], [0.052, 0.05], [0.141, 0.131], [0.048, 0.043], [0.383, 0.326], [0.03, 0.023], [0.176, 0.124], [0, 0], [0.119, 0.075], [0.284, 0.164], [0.212, 0.101], [3.22, 1.277], [1.934, -4.499], [-0.163, -0.166]], "v": [[-0.309, -10.763], [-1.344, -8.354], [-0.857, -7.876], [2.894, 4.275], [2.734, 4.604], [2.67, 4.717], [-2.337, 6.048], [-3.34, 5.557], [-3.611, 5.395], [-4.027, 5.122], [-4.081, 5.082], [-4.894, 4.452], [-4.997, 4.362], [-5.295, 4.092], [-5.406, 3.988], [-5.794, 3.608], [-6.396, 2.948], [-7.432, 5.358], [-6.83, 6.009], [-6.274, 6.556], [-6.116, 6.704], [-5.689, 7.089], [-5.545, 7.214], [-4.391, 8.109], [-4.3, 8.173], [-3.775, 8.521], [-3.714, 8.557], [-3.356, 8.772], [-2.507, 9.22], [-1.895, 9.486], [5.497, 6.957], [0.178, -10.281]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 19, "s": [50]}, {"t": 47.0000019143492, "s": [0]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 19, "s": [50]}, {"t": 47.0000019143492, "s": [100]}], "ix": 2}, "o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 27, "s": [4664]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 34, "s": [4675]}, {"t": 42.0000017106951, "s": [4688]}], "ix": 3}, "m": 1, "ix": 3, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.991999966491, 0.725, 0.075, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [73.425, 45.041], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 7", "np": 4, "cix": 2, "bm": 0, "ix": 6, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.992, 0.573], [1.481, -3.446], [-4.462, -5.02], [0, 0], [-1.283, 2.98], [-2.67, -1.541], [-0.648, -0.604], [0, 0]], "o": [[-3.787, -2.187], [-1.853, 4.309], [0, 0], [-3.022, -3.527], [1.045, -2.429], [0.651, 0.376], [0, 0], [-0.985, -0.948]], "v": [[3.862, -8.108], [-4.992, -6.291], [-0.276, 10.295], [0.758, 7.885], [-2.388, -3.606], [3.853, -4.886], [5.81, -3.416], [6.845, -5.825]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.944999964097, 0.944999964097, 0.944999964097, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 47, "s": [100]}, {"t": 75.0000030548126, "s": [100]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 47, "s": [100]}, {"t": 75.0000030548126, "s": [0]}], "ix": 2}, "o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 50, "s": [-15]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 54, "s": [16]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 58, "s": [52]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 63, "s": [92]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 68, "s": [132]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 69, "s": [132.4]}, {"t": 73.000002973351, "s": [189]}], "ix": 3}, "m": 1, "ix": 4, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [66.27, 40.104], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 8", "np": 4, "cix": 2, "bm": 0, "ix": 7, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-67.331, -11.791], [-57.354, -16.879], [-50.828, -6.338], [-43.55, 1.066], [-36.27, -3.326], [-20.709, -8.346], [-13.682, 1.694], [-6.654, 7.466], [-0.254, 16.879], [8.405, -1.318], [18.947, 3.702], [24.469, -0.816], [35.89, 2.196], [52.831, 0.69]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.165], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [0]}, {"t": 82.0000033399285, "s": [100]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.086000001197, 0.773000021542, 0.862999949736, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 1, "ix": 5}, "lc": 1, "lj": 1, "ml": 10, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [71.218, 101.223], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 9", "np": 3, "cix": 2, "bm": 0, "ix": 8, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.002, -0.35], [0, 0], [0.65, 0.375], [0, 0], [-0.001, 0.35], [0, 0], [-0.65, -0.376], [0, 0]], "o": [[0, 0], [-0.002, 0.75], [0, 0], [-0.304, -0.175], [0, 0], [0.002, -0.75], [0, 0], [0.302, 0.175]], "v": [[13.508, 6.598], [13.505, 7.877], [12.04, 8.719], [-13.02, -5.749], [-13.509, -6.598], [-13.506, -7.876], [-12.04, -8.718], [13.021, 5.749]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.102000000898, 0.898000021542, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 37, "s": [50]}, {"t": 71.0000028918893, "s": [0]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 37, "s": [50]}, {"t": 71.0000028918893, "s": [100]}], "ix": 2}, "o": {"a": 0, "k": 7, "ix": 3}, "m": 1, "ix": 4, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [28.187, 41.074], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 10", "np": 4, "cix": 2, "bm": 0, "ix": 9, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.001, -0.35], [0, 0], [0.65, 0.377], [0, 0], [-0.001, 0.349], [0, 0], [-0.65, -0.378], [0, 0]], "o": [[0, 0], [-0.003, 0.751], [0, 0], [-0.302, -0.175], [0, 0], [0.002, -0.751], [0, 0], [0.301, 0.175]], "v": [[3.986, 1.11], [3.983, 2.386], [2.516, 3.227], [-3.499, -0.262], [-3.986, -1.109], [-3.982, -2.385], [-2.515, -3.226], [3.5, 0.262]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 53, "s": [50]}, {"t": 87.0000035435826, "s": [0]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 53, "s": [50]}, {"t": 87.0000035435826, "s": [100]}], "ix": 2}, "o": {"a": 0, "k": 7, "ix": 3}, "m": 1, "ix": 4, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [18.682, 29.238], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 11", "np": 4, "cix": 2, "bm": 0, "ix": 10, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0.004, -1.41], [0, 0], [1.227, 0.708], [0, 0], [-0.004, 1.409], [0, 0], [-1.227, -0.708]], "o": [[1.228, 0.708], [0, 0], [-0.004, 1.409], [0, 0], [-1.227, -0.708], [0, 0], [0.005, -1.409], [0, 0]], "v": [[20.604, -4.728], [22.816, -0.895], [22.737, 27.196], [20.509, 28.465], [-20.603, 4.728], [-22.816, 0.896], [-22.737, -27.196], [-20.509, -28.465]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.149000010771, 0.536999990426, 0.991999966491, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [79.895, 47.542], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 12", "np": 3, "cix": 2, "bm": 0, "ix": 11, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0.013, -4.484], [0, 0], [3.903, 2.253], [0, 0], [-0.012, 4.484], [0, 0], [-3.902, -2.253]], "o": [[3.902, 2.253], [0, 0], [-0.013, 4.484], [0, 0], [-3.903, -2.253], [0, 0], [0.014, -4.485], [0, 0]], "v": [[65.55, -15.042], [72.589, -2.849], [72.336, 86.522], [65.25, 90.56], [-65.55, 15.042], [-72.59, 2.849], [-72.337, -86.521], [-65.251, -90.56]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.204000001795, 0.224000010771, 0.588000009574, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [72.852, 94.501], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 13", "np": 3, "cix": 2, "bm": 0, "ix": 12, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 94.0000038286985, "st": 0, "bm": 0}, {"ddd": 1, "ind": 15, "ty": 4, "nm": "Layer 17 Outlines", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "rx": {"a": 0, "k": 0, "ix": 8}, "ry": {"a": 0, "k": 0, "ix": 9}, "rz": {"a": 0, "k": 0, "ix": 10}, "or": {"a": 0, "k": [0, 0, 0], "ix": 7}, "p": {"a": 0, "k": [252.735, 207.409, -11], "ix": 2}, "a": {"a": 0, "k": [31.286, 19.555, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-6.128, 1.155], [0, 0], [7.838, 4.526], [0, 0]], "o": [[0, 0], [-9.808, 1.884], [0, 0], [4.891, 2.824]], "v": [[11, -2.577], [14.452, 3.193], [-14.452, -0.764], [-7.033, -5.077]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.702000038297, 0.569000004787, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [25.828, 33.048], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.407, -0.066], [0.286, -0.041], [0.334, -0.041], [0.286, -0.03], [0.532, -0.037], [0.048, -0.003], [0.592, -0.018], [0.15, -0.003], [0.437, 0.003], [0.181, 0.003], [0.481, 0.023], [0.092, 0.004], [0.565, 0.049], [0.148, 0.014], [0.434, 0.054], [0.156, 0.021], [0.556, 0.096], [0.004, 0.001], [0.542, 0.116], [0.192, 0.044], [0.367, 0.094], [0.199, 0.054], [0.313, 0.094], [0.283, 0.092], [0.285, 0.101], [0.274, 0.105], [0.405, 0.174], [0.04, 0.017], [0.456, 0.225], [0.109, 0.055], [0.436, 0.252], [0, 0], [-0.457, -0.232], [-0.11, -0.054], [-0.474, -0.205], [-0.04, -0.017], [-0.306, -0.12], [-0.108, -0.042], [-0.28, -0.099], [-0.289, -0.093], [-0.289, -0.086], [-0.177, -0.05], [-0.139, -0.038], [-0.202, -0.052], [-0.372, -0.085], [-0.193, -0.041], [-0.084, -0.018], [-0.466, -0.081], [0, 0], [-0.463, -0.065], [-0.096, -0.014], [-0.157, -0.019], [-0.438, -0.042], [-0.149, -0.013], [-0.076, -0.007], [-0.49, -0.026], [-0.093, -0.004], [-0.344, -0.008], [-0.136, -0.002], [-0.18, -0.001], [-0.436, 0.009], [-0.15, 0.004], [-0.061, 0.002], [-0.529, 0.035], [-0.048, 0.003], [-0.465, 0.046], [-0.062, 0.007], [-0.285, 0.034], [-0.332, 0.048], [-0.283, 0.046], [-0.242, 0.044], [-0.161, 0.03], [0, 0]], "o": [[-0.283, 0.046], [-0.331, 0.048], [-0.284, 0.034], [-0.528, 0.055], [-0.048, 0.003], [-0.588, 0.039], [-0.15, 0.004], [-0.436, 0.01], [-0.18, -0.001], [-0.482, -0.008], [-0.092, -0.004], [-0.568, -0.03], [-0.149, -0.013], [-0.438, -0.042], [-0.157, -0.02], [-0.563, -0.076], [-0.003, -0.001], [-0.552, -0.096], [-0.194, -0.041], [-0.372, -0.084], [-0.201, -0.052], [-0.318, -0.087], [-0.287, -0.086], [-0.289, -0.095], [-0.278, -0.098], [-0.417, -0.16], [-0.04, -0.017], [-0.474, -0.205], [-0.11, -0.055], [-0.456, -0.232], [0, 0], [0.436, 0.252], [0.109, 0.055], [0.457, 0.225], [0.039, 0.017], [0.3, 0.128], [0.108, 0.043], [0.276, 0.105], [0.283, 0.1], [0.284, 0.093], [0.175, 0.053], [0.138, 0.04], [0.201, 0.054], [0.367, 0.095], [0.193, 0.043], [0.085, 0.018], [0.46, 0.095], [0, 0], [0.458, 0.079], [0.097, 0.014], [0.157, 0.021], [0.435, 0.054], [0.149, 0.014], [0.076, 0.007], [0.489, 0.04], [0.094, 0.005], [0.343, 0.016], [0.137, 0.004], [0.182, 0.003], [0.437, 0.002], [0.15, -0.004], [0.061, -0.002], [0.53, -0.018], [0.048, -0.004], [0.468, -0.033], [0.063, -0.007], [0.286, -0.029], [0.334, -0.041], [0.285, -0.041], [0.244, -0.04], [0.162, -0.029], [0, 0], [-0.403, 0.078]], "v": [[13.24, 0.878], [12.389, 1.014], [11.39, 1.142], [10.537, 1.243], [8.947, 1.382], [8.804, 1.394], [7.034, 1.478], [6.583, 1.486], [5.274, 1.497], [4.732, 1.491], [3.287, 1.446], [3.011, 1.436], [1.312, 1.316], [0.866, 1.273], [-0.442, 1.128], [-0.913, 1.068], [-2.593, 0.813], [-2.604, 0.811], [-4.244, 0.49], [-4.822, 0.359], [-5.931, 0.092], [-6.531, -0.067], [-7.476, -0.341], [-8.333, -0.607], [-9.192, -0.902], [-10.023, -1.205], [-11.259, -1.701], [-11.381, -1.751], [-12.774, -2.399], [-13.103, -2.564], [-14.446, -3.286], [-14.458, -1.499], [-13.115, -0.777], [-12.786, -0.612], [-11.392, 0.036], [-11.272, 0.086], [-10.365, 0.461], [-10.036, 0.582], [-9.202, 0.886], [-8.346, 1.179], [-7.486, 1.446], [-6.963, 1.609], [-6.545, 1.72], [-5.942, 1.879], [-4.835, 2.147], [-4.257, 2.277], [-4.005, 2.334], [-2.617, 2.598], [-2.599, 2.601], [-1.216, 2.818], [-0.926, 2.856], [-0.454, 2.915], [0.854, 3.06], [1.3, 3.103], [1.529, 3.126], [2.998, 3.223], [3.278, 3.234], [4.309, 3.274], [4.719, 3.278], [5.262, 3.284], [6.571, 3.274], [7.022, 3.265], [7.204, 3.261], [8.793, 3.181], [8.935, 3.169], [10.336, 3.053], [10.524, 3.03], [11.379, 2.929], [12.378, 2.801], [13.228, 2.665], [13.961, 2.548], [14.446, 2.458], [14.458, 0.67]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.475, 0.455000005984, 0.961000031116, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [25.822, 35.575], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 3, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [3.952, -0.758], [0, 0], [-1.99, 1.118]], "o": [[-3.162, 1.799], [0, 0], [2.482, -0.465], [0, 0]], "v": [[7.139, 0.239], [-3.687, 4.073], [-7.139, -1.696], [-0.329, -4.073]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.745000023935, 0.238999998803, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [43.967, 33.002], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 3, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-0.188, 0.073], [-0.302, 0.127], [-0.179, 0.078], [-0.311, 0.15], [-0.147, 0.074], [-0.434, 0.247], [0, 0], [0.453, -0.227], [0.149, -0.071], [0.32, -0.141], [0.18, -0.075], [0.308, -0.118], [0.192, -0.069], [0.323, -0.107], [0.205, -0.064], [0.156, -0.047], [0.288, -0.078], [0.161, -0.042], [0.308, -0.074], [0.142, -0.032], [0.456, -0.087], [0, 0], [-0.449, 0.102], [-0.142, 0.033], [-0.305, 0.08], [-0.16, 0.044], [-0.284, 0.085], [-0.155, 0.048], [-0.08, 0.025], [-0.124, 0.041], [-0.317, 0.115]], "o": [[0.31, -0.118], [0.182, -0.075], [0.32, -0.141], [0.15, -0.071], [0.453, -0.227], [0, 0], [-0.432, 0.247], [-0.147, 0.074], [-0.311, 0.15], [-0.178, 0.078], [-0.303, 0.126], [-0.19, 0.073], [-0.316, 0.115], [-0.202, 0.068], [-0.155, 0.048], [-0.283, 0.085], [-0.16, 0.044], [-0.303, 0.08], [-0.142, 0.033], [-0.45, 0.101], [0, 0], [0.456, -0.087], [0.142, -0.031], [0.308, -0.073], [0.161, -0.042], [0.287, -0.079], [0.156, -0.046], [0.079, -0.025], [0.125, -0.04], [0.322, -0.107], [0.191, -0.069]], "v": [[1.224, 0.518], [2.14, 0.15], [2.681, -0.079], [3.627, -0.516], [4.075, -0.731], [5.408, -1.438], [5.419, -2.395], [4.087, -1.688], [3.638, -1.473], [2.693, -1.036], [2.153, -0.807], [1.237, -0.438], [0.666, -0.225], [-0.293, 0.107], [-0.901, 0.308], [-1.368, 0.45], [-2.225, 0.695], [-2.707, 0.825], [-3.625, 1.053], [-4.05, 1.154], [-5.419, 1.438], [-5.419, 2.395], [-4.062, 2.11], [-3.638, 2.01], [-2.718, 1.782], [-2.237, 1.652], [-1.38, 1.406], [-0.913, 1.265], [-0.672, 1.192], [-0.304, 1.064], [0.654, 0.732]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.885999971278, 0.626999978458, 0.161000001197, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [45.687, 35.637], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 4", "np": 3, "cix": 2, "bm": 0, "ix": 4, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [10.996, -6.391], [0.074, -0.042], [0, 0], [-0.073, 0.043], [6.941, 4.007]], "o": [[11.068, 6.39], [-0.073, 0.043], [0, 0], [0.074, -0.042], [6.9, -4.009], [0, 0]], "v": [[-1.78, -11.633], [-1.654, 11.509], [-1.875, 11.633], [-9.342, 7.32], [-9.122, 7.197], [-9.201, -7.32]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.344999994016, 0.685999971278, 0.984000052658, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [52.98, 19.588], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 5", "np": 3, "cix": 2, "bm": 0, "ix": 5, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.009, -0.166], [0.014, -0.142], [0.017, -0.124], [0.027, -0.141], [0.028, -0.124], [0.036, -0.138], [0.038, -0.124], [0.048, -0.137], [0.048, -0.121], [0.064, -0.143], [0.058, -0.118], [0.076, -0.14], [0.066, -0.114], [0.107, -0.166], [0.069, -0.1], [0.139, -0.182], [0.062, -0.078], [0.258, -0.281], [0.068, -0.072], [0.223, -0.21], [0.105, -0.096], [0.139, -0.118], [0.131, -0.107], [0.151, -0.115], [0.144, -0.104], [0.161, -0.111], [0.153, -0.101], [0.173, -0.108], [0.225, -0.131], [0.074, -0.043], [0, 0], [-0.074, 0.043], [-0.115, 0.068], [-0.103, 0.065], [-0.168, 0.109], [-0.147, 0.102], [-0.157, 0.113], [-0.137, 0.106], [-0.145, 0.116], [-0.127, 0.108], [-0.133, 0.119], [-0.054, 0.049], [-0.049, 0.046], [-0.204, 0.214], [-0.067, 0.073], [-0.228, 0.288], [-0.01, 0.013], [-0.051, 0.066], [-0.126, 0.183], [-0.066, 0.101], [-0.097, 0.168], [-0.026, 0.046], [-0.036, 0.069], [-0.07, 0.142], [-0.054, 0.12], [-0.056, 0.142], [-0.024, 0.064], [-0.021, 0.057], [-0.042, 0.137], [-0.033, 0.125], [-0.032, 0.138], [-0.015, 0.075], [-0.01, 0.05], [-0.019, 0.141], [-0.013, 0.124], [-0.008, 0.143], [-0.002, 0.084], [0, 0.083], [0, 0]], "o": [[-0.008, 0.143], [-0.012, 0.124], [-0.02, 0.141], [-0.022, 0.124], [-0.031, 0.137], [-0.034, 0.124], [-0.042, 0.137], [-0.043, 0.121], [-0.057, 0.143], [-0.052, 0.119], [-0.069, 0.143], [-0.062, 0.115], [-0.098, 0.168], [-0.066, 0.101], [-0.126, 0.184], [-0.06, 0.079], [-0.229, 0.289], [-0.067, 0.073], [-0.205, 0.215], [-0.101, 0.096], [-0.132, 0.119], [-0.128, 0.108], [-0.145, 0.117], [-0.138, 0.105], [-0.155, 0.113], [-0.149, 0.102], [-0.168, 0.109], [-0.216, 0.134], [-0.074, 0.042], [0, 0], [0.073, -0.043], [0.115, -0.068], [0.105, -0.064], [0.174, -0.108], [0.152, -0.101], [0.162, -0.111], [0.143, -0.104], [0.151, -0.114], [0.133, -0.107], [0.138, -0.118], [0.054, -0.049], [0.049, -0.046], [0.223, -0.21], [0.069, -0.073], [0.259, -0.282], [0.01, -0.013], [0.052, -0.066], [0.138, -0.182], [0.068, -0.101], [0.107, -0.166], [0.027, -0.046], [0.038, -0.068], [0.076, -0.14], [0.057, -0.119], [0.063, -0.142], [0.026, -0.064], [0.022, -0.058], [0.048, -0.136], [0.039, -0.125], [0.037, -0.137], [0.016, -0.074], [0.01, -0.05], [0.025, -0.141], [0.018, -0.124], [0.014, -0.142], [0.005, -0.083], [0.003, -0.083], [0, 0], [0, 0.166]], "v": [[4.201, -7.457], [4.17, -7.03], [4.123, -6.659], [4.055, -6.234], [3.979, -5.862], [3.878, -5.448], [3.769, -5.077], [3.633, -4.667], [3.495, -4.302], [3.314, -3.874], [3.149, -3.517], [2.929, -3.094], [2.739, -2.75], [2.429, -2.25], [2.232, -1.948], [1.833, -1.4], [1.656, -1.163], [0.923, -0.309], [0.715, -0.092], [0.075, 0.544], [-0.234, 0.832], [-0.641, 1.188], [-1.029, 1.51], [-1.473, 1.858], [-1.894, 2.173], [-2.37, 2.509], [-2.821, 2.814], [-3.334, 3.138], [-3.989, 3.539], [-4.209, 3.663], [-4.222, 7.953], [-4.001, 7.829], [-3.656, 7.625], [-3.348, 7.431], [-2.833, 7.104], [-2.385, 6.801], [-1.904, 6.464], [-1.486, 6.148], [-1.042, 5.802], [-0.653, 5.479], [-0.245, 5.122], [-0.077, 4.977], [0.063, 4.836], [0.702, 4.199], [0.911, 3.983], [1.643, 3.129], [1.675, 3.091], [1.821, 2.892], [2.221, 2.345], [2.417, 2.042], [2.726, 1.54], [2.812, 1.404], [2.917, 1.198], [3.136, 0.775], [3.304, 0.417], [3.482, -0.01], [3.563, -0.202], [3.622, -0.375], [3.757, -0.784], [3.865, -1.159], [3.967, -1.57], [4.02, -1.793], [4.045, -1.944], [4.11, -2.366], [4.157, -2.738], [4.188, -3.164], [4.205, -3.414], [4.209, -3.663], [4.222, -7.953]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.195999998205, 0.470999983245, 0.8, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [55.316, 26.245], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 6", "np": 3, "cix": 2, "bm": 0, "ix": 6, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[3.74, 0.667], [3.728, 3.645], [-3.74, -0.667], [-3.728, -3.645]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.195999998205, 0.470999983245, 0.8, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [47.366, 30.554], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 7", "np": 3, "cix": 2, "bm": 0, "ix": 7, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[3.492, 2.016], [0, 0], [0.008, -2.644], [0, 0]], "o": [[0, 0], [3.492, 2.016], [0, 0], [0.007, -2.644]], "v": [[-2.612, -5.143], [-2.624, -2.164], [2.605, 5.143], [2.617, 0.852]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.195999998205, 0.470999983245, 0.8, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [46.392, 17.41], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 8", "np": 3, "cix": 2, "bm": 0, "ix": 8, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-11.071, -6.392], [0, 0], [6.899, -4.009], [-6.944, -4.009], [0, 0], [-10.996, 6.39]], "o": [[0, 0], [-6.943, -4.009], [-6.899, 4.009], [0, 0], [-11.072, -6.393], [10.996, -6.39]], "v": [[25.475, -8.376], [18.055, -4.063], [-7.01, -4.063], [-6.929, 10.456], [-14.348, 14.768], [-14.479, -8.376]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.337000020345, 0.40800000359, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [25.725, 15.018], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 9", "np": 3, "cix": 2, "bm": 0, "ix": 9, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-5.571, -3.216], [0, 0], [-0.012, 4.214], [0, 0]], "o": [[0, 0], [-5.571, -3.217], [0, 0], [-0.012, 4.214]], "v": [[4.183, 3.679], [4.171, 7.971], [-4.171, -3.681], [-4.159, -7.971]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.8, 0.035000000748, 0.416000007181, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [7.194, 26.105], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 10", "np": 3, "cix": 2, "bm": 0, "ix": 10, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[3.716, -4.302], [3.704, -0.01], [-3.716, 4.302], [-3.703, 0.01]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.8, 0.035000000748, 0.416000007181, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [15.08, 29.775], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 11", "np": 3, "cix": 2, "bm": 0, "ix": 11, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-0.387, 0.151], [-0.051, 0.02], [-0.431, 0.136], [-0.306, 0.083], [-0.237, 0.057], [-0.327, 0.067], [-0.243, 0.043], [-0.1, 0.016], [-0.413, 0.049], [-0.12, 0.013], [-0.482, 0.028], [-0.089, 0.005], [-0.324, 0.006], [-0.325, -0.004], [-0.27, -0.01], [-0.299, -0.02], [-0.273, -0.027], [-0.294, -0.038], [-0.263, -0.041], [-0.311, -0.059], [-0.322, -0.073], [-0.076, -0.019], [-0.438, -0.13], [-0.124, -0.039], [-0.352, -0.131], [-0.102, -0.04], [-0.309, -0.137], [-0.149, -0.071], [-0.418, -0.242], [0, 0], [0.447, 0.212], [0.151, 0.067], [0.275, 0.108], [0.047, 0.019], [0.103, 0.038], [0.365, 0.115], [0.126, 0.037], [0.085, 0.024], [0.365, 0.087], [0.078, 0.018], [0.18, 0.037], [0.146, 0.028], [0.317, 0.049], [0.127, 0.018], [0.138, 0.017], [0.297, 0.029], [0.141, 0.011], [0.132, 0.01], [0.3, 0.011], [0.15, 0.004], [0.121, 0.002], [0.324, -0.007], [0.161, -0.005], [0.163, -0.008], [0.089, -0.006], [0.402, -0.04], [0.077, -0.008], [0.119, -0.015], [0.406, -0.066], [0.099, -0.017], [0.048, -0.008], [0.191, -0.038], [0.323, -0.078], [0.232, -0.063], [0.299, -0.094], [0.064, -0.021], [0.353, -0.134], [0.051, -0.02], [0.371, -0.17], [0.108, -0.052], [0.385, -0.223], [0.071, -0.043], [0.065, -0.04], [0.105, -0.07], [0.093, -0.064], [0.098, -0.072], [0.087, -0.065], [0.091, -0.073], [0.08, -0.068], [0.083, -0.074], [0.033, -0.031], [0.031, -0.029], [0.128, -0.135], [0.042, -0.046], [0.143, -0.18], [0.006, -0.007], [0.031, -0.042], [0.078, -0.115], [0.041, -0.064], [0.061, -0.104], [0.017, -0.029], [0.023, -0.043], [0.044, -0.089], [0.034, -0.075], [0.035, -0.09], [0.015, -0.04], [0.013, -0.036], [0.027, -0.086], [0.021, -0.078], [0.019, -0.086], [0.009, -0.047], [0.006, -0.032], [0.012, -0.089], [0.008, -0.078], [0.005, -0.09], [0.002, -0.052], [0, -0.052], [0, 0], [-0.006, 0.104], [-0.009, 0.089], [-0.011, 0.077], [-0.017, 0.088], [-0.018, 0.078], [-0.023, 0.086], [-0.024, 0.078], [-0.03, 0.086], [-0.03, 0.076], [-0.04, 0.089], [-0.036, 0.075], [-0.047, 0.089], [-0.041, 0.072], [-0.067, 0.104], [-0.043, 0.063], [-0.086, 0.114], [-0.039, 0.05], [-0.162, 0.176], [-0.043, 0.045], [-0.139, 0.131], [-0.066, 0.06], [-0.087, 0.074], [-0.084, 0.067], [-0.094, 0.072], [-0.09, 0.065], [-0.101, 0.069], [-0.096, 0.063], [-0.109, 0.067], [-0.141, 0.082], [-0.41, 0.198], [-0.109, 0.05]], "o": [[0.051, -0.02], [0.415, -0.158], [0.299, -0.094], [0.232, -0.063], [0.321, -0.078], [0.24, -0.049], [0.098, -0.017], [0.407, -0.067], [0.119, -0.015], [0.477, -0.051], [0.089, -0.005], [0.324, -0.016], [0.324, -0.005], [0.27, 0.003], [0.3, 0.012], [0.274, 0.019], [0.296, 0.029], [0.265, 0.034], [0.315, 0.049], [0.326, 0.062], [0.076, 0.018], [0.452, 0.107], [0.126, 0.037], [0.364, 0.115], [0.104, 0.038], [0.321, 0.124], [0.153, 0.068], [0.447, 0.212], [0, 0], [-0.418, -0.242], [-0.148, -0.071], [-0.266, -0.118], [-0.047, -0.019], [-0.102, -0.039], [-0.353, -0.131], [-0.124, -0.039], [-0.084, -0.025], [-0.357, -0.101], [-0.077, -0.018], [-0.177, -0.04], [-0.144, -0.03], [-0.312, -0.059], [-0.126, -0.02], [-0.137, -0.019], [-0.295, -0.038], [-0.141, -0.014], [-0.132, -0.011], [-0.299, -0.021], [-0.151, -0.007], [-0.12, -0.003], [-0.324, -0.004], [-0.16, 0.002], [-0.163, 0.006], [-0.09, 0.005], [-0.404, 0.023], [-0.077, 0.007], [-0.12, 0.013], [-0.412, 0.049], [-0.101, 0.017], [-0.048, 0.009], [-0.193, 0.035], [-0.328, 0.067], [-0.235, 0.057], [-0.307, 0.084], [-0.064, 0.02], [-0.365, 0.118], [-0.052, 0.02], [-0.387, 0.151], [-0.108, 0.051], [-0.409, 0.198], [-0.072, 0.042], [-0.067, 0.04], [-0.109, 0.068], [-0.096, 0.062], [-0.102, 0.07], [-0.09, 0.065], [-0.093, 0.073], [-0.083, 0.068], [-0.087, 0.074], [-0.034, 0.031], [-0.031, 0.029], [-0.139, 0.132], [-0.043, 0.045], [-0.163, 0.177], [-0.006, 0.008], [-0.033, 0.042], [-0.087, 0.114], [-0.044, 0.063], [-0.067, 0.104], [-0.016, 0.029], [-0.024, 0.043], [-0.048, 0.089], [-0.036, 0.074], [-0.04, 0.09], [-0.016, 0.04], [-0.013, 0.036], [-0.03, 0.085], [-0.024, 0.077], [-0.024, 0.086], [-0.011, 0.047], [-0.007, 0.031], [-0.017, 0.088], [-0.011, 0.077], [-0.009, 0.089], [-0.003, 0.051], [-0.001, 0.052], [0, 0], [0, -0.104], [0.004, -0.09], [0.007, -0.078], [0.013, -0.089], [0.014, -0.079], [0.019, -0.086], [0.021, -0.078], [0.027, -0.086], [0.027, -0.077], [0.036, -0.09], [0.033, -0.075], [0.044, -0.088], [0.039, -0.071], [0.062, -0.105], [0.041, -0.063], [0.079, -0.116], [0.038, -0.05], [0.143, -0.18], [0.042, -0.045], [0.128, -0.135], [0.064, -0.061], [0.083, -0.074], [0.08, -0.069], [0.09, -0.073], [0.087, -0.066], [0.097, -0.071], [0.093, -0.064], [0.105, -0.069], [0.135, -0.084], [0.385, -0.224], [0.106, -0.053], [0.37, -0.171]], "v": [[-7.308, -0.868], [-7.157, -0.929], [-5.884, -1.365], [-4.973, -1.626], [-4.274, -1.813], [-3.298, -2.023], [-2.579, -2.169], [-2.281, -2.215], [-1.052, -2.389], [-0.694, -2.432], [0.745, -2.553], [1.012, -2.566], [1.985, -2.593], [2.957, -2.597], [3.768, -2.575], [4.667, -2.527], [5.486, -2.458], [6.371, -2.359], [7.164, -2.247], [8.103, -2.084], [9.078, -1.887], [9.308, -1.834], [10.643, -1.476], [11.017, -1.36], [12.093, -0.993], [12.403, -0.88], [13.342, -0.48], [13.802, -0.281], [15.102, 0.399], [15.114, -4.606], [13.814, -5.286], [13.358, -5.484], [12.557, -5.833], [12.415, -5.885], [12.107, -5.997], [11.029, -6.365], [10.656, -6.481], [10.405, -6.559], [9.321, -6.84], [9.089, -6.893], [8.556, -7.012], [8.115, -7.089], [7.175, -7.252], [6.799, -7.318], [6.383, -7.364], [5.498, -7.463], [5.076, -7.51], [4.679, -7.532], [3.781, -7.579], [3.33, -7.605], [2.968, -7.602], [1.997, -7.597], [1.514, -7.596], [1.025, -7.571], [0.757, -7.557], [-0.452, -7.464], [-0.682, -7.437], [-1.04, -7.394], [-2.267, -7.221], [-2.568, -7.174], [-2.712, -7.15], [-3.283, -7.028], [-4.263, -6.818], [-4.959, -6.631], [-5.872, -6.37], [-6.068, -6.314], [-7.144, -5.934], [-7.296, -5.872], [-8.431, -5.389], [-8.756, -5.234], [-9.952, -4.606], [-10.168, -4.479], [-10.362, -4.356], [-10.684, -4.152], [-10.966, -3.962], [-11.266, -3.749], [-11.53, -3.553], [-11.808, -3.335], [-12.052, -3.132], [-12.307, -2.91], [-12.412, -2.817], [-12.501, -2.73], [-12.902, -2.329], [-13.032, -2.195], [-13.492, -1.658], [-13.511, -1.635], [-13.603, -1.51], [-13.853, -1.166], [-13.978, -0.975], [-14.172, -0.662], [-14.225, -0.576], [-14.291, -0.447], [-14.429, -0.18], [-14.533, 0.042], [-14.646, 0.312], [-14.697, 0.432], [-14.733, 0.541], [-14.818, 0.798], [-14.886, 1.031], [-14.949, 1.29], [-14.983, 1.43], [-14.998, 1.525], [-15.04, 1.79], [-15.069, 2.023], [-15.088, 2.291], [-15.1, 2.447], [-15.102, 3.316], [-15.114, 7.607], [-15.1, 7.296], [-15.081, 7.028], [-15.052, 6.795], [-15.01, 6.529], [-14.961, 6.295], [-14.898, 6.036], [-14.83, 5.801], [-14.745, 5.546], [-14.658, 5.317], [-14.545, 5.048], [-14.441, 4.824], [-14.304, 4.558], [-14.184, 4.343], [-13.99, 4.029], [-13.866, 3.839], [-13.616, 3.497], [-13.504, 3.347], [-13.044, 2.811], [-12.914, 2.676], [-12.513, 2.277], [-12.319, 2.096], [-12.064, 1.874], [-11.819, 1.67], [-11.542, 1.452], [-11.278, 1.256], [-10.979, 1.045], [-10.696, 0.852], [-10.374, 0.65], [-9.964, 0.399], [-8.767, -0.23], [-8.443, -0.382]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.8, 0.035000000748, 0.416000007181, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [28.666, 14.847], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 12", "np": 3, "cix": 2, "bm": 0, "ix": 12, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[3.716, -4.302], [3.704, -0.01], [-3.716, 4.302], [-3.704, 0.01]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.8, 0.035000000748, 0.416000007181, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [47.484, 10.944], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 13", "np": 2, "cix": 2, "bm": 0, "ix": 13, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 94.0000038286985, "st": 0, "bm": 0}, {"ddd": 1, "ind": 16, "ty": 4, "nm": "Layer 18 Outlines", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "rx": {"a": 0, "k": 0, "ix": 8}, "ry": {"a": 0, "k": 0, "ix": 9}, "rz": {"a": 0, "k": 0, "ix": 10}, "or": {"a": 0, "k": [0, 0, 0], "ix": 7}, "p": {"a": 0, "k": [251.385, 213.921, 0], "ix": 2}, "a": {"a": 0, "k": [32.988, 19.195, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-11.674, -6.74], [11.599, -6.74], [11.674, 6.74], [-11.598, 6.74]], "o": [[11.674, 6.74], [-11.597, 6.74], [-11.674, -6.74], [11.598, -6.74]], "v": [[20.989, -12.205], [21.139, 12.205], [-21.016, 12.205], [-21.139, -12.205]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.677999997606, 0.804000016755, 0.987999949736, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [32.987, 19.195], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 94.0000038286985, "st": 0, "bm": 0}, {"ddd": 0, "ind": 17, "ty": 4, "nm": "Layer 19 Outlines", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.573}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [221.922, 151.107, 0], "to": [0, -0.022, 0], "ti": [0, 0.114, 0]}, {"i": {"x": 0.833, "y": 0.705}, "o": {"x": 0.167, "y": 0.104}, "t": 1, "s": [221.922, 150.974, 0], "to": [0, -0.114, 0], "ti": [0, 0.324, 0]}, {"i": {"x": 0.833, "y": 0.754}, "o": {"x": 0.167, "y": 0.116}, "t": 2, "s": [221.922, 150.425, 0], "to": [0, -0.324, 0], "ti": [0, 0.685, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.126}, "t": 3, "s": [221.922, 149.032, 0], "to": [0, -0.685, 0], "ti": [0, 0.904, 0]}, {"i": {"x": 0.833, "y": 0.859}, "o": {"x": 0.167, "y": 0.167}, "t": 4, "s": [221.922, 146.313, 0], "to": [0, -0.904, 0], "ti": [0, 0.761, 0]}, {"i": {"x": 0.833, "y": 0.858}, "o": {"x": 0.167, "y": 0.204}, "t": 5, "s": [221.922, 143.609, 0], "to": [0, -0.761, 0], "ti": [0, 0.53, 0]}, {"i": {"x": 0.833, "y": 0.855}, "o": {"x": 0.167, "y": 0.201}, "t": 6, "s": [221.922, 141.746, 0], "to": [0, -0.53, 0], "ti": [0, 0.383, 0]}, {"i": {"x": 0.833, "y": 0.853}, "o": {"x": 0.167, "y": 0.195}, "t": 7, "s": [221.922, 140.429, 0], "to": [0, -0.383, 0], "ti": [0, 0.288, 0]}, {"i": {"x": 0.833, "y": 0.851}, "o": {"x": 0.167, "y": 0.192}, "t": 8, "s": [221.922, 139.45, 0], "to": [0, -0.288, 0], "ti": [0, 0.224, 0]}, {"i": {"x": 0.833, "y": 0.85}, "o": {"x": 0.167, "y": 0.189}, "t": 9, "s": [221.922, 138.7, 0], "to": [0, -0.224, 0], "ti": [0, 0.179, 0]}, {"i": {"x": 0.833, "y": 0.849}, "o": {"x": 0.167, "y": 0.187}, "t": 10, "s": [221.922, 138.106, 0], "to": [0, -0.179, 0], "ti": [0, 0.144, 0]}, {"i": {"x": 0.833, "y": 0.849}, "o": {"x": 0.167, "y": 0.186}, "t": 11, "s": [221.922, 137.628, 0], "to": [0, -0.144, 0], "ti": [0, 0.117, 0]}, {"i": {"x": 0.833, "y": 0.85}, "o": {"x": 0.167, "y": 0.187}, "t": 12, "s": [221.922, 137.24, 0], "to": [0, -0.117, 0], "ti": [0, 0.094, 0]}, {"i": {"x": 0.833, "y": 0.852}, "o": {"x": 0.167, "y": 0.188}, "t": 13, "s": [221.922, 136.928, 0], "to": [0, -0.094, 0], "ti": [0, 0.074, 0]}, {"i": {"x": 0.833, "y": 0.854}, "o": {"x": 0.167, "y": 0.19}, "t": 14, "s": [221.922, 136.679, 0], "to": [0, -0.074, 0], "ti": [0, 0.057, 0]}, {"i": {"x": 0.833, "y": 0.856}, "o": {"x": 0.167, "y": 0.193}, "t": 15, "s": [221.922, 136.484, 0], "to": [0, -0.057, 0], "ti": [0, 0.042, 0]}, {"i": {"x": 0.833, "y": 0.861}, "o": {"x": 0.167, "y": 0.199}, "t": 16, "s": [221.922, 136.337, 0], "to": [0, -0.042, 0], "ti": [0, 0.03, 0]}, {"i": {"x": 0.833, "y": 0.87}, "o": {"x": 0.167, "y": 0.208}, "t": 17, "s": [221.922, 136.231, 0], "to": [0, -0.03, 0], "ti": [0, 0.018, 0]}, {"i": {"x": 0.833, "y": 0.89}, "o": {"x": 0.167, "y": 0.231}, "t": 18, "s": [221.922, 136.16, 0], "to": [0, -0.018, 0], "ti": [0, 0.009, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.347}, "t": 19, "s": [221.922, 136.12, 0], "to": [0, -0.009, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.653}, "o": {"x": 0.167, "y": 0.167}, "t": 20, "s": [221.922, 136.107, 0], "to": [0, 0, 0], "ti": [0, -0.009, 0]}, {"i": {"x": 0.833, "y": 0.769}, "o": {"x": 0.167, "y": 0.11}, "t": 21, "s": [221.922, 136.12, 0], "to": [0, 0.009, 0], "ti": [0, -0.018, 0]}, {"i": {"x": 0.833, "y": 0.792}, "o": {"x": 0.167, "y": 0.13}, "t": 22, "s": [221.922, 136.16, 0], "to": [0, 0.018, 0], "ti": [0, -0.03, 0]}, {"i": {"x": 0.833, "y": 0.801}, "o": {"x": 0.167, "y": 0.139}, "t": 23, "s": [221.922, 136.231, 0], "to": [0, 0.03, 0], "ti": [0, -0.042, 0]}, {"i": {"x": 0.833, "y": 0.807}, "o": {"x": 0.167, "y": 0.144}, "t": 24, "s": [221.922, 136.337, 0], "to": [0, 0.042, 0], "ti": [0, -0.057, 0]}, {"i": {"x": 0.833, "y": 0.81}, "o": {"x": 0.167, "y": 0.146}, "t": 25, "s": [221.922, 136.484, 0], "to": [0, 0.057, 0], "ti": [0, -0.074, 0]}, {"i": {"x": 0.833, "y": 0.812}, "o": {"x": 0.167, "y": 0.148}, "t": 26, "s": [221.922, 136.679, 0], "to": [0, 0.074, 0], "ti": [0, -0.094, 0]}, {"i": {"x": 0.833, "y": 0.813}, "o": {"x": 0.167, "y": 0.15}, "t": 27, "s": [221.922, 136.928, 0], "to": [0, 0.094, 0], "ti": [0, -0.117, 0]}, {"i": {"x": 0.833, "y": 0.814}, "o": {"x": 0.167, "y": 0.151}, "t": 28, "s": [221.922, 137.24, 0], "to": [0, 0.117, 0], "ti": [0, -0.144, 0]}, {"i": {"x": 0.833, "y": 0.813}, "o": {"x": 0.167, "y": 0.151}, "t": 29, "s": [221.922, 137.628, 0], "to": [0, 0.144, 0], "ti": [0, -0.179, 0]}, {"i": {"x": 0.833, "y": 0.811}, "o": {"x": 0.167, "y": 0.15}, "t": 30, "s": [221.922, 138.106, 0], "to": [0, 0.179, 0], "ti": [0, -0.224, 0]}, {"i": {"x": 0.833, "y": 0.808}, "o": {"x": 0.167, "y": 0.149}, "t": 31, "s": [221.922, 138.7, 0], "to": [0, 0.224, 0], "ti": [0, -0.288, 0]}, {"i": {"x": 0.833, "y": 0.805}, "o": {"x": 0.167, "y": 0.147}, "t": 32, "s": [221.922, 139.45, 0], "to": [0, 0.288, 0], "ti": [0, -0.383, 0]}, {"i": {"x": 0.833, "y": 0.799}, "o": {"x": 0.167, "y": 0.145}, "t": 33, "s": [221.922, 140.429, 0], "to": [0, 0.383, 0], "ti": [0, -0.53, 0]}, {"i": {"x": 0.833, "y": 0.796}, "o": {"x": 0.167, "y": 0.142}, "t": 34, "s": [221.922, 141.746, 0], "to": [0, 0.53, 0], "ti": [0, -0.761, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.141}, "t": 35, "s": [221.922, 143.609, 0], "to": [0, 0.761, 0], "ti": [0, -0.904, 0]}, {"i": {"x": 0.833, "y": 0.874}, "o": {"x": 0.167, "y": 0.166}, "t": 36, "s": [221.922, 146.313, 0], "to": [0, 0.904, 0], "ti": [0, -0.685, 0]}, {"i": {"x": 0.833, "y": 0.884}, "o": {"x": 0.167, "y": 0.246}, "t": 37, "s": [221.922, 149.032, 0], "to": [0, 0.685, 0], "ti": [0, -0.324, 0]}, {"i": {"x": 0.833, "y": 0.896}, "o": {"x": 0.167, "y": 0.295}, "t": 38, "s": [221.922, 150.425, 0], "to": [0, 0.324, 0], "ti": [0, -0.114, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.427}, "t": 39, "s": [221.922, 150.974, 0], "to": [0, 0.114, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.573}, "o": {"x": 0.167, "y": 0.167}, "t": 40, "s": [221.922, 151.107, 0], "to": [0, 0, 0], "ti": [0, 0.114, 0]}, {"i": {"x": 0.833, "y": 0.705}, "o": {"x": 0.167, "y": 0.104}, "t": 41, "s": [221.922, 150.974, 0], "to": [0, -0.114, 0], "ti": [0, 0.324, 0]}, {"i": {"x": 0.833, "y": 0.754}, "o": {"x": 0.167, "y": 0.116}, "t": 42, "s": [221.922, 150.425, 0], "to": [0, -0.324, 0], "ti": [0, 0.685, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.126}, "t": 43, "s": [221.922, 149.032, 0], "to": [0, -0.685, 0], "ti": [0, 0.904, 0]}, {"i": {"x": 0.833, "y": 0.859}, "o": {"x": 0.167, "y": 0.167}, "t": 44, "s": [221.922, 146.313, 0], "to": [0, -0.904, 0], "ti": [0, 0.761, 0]}, {"i": {"x": 0.833, "y": 0.858}, "o": {"x": 0.167, "y": 0.204}, "t": 45, "s": [221.922, 143.609, 0], "to": [0, -0.761, 0], "ti": [0, 0.53, 0]}, {"i": {"x": 0.833, "y": 0.855}, "o": {"x": 0.167, "y": 0.201}, "t": 46, "s": [221.922, 141.746, 0], "to": [0, -0.53, 0], "ti": [0, 0.383, 0]}, {"i": {"x": 0.833, "y": 0.853}, "o": {"x": 0.167, "y": 0.195}, "t": 47, "s": [221.922, 140.429, 0], "to": [0, -0.383, 0], "ti": [0, 0.288, 0]}, {"i": {"x": 0.833, "y": 0.851}, "o": {"x": 0.167, "y": 0.192}, "t": 48, "s": [221.922, 139.45, 0], "to": [0, -0.288, 0], "ti": [0, 0.224, 0]}, {"i": {"x": 0.833, "y": 0.85}, "o": {"x": 0.167, "y": 0.189}, "t": 49, "s": [221.922, 138.7, 0], "to": [0, -0.224, 0], "ti": [0, 0.179, 0]}, {"i": {"x": 0.833, "y": 0.849}, "o": {"x": 0.167, "y": 0.187}, "t": 50, "s": [221.922, 138.106, 0], "to": [0, -0.179, 0], "ti": [0, 0.144, 0]}, {"i": {"x": 0.833, "y": 0.849}, "o": {"x": 0.167, "y": 0.186}, "t": 51, "s": [221.922, 137.628, 0], "to": [0, -0.144, 0], "ti": [0, 0.117, 0]}, {"i": {"x": 0.833, "y": 0.85}, "o": {"x": 0.167, "y": 0.187}, "t": 52, "s": [221.922, 137.24, 0], "to": [0, -0.117, 0], "ti": [0, 0.094, 0]}, {"i": {"x": 0.833, "y": 0.852}, "o": {"x": 0.167, "y": 0.188}, "t": 53, "s": [221.922, 136.928, 0], "to": [0, -0.094, 0], "ti": [0, 0.074, 0]}, {"i": {"x": 0.833, "y": 0.854}, "o": {"x": 0.167, "y": 0.19}, "t": 54, "s": [221.922, 136.679, 0], "to": [0, -0.074, 0], "ti": [0, 0.057, 0]}, {"i": {"x": 0.833, "y": 0.856}, "o": {"x": 0.167, "y": 0.193}, "t": 55, "s": [221.922, 136.484, 0], "to": [0, -0.057, 0], "ti": [0, 0.042, 0]}, {"i": {"x": 0.833, "y": 0.861}, "o": {"x": 0.167, "y": 0.199}, "t": 56, "s": [221.922, 136.337, 0], "to": [0, -0.042, 0], "ti": [0, 0.03, 0]}, {"i": {"x": 0.833, "y": 0.87}, "o": {"x": 0.167, "y": 0.208}, "t": 57, "s": [221.922, 136.231, 0], "to": [0, -0.03, 0], "ti": [0, 0.018, 0]}, {"i": {"x": 0.833, "y": 0.89}, "o": {"x": 0.167, "y": 0.231}, "t": 58, "s": [221.922, 136.16, 0], "to": [0, -0.018, 0], "ti": [0, 0.009, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.347}, "t": 59, "s": [221.922, 136.12, 0], "to": [0, -0.009, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.653}, "o": {"x": 0.167, "y": 0.167}, "t": 60, "s": [221.922, 136.107, 0], "to": [0, 0, 0], "ti": [0, -0.009, 0]}, {"i": {"x": 0.833, "y": 0.769}, "o": {"x": 0.167, "y": 0.11}, "t": 61, "s": [221.922, 136.12, 0], "to": [0, 0.009, 0], "ti": [0, -0.018, 0]}, {"i": {"x": 0.833, "y": 0.792}, "o": {"x": 0.167, "y": 0.13}, "t": 62, "s": [221.922, 136.16, 0], "to": [0, 0.018, 0], "ti": [0, -0.03, 0]}, {"i": {"x": 0.833, "y": 0.801}, "o": {"x": 0.167, "y": 0.139}, "t": 63, "s": [221.922, 136.231, 0], "to": [0, 0.03, 0], "ti": [0, -0.042, 0]}, {"i": {"x": 0.833, "y": 0.807}, "o": {"x": 0.167, "y": 0.144}, "t": 64, "s": [221.922, 136.337, 0], "to": [0, 0.042, 0], "ti": [0, -0.057, 0]}, {"i": {"x": 0.833, "y": 0.81}, "o": {"x": 0.167, "y": 0.146}, "t": 65, "s": [221.922, 136.484, 0], "to": [0, 0.057, 0], "ti": [0, -0.074, 0]}, {"i": {"x": 0.833, "y": 0.812}, "o": {"x": 0.167, "y": 0.148}, "t": 66, "s": [221.922, 136.679, 0], "to": [0, 0.074, 0], "ti": [0, -0.094, 0]}, {"i": {"x": 0.833, "y": 0.813}, "o": {"x": 0.167, "y": 0.15}, "t": 67, "s": [221.922, 136.928, 0], "to": [0, 0.094, 0], "ti": [0, -0.117, 0]}, {"i": {"x": 0.833, "y": 0.814}, "o": {"x": 0.167, "y": 0.151}, "t": 68, "s": [221.922, 137.24, 0], "to": [0, 0.117, 0], "ti": [0, -0.144, 0]}, {"i": {"x": 0.833, "y": 0.813}, "o": {"x": 0.167, "y": 0.151}, "t": 69, "s": [221.922, 137.628, 0], "to": [0, 0.144, 0], "ti": [0, -0.179, 0]}, {"i": {"x": 0.833, "y": 0.811}, "o": {"x": 0.167, "y": 0.15}, "t": 70, "s": [221.922, 138.106, 0], "to": [0, 0.179, 0], "ti": [0, -0.224, 0]}, {"i": {"x": 0.833, "y": 0.808}, "o": {"x": 0.167, "y": 0.149}, "t": 71, "s": [221.922, 138.7, 0], "to": [0, 0.224, 0], "ti": [0, -0.288, 0]}, {"i": {"x": 0.833, "y": 0.805}, "o": {"x": 0.167, "y": 0.147}, "t": 72, "s": [221.922, 139.45, 0], "to": [0, 0.288, 0], "ti": [0, -0.383, 0]}, {"i": {"x": 0.833, "y": 0.799}, "o": {"x": 0.167, "y": 0.145}, "t": 73, "s": [221.922, 140.429, 0], "to": [0, 0.383, 0], "ti": [0, -0.53, 0]}, {"i": {"x": 0.833, "y": 0.796}, "o": {"x": 0.167, "y": 0.142}, "t": 74, "s": [221.922, 141.746, 0], "to": [0, 0.53, 0], "ti": [0, -0.761, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.141}, "t": 75, "s": [221.922, 143.609, 0], "to": [0, 0.761, 0], "ti": [0, -0.904, 0]}, {"i": {"x": 0.833, "y": 0.874}, "o": {"x": 0.167, "y": 0.166}, "t": 76, "s": [221.922, 146.313, 0], "to": [0, 0.904, 0], "ti": [0, -0.685, 0]}, {"i": {"x": 0.833, "y": 0.884}, "o": {"x": 0.167, "y": 0.246}, "t": 77, "s": [221.922, 149.032, 0], "to": [0, 0.685, 0], "ti": [0, -0.324, 0]}, {"i": {"x": 0.833, "y": 0.896}, "o": {"x": 0.167, "y": 0.295}, "t": 78, "s": [221.922, 150.425, 0], "to": [0, 0.324, 0], "ti": [0, -0.114, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.427}, "t": 79, "s": [221.922, 150.974, 0], "to": [0, 0.114, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.573}, "o": {"x": 0.167, "y": 0.167}, "t": 80, "s": [221.922, 151.107, 0], "to": [0, 0, 0], "ti": [0, 0.114, 0]}, {"i": {"x": 0.833, "y": 0.705}, "o": {"x": 0.167, "y": 0.104}, "t": 81, "s": [221.922, 150.974, 0], "to": [0, -0.114, 0], "ti": [0, 0.324, 0]}, {"i": {"x": 0.833, "y": 0.754}, "o": {"x": 0.167, "y": 0.116}, "t": 82, "s": [221.922, 150.425, 0], "to": [0, -0.324, 0], "ti": [0, 0.685, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.126}, "t": 83, "s": [221.922, 149.032, 0], "to": [0, -0.685, 0], "ti": [0, 0.904, 0]}, {"i": {"x": 0.833, "y": 0.859}, "o": {"x": 0.167, "y": 0.167}, "t": 84, "s": [221.922, 146.313, 0], "to": [0, -0.904, 0], "ti": [0, 0.761, 0]}, {"i": {"x": 0.833, "y": 0.858}, "o": {"x": 0.167, "y": 0.204}, "t": 85, "s": [221.922, 143.609, 0], "to": [0, -0.761, 0], "ti": [0, 0.53, 0]}, {"i": {"x": 0.833, "y": 0.855}, "o": {"x": 0.167, "y": 0.201}, "t": 86, "s": [221.922, 141.746, 0], "to": [0, -0.53, 0], "ti": [0, 0.383, 0]}, {"i": {"x": 0.833, "y": 0.853}, "o": {"x": 0.167, "y": 0.195}, "t": 87, "s": [221.922, 140.429, 0], "to": [0, -0.383, 0], "ti": [0, 0.288, 0]}, {"i": {"x": 0.833, "y": 0.851}, "o": {"x": 0.167, "y": 0.192}, "t": 88, "s": [221.922, 139.45, 0], "to": [0, -0.288, 0], "ti": [0, 0.224, 0]}, {"i": {"x": 0.833, "y": 0.85}, "o": {"x": 0.167, "y": 0.189}, "t": 89, "s": [221.922, 138.7, 0], "to": [0, -0.224, 0], "ti": [0, 0.179, 0]}, {"i": {"x": 0.833, "y": 0.849}, "o": {"x": 0.167, "y": 0.187}, "t": 90, "s": [221.922, 138.106, 0], "to": [0, -0.179, 0], "ti": [0, 0.144, 0]}, {"i": {"x": 0.833, "y": 0.849}, "o": {"x": 0.167, "y": 0.186}, "t": 91, "s": [221.922, 137.628, 0], "to": [0, -0.144, 0], "ti": [0, 0.117, 0]}, {"i": {"x": 0.833, "y": 0.85}, "o": {"x": 0.167, "y": 0.187}, "t": 92, "s": [221.922, 137.24, 0], "to": [0, -0.117, 0], "ti": [0, 0.094, 0]}, {"i": {"x": 0.833, "y": 0.852}, "o": {"x": 0.167, "y": 0.188}, "t": 93, "s": [221.922, 136.928, 0], "to": [0, -0.094, 0], "ti": [0, 0.074, 0]}, {"i": {"x": 0.833, "y": 0.854}, "o": {"x": 0.167, "y": 0.19}, "t": 94, "s": [221.922, 136.679, 0], "to": [0, -0.074, 0], "ti": [0, 0.057, 0]}, {"i": {"x": 0.833, "y": 0.856}, "o": {"x": 0.167, "y": 0.193}, "t": 95, "s": [221.922, 136.484, 0], "to": [0, -0.057, 0], "ti": [0, 0.042, 0]}, {"i": {"x": 0.833, "y": 0.861}, "o": {"x": 0.167, "y": 0.199}, "t": 96, "s": [221.922, 136.337, 0], "to": [0, -0.042, 0], "ti": [0, 0.03, 0]}, {"i": {"x": 0.833, "y": 0.87}, "o": {"x": 0.167, "y": 0.208}, "t": 97, "s": [221.922, 136.231, 0], "to": [0, -0.03, 0], "ti": [0, 0.018, 0]}, {"i": {"x": 0.833, "y": 0.89}, "o": {"x": 0.167, "y": 0.231}, "t": 98, "s": [221.922, 136.16, 0], "to": [0, -0.018, 0], "ti": [0, 0.009, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.347}, "t": 99, "s": [221.922, 136.12, 0], "to": [0, -0.009, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.653}, "o": {"x": 0.167, "y": 0.167}, "t": 100, "s": [221.922, 136.107, 0], "to": [0, 0, 0], "ti": [0, -0.009, 0]}, {"i": {"x": 0.833, "y": 0.769}, "o": {"x": 0.167, "y": 0.11}, "t": 101, "s": [221.922, 136.12, 0], "to": [0, 0.009, 0], "ti": [0, -0.018, 0]}, {"i": {"x": 0.833, "y": 0.792}, "o": {"x": 0.167, "y": 0.13}, "t": 102, "s": [221.922, 136.16, 0], "to": [0, 0.018, 0], "ti": [0, -0.03, 0]}, {"i": {"x": 0.833, "y": 0.801}, "o": {"x": 0.167, "y": 0.139}, "t": 103, "s": [221.922, 136.231, 0], "to": [0, 0.03, 0], "ti": [0, -0.042, 0]}, {"i": {"x": 0.833, "y": 0.807}, "o": {"x": 0.167, "y": 0.144}, "t": 104, "s": [221.922, 136.337, 0], "to": [0, 0.042, 0], "ti": [0, -0.057, 0]}, {"i": {"x": 0.833, "y": 0.81}, "o": {"x": 0.167, "y": 0.146}, "t": 105, "s": [221.922, 136.484, 0], "to": [0, 0.057, 0], "ti": [0, -0.074, 0]}, {"i": {"x": 0.833, "y": 0.812}, "o": {"x": 0.167, "y": 0.148}, "t": 106, "s": [221.922, 136.679, 0], "to": [0, 0.074, 0], "ti": [0, -0.094, 0]}, {"i": {"x": 0.833, "y": 0.813}, "o": {"x": 0.167, "y": 0.15}, "t": 107, "s": [221.922, 136.928, 0], "to": [0, 0.094, 0], "ti": [0, -0.117, 0]}, {"i": {"x": 0.833, "y": 0.814}, "o": {"x": 0.167, "y": 0.151}, "t": 108, "s": [221.922, 137.24, 0], "to": [0, 0.117, 0], "ti": [0, -0.144, 0]}, {"i": {"x": 0.833, "y": 0.813}, "o": {"x": 0.167, "y": 0.151}, "t": 109, "s": [221.922, 137.628, 0], "to": [0, 0.144, 0], "ti": [0, -0.179, 0]}, {"i": {"x": 0.833, "y": 0.811}, "o": {"x": 0.167, "y": 0.15}, "t": 110, "s": [221.922, 138.106, 0], "to": [0, 0.179, 0], "ti": [0, -0.224, 0]}, {"i": {"x": 0.833, "y": 0.808}, "o": {"x": 0.167, "y": 0.149}, "t": 111, "s": [221.922, 138.7, 0], "to": [0, 0.224, 0], "ti": [0, -0.288, 0]}, {"i": {"x": 0.833, "y": 0.805}, "o": {"x": 0.167, "y": 0.147}, "t": 112, "s": [221.922, 139.45, 0], "to": [0, 0.288, 0], "ti": [0, -0.383, 0]}, {"i": {"x": 0.833, "y": 0.799}, "o": {"x": 0.167, "y": 0.145}, "t": 113, "s": [221.922, 140.429, 0], "to": [0, 0.383, 0], "ti": [0, -0.53, 0]}, {"i": {"x": 0.833, "y": 0.796}, "o": {"x": 0.167, "y": 0.142}, "t": 114, "s": [221.922, 141.746, 0], "to": [0, 0.53, 0], "ti": [0, -0.761, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.141}, "t": 115, "s": [221.922, 143.609, 0], "to": [0, 0.761, 0], "ti": [0, -0.904, 0]}, {"i": {"x": 0.833, "y": 0.874}, "o": {"x": 0.167, "y": 0.166}, "t": 116, "s": [221.922, 146.313, 0], "to": [0, 0.904, 0], "ti": [0, -0.685, 0]}, {"i": {"x": 0.833, "y": 0.884}, "o": {"x": 0.167, "y": 0.246}, "t": 117, "s": [221.922, 149.032, 0], "to": [0, 0.685, 0], "ti": [0, -0.324, 0]}, {"i": {"x": 0.833, "y": 0.896}, "o": {"x": 0.167, "y": 0.295}, "t": 118, "s": [221.922, 150.425, 0], "to": [0, 0.324, 0], "ti": [0, -0.114, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.427}, "t": 119, "s": [221.922, 150.974, 0], "to": [0, 0.114, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.573}, "o": {"x": 0.167, "y": 0.167}, "t": 120, "s": [221.922, 151.107, 0], "to": [0, 0, 0], "ti": [0, 0.114, 0]}, {"i": {"x": 0.833, "y": 0.705}, "o": {"x": 0.167, "y": 0.104}, "t": 121, "s": [221.922, 150.974, 0], "to": [0, -0.114, 0], "ti": [0, 0.324, 0]}, {"i": {"x": 0.833, "y": 0.754}, "o": {"x": 0.167, "y": 0.116}, "t": 122, "s": [221.922, 150.425, 0], "to": [0, -0.324, 0], "ti": [0, 0.685, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.126}, "t": 123, "s": [221.922, 149.032, 0], "to": [0, -0.685, 0], "ti": [0, 0.904, 0]}, {"i": {"x": 0.833, "y": 0.859}, "o": {"x": 0.167, "y": 0.167}, "t": 124, "s": [221.922, 146.313, 0], "to": [0, -0.904, 0], "ti": [0, 0.761, 0]}, {"i": {"x": 0.833, "y": 0.858}, "o": {"x": 0.167, "y": 0.204}, "t": 125, "s": [221.922, 143.609, 0], "to": [0, -0.761, 0], "ti": [0, 0.53, 0]}, {"i": {"x": 0.833, "y": 0.855}, "o": {"x": 0.167, "y": 0.201}, "t": 126, "s": [221.922, 141.746, 0], "to": [0, -0.53, 0], "ti": [0, 0.383, 0]}, {"i": {"x": 0.833, "y": 0.853}, "o": {"x": 0.167, "y": 0.195}, "t": 127, "s": [221.922, 140.429, 0], "to": [0, -0.383, 0], "ti": [0, 0.288, 0]}, {"i": {"x": 0.833, "y": 0.851}, "o": {"x": 0.167, "y": 0.192}, "t": 128, "s": [221.922, 139.45, 0], "to": [0, -0.288, 0], "ti": [0, 0.224, 0]}, {"i": {"x": 0.833, "y": 0.85}, "o": {"x": 0.167, "y": 0.189}, "t": 129, "s": [221.922, 138.7, 0], "to": [0, -0.224, 0], "ti": [0, 0.179, 0]}, {"i": {"x": 0.833, "y": 0.849}, "o": {"x": 0.167, "y": 0.187}, "t": 130, "s": [221.922, 138.106, 0], "to": [0, -0.179, 0], "ti": [0, 0.144, 0]}, {"i": {"x": 0.833, "y": 0.849}, "o": {"x": 0.167, "y": 0.186}, "t": 131, "s": [221.922, 137.628, 0], "to": [0, -0.144, 0], "ti": [0, 0.117, 0]}, {"i": {"x": 0.833, "y": 0.85}, "o": {"x": 0.167, "y": 0.187}, "t": 132, "s": [221.922, 137.24, 0], "to": [0, -0.117, 0], "ti": [0, 0.094, 0]}, {"i": {"x": 0.833, "y": 0.852}, "o": {"x": 0.167, "y": 0.188}, "t": 133, "s": [221.922, 136.928, 0], "to": [0, -0.094, 0], "ti": [0, 0.074, 0]}, {"i": {"x": 0.833, "y": 0.854}, "o": {"x": 0.167, "y": 0.19}, "t": 134, "s": [221.922, 136.679, 0], "to": [0, -0.074, 0], "ti": [0, 0.057, 0]}, {"i": {"x": 0.833, "y": 0.856}, "o": {"x": 0.167, "y": 0.193}, "t": 135, "s": [221.922, 136.484, 0], "to": [0, -0.057, 0], "ti": [0, 0.042, 0]}, {"i": {"x": 0.833, "y": 0.861}, "o": {"x": 0.167, "y": 0.199}, "t": 136, "s": [221.922, 136.337, 0], "to": [0, -0.042, 0], "ti": [0, 0.03, 0]}, {"i": {"x": 0.833, "y": 0.87}, "o": {"x": 0.167, "y": 0.208}, "t": 137, "s": [221.922, 136.231, 0], "to": [0, -0.03, 0], "ti": [0, 0.018, 0]}, {"i": {"x": 0.833, "y": 0.89}, "o": {"x": 0.167, "y": 0.231}, "t": 138, "s": [221.922, 136.16, 0], "to": [0, -0.018, 0], "ti": [0, 0.009, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.347}, "t": 139, "s": [221.922, 136.12, 0], "to": [0, -0.009, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.653}, "o": {"x": 0.167, "y": 0.167}, "t": 140, "s": [221.922, 136.107, 0], "to": [0, 0, 0], "ti": [0, -0.009, 0]}, {"i": {"x": 0.833, "y": 0.769}, "o": {"x": 0.167, "y": 0.11}, "t": 141, "s": [221.922, 136.12, 0], "to": [0, 0.009, 0], "ti": [0, -0.018, 0]}, {"i": {"x": 0.833, "y": 0.792}, "o": {"x": 0.167, "y": 0.13}, "t": 142, "s": [221.922, 136.16, 0], "to": [0, 0.018, 0], "ti": [0, -0.03, 0]}, {"i": {"x": 0.833, "y": 0.801}, "o": {"x": 0.167, "y": 0.139}, "t": 143, "s": [221.922, 136.231, 0], "to": [0, 0.03, 0], "ti": [0, -0.042, 0]}, {"i": {"x": 0.833, "y": 0.807}, "o": {"x": 0.167, "y": 0.144}, "t": 144, "s": [221.922, 136.337, 0], "to": [0, 0.042, 0], "ti": [0, -0.057, 0]}, {"i": {"x": 0.833, "y": 0.81}, "o": {"x": 0.167, "y": 0.146}, "t": 145, "s": [221.922, 136.484, 0], "to": [0, 0.057, 0], "ti": [0, -0.074, 0]}, {"i": {"x": 0.833, "y": 0.812}, "o": {"x": 0.167, "y": 0.148}, "t": 146, "s": [221.922, 136.679, 0], "to": [0, 0.074, 0], "ti": [0, -0.094, 0]}, {"i": {"x": 0.833, "y": 0.813}, "o": {"x": 0.167, "y": 0.15}, "t": 147, "s": [221.922, 136.928, 0], "to": [0, 0.094, 0], "ti": [0, -0.117, 0]}, {"i": {"x": 0.833, "y": 0.814}, "o": {"x": 0.167, "y": 0.151}, "t": 148, "s": [221.922, 137.24, 0], "to": [0, 0.117, 0], "ti": [0, -0.144, 0]}, {"i": {"x": 0.833, "y": 0.813}, "o": {"x": 0.167, "y": 0.151}, "t": 149, "s": [221.922, 137.628, 0], "to": [0, 0.144, 0], "ti": [0, -0.179, 0]}, {"i": {"x": 0.833, "y": 0.811}, "o": {"x": 0.167, "y": 0.15}, "t": 150, "s": [221.922, 138.106, 0], "to": [0, 0.179, 0], "ti": [0, -0.224, 0]}, {"i": {"x": 0.833, "y": 0.808}, "o": {"x": 0.167, "y": 0.149}, "t": 151, "s": [221.922, 138.7, 0], "to": [0, 0.224, 0], "ti": [0, -0.288, 0]}, {"i": {"x": 0.833, "y": 0.805}, "o": {"x": 0.167, "y": 0.147}, "t": 152, "s": [221.922, 139.45, 0], "to": [0, 0.288, 0], "ti": [0, -0.383, 0]}, {"i": {"x": 0.833, "y": 0.799}, "o": {"x": 0.167, "y": 0.145}, "t": 153, "s": [221.922, 140.429, 0], "to": [0, 0.383, 0], "ti": [0, -0.53, 0]}, {"i": {"x": 0.833, "y": 0.796}, "o": {"x": 0.167, "y": 0.142}, "t": 154, "s": [221.922, 141.746, 0], "to": [0, 0.53, 0], "ti": [0, -0.761, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.141}, "t": 155, "s": [221.922, 143.609, 0], "to": [0, 0.761, 0], "ti": [0, -0.904, 0]}, {"i": {"x": 0.833, "y": 0.874}, "o": {"x": 0.167, "y": 0.166}, "t": 156, "s": [221.922, 146.313, 0], "to": [0, 0.904, 0], "ti": [0, -0.685, 0]}, {"i": {"x": 0.833, "y": 0.884}, "o": {"x": 0.167, "y": 0.246}, "t": 157, "s": [221.922, 149.032, 0], "to": [0, 0.685, 0], "ti": [0, -0.324, 0]}, {"i": {"x": 0.833, "y": 0.896}, "o": {"x": 0.167, "y": 0.295}, "t": 158, "s": [221.922, 150.425, 0], "to": [0, 0.324, 0], "ti": [0, -0.114, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.427}, "t": 159, "s": [221.922, 150.974, 0], "to": [0, 0.114, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.573}, "o": {"x": 0.167, "y": 0.167}, "t": 160, "s": [221.922, 151.107, 0], "to": [0, 0, 0], "ti": [0, 0.114, 0]}, {"i": {"x": 0.833, "y": 0.705}, "o": {"x": 0.167, "y": 0.104}, "t": 161, "s": [221.922, 150.974, 0], "to": [0, -0.114, 0], "ti": [0, 0.324, 0]}, {"i": {"x": 0.833, "y": 0.754}, "o": {"x": 0.167, "y": 0.116}, "t": 162, "s": [221.922, 150.425, 0], "to": [0, -0.324, 0], "ti": [0, 0.685, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.126}, "t": 163, "s": [221.922, 149.032, 0], "to": [0, -0.685, 0], "ti": [0, 0.904, 0]}, {"i": {"x": 0.833, "y": 0.859}, "o": {"x": 0.167, "y": 0.167}, "t": 164, "s": [221.922, 146.313, 0], "to": [0, -0.904, 0], "ti": [0, 0.761, 0]}, {"i": {"x": 0.833, "y": 0.858}, "o": {"x": 0.167, "y": 0.204}, "t": 165, "s": [221.922, 143.609, 0], "to": [0, -0.761, 0], "ti": [0, 0.53, 0]}, {"i": {"x": 0.833, "y": 0.855}, "o": {"x": 0.167, "y": 0.201}, "t": 166, "s": [221.922, 141.746, 0], "to": [0, -0.53, 0], "ti": [0, 0.383, 0]}, {"i": {"x": 0.833, "y": 0.853}, "o": {"x": 0.167, "y": 0.195}, "t": 167, "s": [221.922, 140.429, 0], "to": [0, -0.383, 0], "ti": [0, 0.288, 0]}, {"i": {"x": 0.833, "y": 0.851}, "o": {"x": 0.167, "y": 0.192}, "t": 168, "s": [221.922, 139.45, 0], "to": [0, -0.288, 0], "ti": [0, 0.224, 0]}, {"i": {"x": 0.833, "y": 0.85}, "o": {"x": 0.167, "y": 0.189}, "t": 169, "s": [221.922, 138.7, 0], "to": [0, -0.224, 0], "ti": [0, 0.179, 0]}, {"i": {"x": 0.833, "y": 0.849}, "o": {"x": 0.167, "y": 0.187}, "t": 170, "s": [221.922, 138.106, 0], "to": [0, -0.179, 0], "ti": [0, 0.144, 0]}, {"i": {"x": 0.833, "y": 0.849}, "o": {"x": 0.167, "y": 0.186}, "t": 171, "s": [221.922, 137.628, 0], "to": [0, -0.144, 0], "ti": [0, 0.117, 0]}, {"i": {"x": 0.833, "y": 0.85}, "o": {"x": 0.167, "y": 0.187}, "t": 172, "s": [221.922, 137.24, 0], "to": [0, -0.117, 0], "ti": [0, 0.094, 0]}, {"i": {"x": 0.833, "y": 0.852}, "o": {"x": 0.167, "y": 0.188}, "t": 173, "s": [221.922, 136.928, 0], "to": [0, -0.094, 0], "ti": [0, 0.074, 0]}, {"i": {"x": 0.833, "y": 0.854}, "o": {"x": 0.167, "y": 0.19}, "t": 174, "s": [221.922, 136.679, 0], "to": [0, -0.074, 0], "ti": [0, 0.057, 0]}, {"i": {"x": 0.833, "y": 0.856}, "o": {"x": 0.167, "y": 0.193}, "t": 175, "s": [221.922, 136.484, 0], "to": [0, -0.057, 0], "ti": [0, 0.042, 0]}, {"i": {"x": 0.833, "y": 0.861}, "o": {"x": 0.167, "y": 0.199}, "t": 176, "s": [221.922, 136.337, 0], "to": [0, -0.042, 0], "ti": [0, 0.03, 0]}, {"i": {"x": 0.833, "y": 0.87}, "o": {"x": 0.167, "y": 0.208}, "t": 177, "s": [221.922, 136.231, 0], "to": [0, -0.03, 0], "ti": [0, 0.018, 0]}, {"i": {"x": 0.833, "y": 0.89}, "o": {"x": 0.167, "y": 0.231}, "t": 178, "s": [221.922, 136.16, 0], "to": [0, -0.018, 0], "ti": [0, 0.009, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.347}, "t": 179, "s": [221.922, 136.12, 0], "to": [0, -0.009, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.653}, "o": {"x": 0.167, "y": 0.167}, "t": 180, "s": [221.922, 136.107, 0], "to": [0, 0, 0], "ti": [0, -0.009, 0]}, {"i": {"x": 0.833, "y": 0.769}, "o": {"x": 0.167, "y": 0.11}, "t": 181, "s": [221.922, 136.12, 0], "to": [0, 0.009, 0], "ti": [0, -0.018, 0]}, {"i": {"x": 0.833, "y": 0.792}, "o": {"x": 0.167, "y": 0.13}, "t": 182, "s": [221.922, 136.16, 0], "to": [0, 0.018, 0], "ti": [0, -0.03, 0]}, {"i": {"x": 0.833, "y": 0.801}, "o": {"x": 0.167, "y": 0.139}, "t": 183, "s": [221.922, 136.231, 0], "to": [0, 0.03, 0], "ti": [0, -0.042, 0]}, {"i": {"x": 0.833, "y": 0.807}, "o": {"x": 0.167, "y": 0.144}, "t": 184, "s": [221.922, 136.337, 0], "to": [0, 0.042, 0], "ti": [0, -0.057, 0]}, {"i": {"x": 0.833, "y": 0.81}, "o": {"x": 0.167, "y": 0.146}, "t": 185, "s": [221.922, 136.484, 0], "to": [0, 0.057, 0], "ti": [0, -0.074, 0]}, {"i": {"x": 0.833, "y": 0.812}, "o": {"x": 0.167, "y": 0.148}, "t": 186, "s": [221.922, 136.679, 0], "to": [0, 0.074, 0], "ti": [0, -0.094, 0]}, {"i": {"x": 0.833, "y": 0.813}, "o": {"x": 0.167, "y": 0.15}, "t": 187, "s": [221.922, 136.928, 0], "to": [0, 0.094, 0], "ti": [0, -0.117, 0]}, {"i": {"x": 0.833, "y": 0.814}, "o": {"x": 0.167, "y": 0.151}, "t": 188, "s": [221.922, 137.24, 0], "to": [0, 0.117, 0], "ti": [0, -0.144, 0]}, {"i": {"x": 0.833, "y": 0.813}, "o": {"x": 0.167, "y": 0.151}, "t": 189, "s": [221.922, 137.628, 0], "to": [0, 0.144, 0], "ti": [0, -0.179, 0]}, {"i": {"x": 0.833, "y": 0.811}, "o": {"x": 0.167, "y": 0.15}, "t": 190, "s": [221.922, 138.106, 0], "to": [0, 0.179, 0], "ti": [0, -0.224, 0]}, {"i": {"x": 0.833, "y": 0.808}, "o": {"x": 0.167, "y": 0.149}, "t": 191, "s": [221.922, 138.7, 0], "to": [0, 0.224, 0], "ti": [0, -0.288, 0]}, {"i": {"x": 0.833, "y": 0.805}, "o": {"x": 0.167, "y": 0.147}, "t": 192, "s": [221.922, 139.45, 0], "to": [0, 0.288, 0], "ti": [0, -0.383, 0]}, {"i": {"x": 0.833, "y": 0.799}, "o": {"x": 0.167, "y": 0.145}, "t": 193, "s": [221.922, 140.429, 0], "to": [0, 0.383, 0], "ti": [0, -0.53, 0]}, {"i": {"x": 0.833, "y": 0.796}, "o": {"x": 0.167, "y": 0.142}, "t": 194, "s": [221.922, 141.746, 0], "to": [0, 0.53, 0], "ti": [0, -0.761, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.141}, "t": 195, "s": [221.922, 143.609, 0], "to": [0, 0.761, 0], "ti": [0, -0.904, 0]}, {"i": {"x": 0.833, "y": 0.874}, "o": {"x": 0.167, "y": 0.166}, "t": 196, "s": [221.922, 146.313, 0], "to": [0, 0.904, 0], "ti": [0, -0.685, 0]}, {"i": {"x": 0.833, "y": 0.884}, "o": {"x": 0.167, "y": 0.246}, "t": 197, "s": [221.922, 149.032, 0], "to": [0, 0.685, 0], "ti": [0, -0.324, 0]}, {"i": {"x": 0.833, "y": 0.896}, "o": {"x": 0.167, "y": 0.295}, "t": 198, "s": [221.922, 150.425, 0], "to": [0, 0.324, 0], "ti": [0, -0.114, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.427}, "t": 199, "s": [221.922, 150.974, 0], "to": [0, 0.114, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.573}, "o": {"x": 0.167, "y": 0.167}, "t": 200, "s": [221.922, 151.107, 0], "to": [0, 0, 0], "ti": [0, 0.114, 0]}, {"i": {"x": 0.833, "y": 0.705}, "o": {"x": 0.167, "y": 0.104}, "t": 201, "s": [221.922, 150.974, 0], "to": [0, -0.114, 0], "ti": [0, 0.324, 0]}, {"i": {"x": 0.833, "y": 0.754}, "o": {"x": 0.167, "y": 0.116}, "t": 202, "s": [221.922, 150.425, 0], "to": [0, -0.324, 0], "ti": [0, 0.685, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.126}, "t": 203, "s": [221.922, 149.032, 0], "to": [0, -0.685, 0], "ti": [0, 0.904, 0]}, {"i": {"x": 0.833, "y": 0.859}, "o": {"x": 0.167, "y": 0.167}, "t": 204, "s": [221.922, 146.313, 0], "to": [0, -0.904, 0], "ti": [0, 0.761, 0]}, {"i": {"x": 0.833, "y": 0.858}, "o": {"x": 0.167, "y": 0.204}, "t": 205, "s": [221.922, 143.609, 0], "to": [0, -0.761, 0], "ti": [0, 0.53, 0]}, {"i": {"x": 0.833, "y": 0.855}, "o": {"x": 0.167, "y": 0.201}, "t": 206, "s": [221.922, 141.746, 0], "to": [0, -0.53, 0], "ti": [0, 0.383, 0]}, {"i": {"x": 0.833, "y": 0.853}, "o": {"x": 0.167, "y": 0.195}, "t": 207, "s": [221.922, 140.429, 0], "to": [0, -0.383, 0], "ti": [0, 0.288, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.192}, "t": 208, "s": [221.922, 139.45, 0], "to": [0, -0.288, 0], "ti": [0, 0.125, 0]}, {"t": 209.000008512745, "s": [221.922, 138.7, 0]}], "ix": 2}, "a": {"a": 0, "k": [16.16, 30.322, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0]], "o": [[0, 0]], "v": [[11.862, 38.434]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.203921583587, 0.223529426724, 0.588235294118, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 2, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1.682, -4.877], [2.357, -4.487], [-1.671, 4.877], [-2.357, 4.481]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [20.715, 40.952], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 3, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-0.002, 0.638], [0, 0], [0.511, 0.295], [0.002, -0.625], [0, 0], [-0.523, -0.302]], "o": [[0, 0], [0.002, -0.625], [-0.523, -0.301], [0, 0], [-0.002, 0.638], [0.511, 0.295]], "v": [[0.719, 2.325], [0.729, -1.477], [0.003, -2.962], [-0.719, -2.313], [-0.73, 1.489], [-0.014, 2.955]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0.99, 0.572], [-0.003, 1.125], [0, 0], [-0.98, -0.566], [0.003, -1.113]], "o": [[-0.003, 1.126], [-0.98, -0.566], [0, 0], [0.003, -1.113], [0.99, 0.572], [0, 0]], "v": [[1.447, 2.683], [-0.016, 3.718], [-1.458, 1.007], [-1.449, -2.684], [0.005, -3.725], [1.458, -1.006]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 2", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [18.267, 37.355], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 5, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-0.001, 0.625], [0, 0], [0.501, 0.289], [0.002, -0.638], [0, 0], [-0.512, -0.295]], "o": [[0, 0], [0.002, -0.638], [-0.511, -0.295], [0, 0], [-0.001, 0.626], [0.5, 0.289]], "v": [[0.719, 2.307], [0.729, -1.483], [0.014, -2.949], [-0.719, -2.319], [-0.73, 1.471], [-0.003, 2.955]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[-0.991, -0.572], [0.003, -1.114], [0, 0], [1.002, 0.578], [-0.003, 1.138], [0, 0]], "o": [[1.001, 0.579], [0, 0], [-0.003, 1.138], [-0.99, -0.572], [0, 0], [0.004, -1.113]], "v": [[0.016, -3.712], [1.458, -1.012], [1.449, 2.653], [-0.006, 3.706], [-1.458, 0.975], [-1.449, -2.69]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 2", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [23.24, 44.592], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 5, "cix": 2, "bm": 0, "ix": 4, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [-0.686, -0.396], [0.005, -1.663], [0, 0], [1.546, 0.892], [-0.005, 1.789], [0, 0], [0, 0], [0, 0], [-0.816, -0.471], [-0.003, 1.05], [0, 0], [0.806, 0.465], [0.1, -0.794], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0.329, -0.449], [1.307, 0.754], [0, 0], [-0.005, 1.789], [-1.557, -0.899], [0, 0], [0, 0], [0, 0], [-0.003, 1.05], [0.806, 0.465], [0, 0], [0.003, -1.051], [-0.62, -0.358], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[2.058, -4.597], [2.055, -3.433], [-0.928, -5.156], [-1.102, -1.66], [0.414, -1.748], [2.289, 2.102], [2.283, 4.38], [-0.011, 6.011], [-2.289, 1.74], [-2.287, 0.964], [-1.22, 1.58], [-1.222, 2.443], [-0.041, 4.817], [1.151, 3.813], [1.157, 1.55], [-0.025, -0.825], [-1.171, -0.234], [-1.172, 0.028], [-2.239, -0.587], [-1.937, -6.903]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [13.481, 36.712], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 4", "np": 3, "cix": 2, "bm": 0, "ix": 5, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[2.299, -3.756], [2.296, -2.617], [-0.348, 6.408], [-1.48, 5.753], [1.152, -3.253], [-2.299, -5.245], [-2.296, -6.408]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [7.874, 32.772], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 5", "np": 3, "cix": 2, "bm": 0, "ix": 6, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -0.272], [0, 0], [0.506, 0.292], [0, 0], [-0.001, 0.273], [0, 0], [-0.507, -0.292], [0, 0]], "o": [[0, 0], [-0.001, 0.584], [0, 0], [-0.236, -0.136], [0, 0], [0.002, -0.584], [0, 0], [0.235, 0.137]], "v": [[10.521, 5.138], [10.518, 6.134], [9.377, 6.79], [-10.139, -4.478], [-10.52, -5.138], [-10.517, -6.133], [-9.375, -6.79], [10.141, 4.478]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.677999997606, 0.804000016755, 0.987999949736, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [15.653, 24.267], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 6", "np": 3, "cix": 2, "bm": 0, "ix": 7, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.001, -0.271], [0, 0], [0.506, 0.294], [0, 0], [-0.001, 0.272], [0, 0], [-0.506, -0.294], [0, 0]], "o": [[0, 0], [-0.002, 0.586], [0, 0], [-0.235, -0.137], [0, 0], [0.001, -0.585], [0, 0], [0.235, 0.137]], "v": [[3.104, 0.864], [3.102, 1.857], [1.959, 2.513], [-2.725, -0.204], [-3.104, -0.865], [-3.101, -1.858], [-1.959, -2.513], [2.725, 0.204]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [8.251, 15.05], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 7", "np": 3, "cix": 2, "bm": 0, "ix": 8, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0.007, -2.316], [0, 0], [2.016, 1.164], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.006, 2.316], [0, 0], [-2.016, -1.164]], "o": [[2.016, 1.164], [0, 0], [-0.006, 2.316], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-2.017, -1.164], [0, 0], [0.007, -2.317], [0, 0]], "v": [[12.259, -14.786], [15.903, -8.474], [15.863, 26.818], [12.195, 28.908], [2.354, 23.225], [1.875, 23.892], [-0.046, 26.596], [-1.951, 21.684], [-2.424, 20.467], [-12.265, 14.785], [-15.904, 8.477], [-15.864, -26.814], [-12.202, -28.908]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.344999994016, 0.685999971278, 0.984000052658, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [16.159, 30.322], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 8", "np": 3, "cix": 2, "bm": 0, "ix": 9, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 94.0000038286985, "st": 0, "bm": 0}, {"ddd": 0, "ind": 18, "ty": 4, "nm": "Layer 20 Outlines", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [129.446, 253.201, 0], "ix": 2}, "a": {"a": 0, "k": [18.067, 10.646, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-3.464, -2.115], [0, 0], [-5.404, 3.68]], "o": [[-2.998, 2.258], [0, 0], [-5.899, -3.526], [0, 0]], "v": [[4.57, -3.798], [5.273, 3.811], [0.885, 6.361], [0.131, -6.361]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.243000000598, 0.670999983245, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [5.523, 10.864], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0.519, -0.301], [6.435, 3.427], [0, 0], [-3.783, 2.199], [-0.384, 0.35]], "o": [[-0.434, 0.329], [-6.206, 3.608], [0, 0], [3.991, 2.021], [0.526, -0.305], [0, 0]], "v": [[12.033, -1.029], [10.605, -0.082], [-12.033, 0.189], [-7.64, -2.364], [6.191, -2.63], [7.553, -3.616]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.243000000598, 0.670999983245, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [19.133, 17.426], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 3, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [4.149, -3.58], [0, 0], [2.788, 1.834]], "o": [[5.213, 3.264], [0, 0], [1.827, -2.093], [0, 0]], "v": [[-0.679, -5.854], [0.924, 5.854], [-3.63, 3.225], [-5.073, -3.299]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.486000001197, 0.8, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [30.811, 10.084], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 3, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-6.421, -3.582], [0, 0], [3.875, -2.252], [0.077, -0.048], [0, 0], [-0.08, 0.046]], "o": [[0, 0], [-3.983, -2.174], [-0.08, 0.046], [0, 0], [0.078, -0.047], [6.297, -3.66]], "v": [[11.577, 0.387], [7.189, 2.937], [-6.928, 3.053], [-7.16, 3.195], [-11.577, 0.644], [-11.342, 0.504]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.486000001197, 0.8, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [17.878, 3.445], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 4", "np": 3, "cix": 2, "bm": 0, "ix": 4, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 94.0000038286985, "st": 0, "bm": 0}, {"ddd": 0, "ind": 19, "ty": 4, "nm": "Layer 21 Outlines", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [144.905, 266.292, 0], "ix": 2}, "a": {"a": 0, "k": [18.652, 9.096, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-0.538, -0.313], [-0.542, 0.313], [0, 0], [0.539, 0.313], [0.542, -0.313]], "o": [[-0.542, 0.312], [0.539, 0.313], [0, 0], [0.541, -0.312], [-0.538, -0.313], [0, 0]], "v": [[-10.13, 4.728], [-10.147, 5.858], [-8.195, 5.852], [10.141, -4.734], [10.146, -5.858], [8.205, -5.858]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.677999997606, 0.804000016755, 0.987999949736, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [26.368, 10.005], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-0.529, -0.307], [-0.532, 0.307], [0, 0], [0.538, 0.313], [0.542, -0.313]], "o": [[-0.532, 0.307], [0.538, 0.313], [0, 0], [0.543, -0.312], [-0.529, -0.307], [0, 0]], "v": [[-6.835, 2.821], [-6.842, 3.945], [-4.891, 3.951], [6.827, -2.815], [6.824, -3.945], [4.882, -3.945]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.677999997606, 0.804000016755, 0.987999949736, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [26.708, 6.358], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 3, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-0.539, -0.313], [-0.543, 0.312], [0, 0], [0.539, 0.313], [0.543, -0.314]], "o": [[-0.542, 0.313], [0.538, 0.312], [0, 0], [0.543, -0.314], [-0.539, -0.314], [0, 0]], "v": [[-14.774, 7.41], [-14.781, 8.535], [-12.839, 8.535], [14.773, -7.408], [14.781, -8.533], [12.839, -8.533]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.677999997606, 0.804000016755, 0.987999949736, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [15.569, 9.097], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 3, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 94.0000038286985, "st": 0, "bm": 0}, {"ddd": 0, "ind": 20, "ty": 4, "nm": "Layer 22 Outlines", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [152.697, 284.684, 0], "ix": 2}, "a": {"a": 0, "k": [11.279, 7.805, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "hasMask": true, "masksProperties": [{"inv": false, "mode": "a", "pt": {"a": 1, "k": [{"i": {"x": 0.342, "y": 0.851}, "o": {"x": 0.333, "y": 0}, "t": 1, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[8.397, -2.784], [7.128, -2.53], [-2.705, 4.132], [-3.403, 5.591]], "c": true}]}, {"t": 23.0000009368092, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[45.883, 22.109], [7.128, -2.53], [-2.705, 4.132], [34.527, 28.834]], "c": true}]}], "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "Mask 1"}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[11.026, 3.219], [5.592, 6.376], [-11.026, -3.218], [-5.593, -6.376]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.238999998803, 0.616000007181, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [11.283, 6.626], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[8.313, 3.619], [8.305, 5.976], [-8.313, -3.618], [-8.307, -5.976]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.933000033509, 0.258999992819, 0.54900004069, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [8.563, 9.383], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[2.72, -2.758], [2.713, -0.4], [-2.72, 2.758], [-2.714, 0.4]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.933000033509, 0.172999991623, 0.416000007181, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [19.589, 12.603], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 2, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 94.0000038286985, "st": 0, "bm": 0}, {"ddd": 0, "ind": 21, "ty": 4, "nm": "Layer 23 Outlines", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [173.179, 284.191, 0], "ix": 2}, "a": {"a": 0, "k": [21.129, 13.492, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "hasMask": true, "masksProperties": [{"inv": false, "mode": "a", "pt": {"a": 1, "k": [{"i": {"x": 0, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 7, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[8.397, -2.784], [7.128, -2.53], [-2.705, 4.132], [-3.403, 5.591]], "c": true}]}, {"t": 35.0000014255792, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[45.883, 22.109], [7.128, -2.53], [-2.705, 4.132], [34.527, 28.834]], "c": true}]}], "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "Mask 1"}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[20.876, 8.905], [15.441, 12.063], [-20.876, -8.905], [-15.441, -12.063]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.344999994016, 0.685999971278, 0.984000052658, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [21.133, 12.313], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[18.162, 9.305], [18.155, 11.663], [-18.162, -9.305], [-18.155, -11.663]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.20800000359, 0.282000014361, 0.859000052658, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [18.412, 15.071], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[2.721, -2.758], [2.714, -0.401], [-2.721, 2.758], [-2.714, 0.4]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.149000010771, 0.536999990426, 0.991999966491, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [39.288, 23.976], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 2, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 94.0000038286985, "st": 0, "bm": 0}, {"ddd": 1, "ind": 22, "ty": 4, "nm": "Layer 24 Outlines", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "rx": {"a": 0, "k": 0, "ix": 8}, "ry": {"a": 0, "k": 0, "ix": 9}, "rz": {"a": 0, "k": 0, "ix": 10}, "or": {"a": 0, "k": [0, 0, 0], "ix": 7}, "p": {"a": 0, "k": [188.827, 278.565, 10.485], "ix": 2}, "a": {"a": 0, "k": [26.148, 14.044, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "hasMask": true, "masksProperties": [{"inv": false, "mode": "a", "pt": {"a": 1, "k": [{"i": {"x": 0, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 22, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[8.397, -2.784], [7.128, -2.53], [-2.705, 4.132], [-3.403, 5.591]], "c": true}]}, {"t": 60.0000024438501, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[57.883, 27.859], [7.128, -2.53], [-2.705, 4.132], [46.527, 34.584]], "c": true}]}], "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "Mask 1"}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[25.835, 11.766], [20.398, 14.926], [-25.835, -11.766], [-20.398, -14.926]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.475, 0.455000005984, 0.961000031116, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [26.093, 15.176], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[23.12, 12.167], [23.114, 14.525], [-23.12, -12.168], [-23.113, -14.525]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.246999987434, 0.035000000748, 0.583999992819, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [23.37, 17.935], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[2.722, -2.759], [2.716, -0.402], [-2.722, 2.759], [-2.715, 0.401]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.328999986836, 0.046999998654, 0.779999976065, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [49.206, 29.701], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 2, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 94.0000038286985, "st": 0, "bm": 0}, {"ddd": 0, "ind": 23, "ty": 4, "nm": "Layer 25 Outlines", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [184.918, 224.602, 0], "ix": 2}, "a": {"a": 0, "k": [33.674, 19.622, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-0.342, -0.198], [-0.344, 0.198], [0, 0], [0.342, 0.199], [0.345, -0.199]], "o": [[-0.344, 0.199], [0.342, 0.199], [0, 0], [0.344, -0.199], [-0.342, -0.199], [0, 0]], "v": [[-6.427, 2.999], [-6.437, 3.716], [-5.199, 3.714], [6.433, -3.003], [6.437, -3.716], [5.205, -3.716]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.019999999626, 0.8, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [45.567, 23.907], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-0.335, -0.194], [-0.338, 0.195], [0, 0], [0.342, 0.199], [0.343, -0.199]], "o": [[-0.337, 0.195], [0.342, 0.199], [0, 0], [0.345, -0.198], [-0.335, -0.195], [0, 0]], "v": [[-4.337, 1.789], [-4.341, 2.503], [-3.103, 2.507], [4.331, -1.785], [4.329, -2.503], [3.098, -2.503]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.830999995213, 0.851000019148, 0.875, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [45.783, 21.593], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 3, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-0.342, -0.198], [-0.344, 0.199], [0, 0], [0.342, 0.198], [0.344, -0.199]], "o": [[-0.344, 0.199], [0.341, 0.199], [0, 0], [0.344, -0.199], [-0.341, -0.199], [0, 0]], "v": [[-9.374, 4.7], [-9.377, 5.413], [-8.146, 5.413], [9.374, -4.7], [9.377, -5.413], [8.146, -5.413]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.677999997606, 0.804000016755, 0.987999949736, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [38.716, 23.331], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 3, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [4.123, -2.396], [4.149, 2.395], [-2.112, 2.415], [0, 0]], "o": [[4.149, 2.396], [-4.123, 2.395], [-3.32, -1.917], [0, 0], [0, 0]], "v": [[7.43, -5.536], [7.478, 3.141], [-7.501, 3.141], [-9.488, -3.983], [-0.036, -1.198]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.019999999626, 0.8, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [27.59, 16.515], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 4", "np": 3, "cix": 2, "bm": 0, "ix": 4, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-0.687, -0.396], [0, 0], [0, 0], [-4.318, -1.273]], "o": [[0, 0], [0, 0], [2.182, -2.496], [0.864, 0.254]], "v": [[6.997, -0.691], [0.822, 2.897], [-6.997, 0.593], [4.773, -1.624]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.702000038297, 0.569000004787, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [26.608, 11.204], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 5", "np": 3, "cix": 2, "bm": 0, "ix": 5, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-33.424, -3.434], [5.819, 19.372], [33.424, 3.435], [-5.82, -19.372]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [33.674, 19.622], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 6", "np": 2, "cix": 2, "bm": 0, "ix": 6, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 94.0000038286985, "st": 0, "bm": 0}, {"ddd": 0, "ind": 24, "ty": 4, "nm": "Layer 27 Outlines", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [272.089, 284.376, 0], "ix": 2}, "a": {"a": 0, "k": [33.674, 19.622, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-0.342, -0.198], [-0.345, 0.198], [0, 0], [0.342, 0.198], [0.344, -0.199]], "o": [[-0.344, 0.198], [0.342, 0.199], [0, 0], [0.344, -0.198], [-0.342, -0.199], [0, 0]], "v": [[-6.427, 2.999], [-6.437, 3.717], [-5.198, 3.714], [6.433, -3.003], [6.437, -3.716], [5.206, -3.716]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.830999995213, 0.851000019148, 0.875, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [45.567, 23.906], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-0.335, -0.194], [-0.338, 0.195], [0, 0], [0.342, 0.198], [0.343, -0.199]], "o": [[-0.337, 0.194], [0.342, 0.199], [0, 0], [0.344, -0.198], [-0.335, -0.195], [0, 0]], "v": [[-4.337, 1.79], [-4.341, 2.503], [-3.103, 2.507], [4.332, -1.786], [4.329, -2.503], [3.098, -2.503]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.830999995213, 0.851000019148, 0.875, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [45.782, 21.593], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 3, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-0.342, -0.198], [-0.344, 0.198], [0, 0], [0.342, 0.199], [0.344, -0.199]], "o": [[-0.345, 0.199], [0.342, 0.198], [0, 0], [0.344, -0.2], [-0.342, -0.199], [0, 0]], "v": [[-9.372, 4.701], [-9.377, 5.415], [-8.144, 5.415], [9.373, -4.7], [9.378, -5.414], [8.145, -5.414]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.677999997606, 0.804000016755, 0.987999949736, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [38.715, 23.33], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 3, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [4.123, -2.396], [4.151, 2.396], [-2.111, 2.415], [0, 0]], "o": [[4.149, 2.396], [-4.122, 2.395], [-3.319, -1.917], [0, 0], [0, 0]], "v": [[7.429, -5.537], [7.477, 3.141], [-7.502, 3.141], [-9.489, -3.983], [-0.037, -1.198]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.702000038297, 0.569000004787, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [27.59, 16.515], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 4", "np": 3, "cix": 2, "bm": 0, "ix": 4, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-0.687, -0.396], [0, 0], [0, 0], [-4.318, -1.273]], "o": [[0, 0], [0, 0], [2.182, -2.497], [0.864, 0.254]], "v": [[6.997, -0.691], [0.821, 2.897], [-6.997, 0.593], [4.773, -1.624]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.337000020345, 0.40800000359, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [26.608, 11.203], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 5", "np": 3, "cix": 2, "bm": 0, "ix": 5, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-33.424, -3.434], [5.819, 19.372], [33.424, 3.434], [-5.82, -19.372]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [33.674, 19.622], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 6", "np": 2, "cix": 2, "bm": 0, "ix": 6, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 94.0000038286985, "st": 0, "bm": 0}, {"ddd": 0, "ind": 25, "ty": 4, "nm": "Layer 28 Outlines", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [237.464, 267.713, 0], "ix": 2}, "a": {"a": 0, "k": [162.445, 103.738, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [6.841, -3.976], [0, 0], [6.886, 3.975], [0, 0], [-6.841, 3.976], [0, 0], [-6.885, -3.975]], "o": [[6.886, 3.975], [0, 0], [-6.841, 3.975], [0, 0], [-6.886, -3.976], [0, 0], [6.841, -3.975], [0, 0]], "v": [[155.273, -7.199], [155.354, 7.198], [12.933, 89.963], [-11.92, 89.963], [-155.273, 7.198], [-155.354, -7.198], [-12.933, -89.964], [11.92, -89.964]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.885999971278, 0.929000016755, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [162.445, 94.189], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.564, 0.08], [0.574, 0.119], [0.599, 0.171], [0.687, 0.272], [0.793, 0.458], [0, 0], [-0.007, 2.622], [0, 0], [-3.465, -2], [0, 0], [-0.893, -0.353], [-0.733, -0.209], [-0.622, -0.129], [-0.587, -0.083], [-0.574, -0.048], [0, 0]], "o": [[-0.587, -0.083], [-0.621, -0.129], [-0.733, -0.209], [-0.893, -0.352], [0, 0], [-3.464, -2], [0, 0], [-0.007, 2.622], [0, 0], [0.794, 0.458], [0.687, 0.271], [0.599, 0.17], [0.573, 0.118], [0.565, 0.08], [0, 0], [-0.573, -0.047]], "v": [[77.557, 41.179], [75.813, 40.876], [73.98, 40.427], [71.847, 39.706], [69.311, 38.491], [-74.042, -44.274], [-79.229, -51.522], [-79.257, -41.371], [-74.07, -34.123], [69.283, 48.642], [71.818, 49.858], [73.952, 50.578], [75.785, 51.028], [77.527, 51.33], [79.236, 51.522], [79.264, 41.37]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.677999997606, 0.804000016755, 0.987999949736, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [81.213, 145.259], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 3, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.016, -0.173], [0.033, -0.184], [0.04, -0.149], [0.061, -0.173], [0.062, -0.143], [0.098, -0.181], [0.08, -0.13], [0.151, -0.202], [0.168, -0.19], [0.041, -0.044], [0.263, -0.237], [0.184, -0.149], [0.174, -0.126], [0.214, -0.14], [0.237, -0.138], [0, 0], [1.38, -0.447], [1.145, -0.206], [0.756, -0.075], [0.652, -0.022], [0.6, 0.015], [0.574, 0.047], [0, 0], [-0.578, -0.014], [-0.599, 0.02], [-0.646, 0.064], [-0.737, 0.133], [-1.067, 0.346], [-1.173, 0.681], [0, 0], [-0.07, 0.042], [-0.152, 0.099], [-0.2, 0.145], [-0.161, 0.13], [-0.169, 0.152], [-0.034, 0.031], [-0.197, 0.216], [-0.04, 0.045], [-0.106, 0.135], [-0.044, 0.059], [-0.128, 0.206], [-0.049, 0.087], [-0.023, 0.044], [-0.079, 0.183], [-0.041, 0.108], [-0.014, 0.036], [-0.046, 0.176], [-0.023, 0.119], [-0.006, 0.031], [-0.017, 0.186], [-0.003, 0.12], [0, 0.052], [0, 0]], "o": [[-0.016, 0.184], [-0.029, 0.15], [-0.046, 0.176], [-0.051, 0.144], [-0.08, 0.184], [-0.07, 0.132], [-0.127, 0.206], [-0.146, 0.194], [-0.039, 0.045], [-0.227, 0.247], [-0.168, 0.153], [-0.162, 0.128], [-0.199, 0.146], [-0.22, 0.144], [0, 0], [-1.173, 0.682], [-1.067, 0.346], [-0.736, 0.133], [-0.646, 0.065], [-0.599, 0.021], [-0.578, -0.015], [0, 0], [0.575, 0.047], [0.6, 0.015], [0.652, -0.023], [0.756, -0.075], [1.145, -0.206], [1.381, -0.447], [0, 0], [0.072, -0.042], [0.16, -0.096], [0.216, -0.14], [0.173, -0.126], [0.185, -0.149], [0.033, -0.031], [0.225, -0.208], [0.042, -0.045], [0.116, -0.132], [0.046, -0.059], [0.151, -0.202], [0.052, -0.087], [0.024, -0.044], [0.098, -0.18], [0.047, -0.106], [0.013, -0.036], [0.061, -0.175], [0.031, -0.118], [0.007, -0.031], [0.034, -0.183], [0.01, -0.12], [0.002, -0.052], [0, 0], [0, 0.172]], "v": [[81.184, -51.01], [81.122, -50.458], [81.008, -50.008], [80.858, -49.484], [80.677, -49.055], [80.42, -48.508], [80.185, -48.117], [79.772, -47.504], [79.308, -46.925], [79.193, -46.79], [78.453, -46.064], [77.912, -45.616], [77.421, -45.23], [76.788, -44.807], [76.122, -44.378], [-66.299, 38.387], [-70.15, 40.081], [-73.478, 40.909], [-75.719, 41.221], [-77.668, 41.351], [-79.468, 41.36], [-81.199, 41.267], [-81.228, 51.419], [-79.497, 51.511], [-77.697, 51.503], [-75.748, 51.373], [-73.506, 51.06], [-70.179, 50.232], [-66.328, 48.539], [76.092, -34.227], [76.307, -34.354], [76.756, -34.654], [77.393, -35.078], [77.882, -35.465], [78.424, -35.912], [78.533, -36.002], [79.163, -36.639], [79.281, -36.775], [79.622, -37.174], [79.744, -37.353], [80.157, -37.966], [80.33, -38.224], [80.392, -38.357], [80.648, -38.904], [80.798, -39.223], [80.83, -39.332], [80.98, -39.858], [81.081, -40.212], [81.092, -40.307], [81.156, -40.861], [81.197, -41.22], [81.2, -41.375], [81.228, -51.526]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.677999997606, 0.804000016755, 0.987999949736, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [241.677, 145.362], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 3, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[11.113, 0], [0.036, 6.437], [-11.113, 0], [-0.036, -6.437]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.301999978458, 0.647000002394, 0.862999949736, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [129.611, 137.723], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 4", "np": 3, "cix": 2, "bm": 0, "ix": 4, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[5.665, -28.439], [5.486, 34.876], [-5.665, 28.44], [-5.484, -34.876]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.481999984442, 0.681999954523, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [123.983, 172.599], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 5", "np": 3, "cix": 2, "bm": 0, "ix": 5, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[5.628, -34.876], [5.449, 28.44], [-5.628, 34.877], [-5.449, -28.439]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.779999976065, 0.847000002394, 0.948999980852, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [135.096, 172.599], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 6", "np": 3, "cix": 2, "bm": 0, "ix": 6, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 94.0000038286985, "st": 0, "bm": 0}, {"ddd": 0, "ind": 26, "ty": 4, "nm": "Layer 29 Outlines", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [160.125, 229.671, 0], "ix": 2}, "a": {"a": 0, "k": [68.43, 110.407, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[5.396, -9.651], [0, -2.179], [-3.114, -7.471], [-6.849, -3.113], [-1.245, -0.934], [-5.397, -7.887], [-2.076, -6.642], [-5.302, 3.468], [0, 0], [2.491, 9.962], [4.567, 2.906], [3.321, 2.075], [1.752, 3.984]], "o": [[0, 0], [0, 2.179], [3.112, 7.472], [6.85, 3.114], [1.246, 0.934], [5.396, 7.887], [0, 0], [2.776, -1.816], [0, 0], [-2.491, -9.962], [-4.565, -2.905], [-3.32, -2.075], [-1.752, -3.983]], "v": [[-41.6, -41.791], [-40.978, -37.121], [-39.421, -21.244], [-8.6, -9.103], [3.542, -2.253], [11.947, 19.747], [28.551, 50.05], [39.758, 47.974], [42.249, 45.068], [28.967, 11.445], [19.003, -13.046], [-3.411, -28.404], [-17.432, -41.025]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.579999976065, 0.638999968884, 0.752999997606, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [81.596, 145.504], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[5.396, -9.651], [0, -2.179], [-3.113, -7.472], [-6.849, -3.113], [-1.246, -0.934], [-5.397, -7.887], [-2.075, -6.642], [-5.302, 3.468], [0, 0], [2.491, 9.963], [4.566, 2.905], [3.321, 2.075], [1.753, 3.983]], "o": [[0, 0], [0, 2.179], [3.113, 7.472], [6.85, 3.114], [1.245, 0.935], [5.396, 7.888], [0, 0], [2.776, -1.817], [0, 0], [-2.49, -9.962], [-4.566, -2.906], [-3.321, -2.075], [-1.752, -3.984]], "v": [[-41.6, -41.792], [-40.978, -37.122], [-39.422, -21.244], [-8.6, -9.103], [3.542, -2.254], [11.947, 19.746], [28.551, 50.049], [39.758, 47.974], [42.249, 45.067], [28.965, 11.444], [19.003, -13.046], [-3.411, -28.405], [-17.434, -41.024]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.579999976065, 0.638999968884, 0.752999997606, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [64.473, 155.778], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 3, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-3.321, -2.283], [-2.698, -1.037], [1.245, -2.491], [3.736, 1.038], [3.528, 1.245], [1.037, 1.038], [0, 0], [0, 0]], "o": [[0, 0], [3.321, 2.284], [2.698, 1.038], [-1.245, 2.491], [-3.737, -1.038], [-3.529, -1.245], [-1.039, -1.037], [0, 0], [0, 0]], "v": [[-1.556, -10.067], [1.557, -4.671], [8.199, 0.104], [12.142, 7.16], [1.35, 9.028], [-6.537, 3.424], [-12.348, -0.312], [-12.972, -5.501], [-0.311, -9.652]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.219999994016, 0.152999997606, 0.277999997606, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [123.222, 198.459], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 3, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-3.321, -2.283], [-2.698, -1.038], [1.245, -2.491], [3.736, 1.037], [3.528, 1.245], [1.037, 1.038], [0, 0], [0, 0]], "o": [[0, 0], [3.321, 2.284], [2.698, 1.038], [-1.245, 2.49], [-3.736, -1.038], [-3.528, -1.246], [-1.038, -1.038], [0, 0], [0, 0]], "v": [[-1.557, -10.066], [1.557, -4.67], [8.199, 0.104], [12.142, 7.161], [1.35, 9.029], [-6.538, 3.425], [-12.349, -0.311], [-12.972, -5.5], [-0.312, -9.651]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.219999994016, 0.152999997606, 0.277999997606, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [107.034, 210.497], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 4", "np": 3, "cix": 2, "bm": 0, "ix": 4, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-1.245, -3.736], [-4.67, -7.161], [-1.234, 0.191], [0.311, 0.934], [0, 0], [0, 3.425], [-2.179, -0.934], [-1.245, -1.245], [-0.934, 0.312], [0, 1.557], [1.868, 1.868], [1.868, 2.491], [0, 0], [-6.226, 1.246], [-2.491, 1.557], [1.557, 1.245], [4.359, 1.557], [5.292, -1.245]], "o": [[0, 0], [4.67, 7.16], [1.233, -0.193], [-0.311, -0.934], [0, 0], [0, -3.424], [2.179, 0.934], [1.245, 1.245], [0.934, -0.311], [0, -1.556], [-1.868, -1.868], [-1.867, -2.49], [0, 0], [6.226, -1.245], [2.49, -1.556], [-1.556, -1.245], [-4.358, -1.556], [-5.293, 1.246]], "v": [[-14.632, -5.796], [-13.698, 8.214], [-2.789, 18.369], [-1.245, 16.931], [-3.113, 12.884], [-8.405, 8.836], [-4.358, 5.723], [-0.622, 9.459], [1.868, 13.506], [3.425, 10.704], [1.557, 5.412], [0, -2.06], [-3.113, -5.484], [4.359, -4.862], [15.878, -7.353], [16.189, -13.579], [6.538, -17.004], [-5.603, -16.381]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.882000014361, 0.419999994016, 0.250999989229, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [32.316, 18.81], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.579]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [0]}, {"i": {"x": [0.833], "y": [0.706]}, "o": {"x": [0.167], "y": [0.104]}, "t": 1, "s": [-0.171]}, {"i": {"x": [0.833], "y": [0.754]}, "o": {"x": [0.167], "y": [0.116]}, "t": 2, "s": [-0.867]}, {"i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.126]}, "t": 3, "s": [-2.628]}, {"i": {"x": [0.833], "y": [0.859]}, "o": {"x": [0.167], "y": [0.167]}, "t": 4, "s": [-6.072]}, {"i": {"x": [0.833], "y": [0.858]}, "o": {"x": [0.167], "y": [0.204]}, "t": 5, "s": [-9.5]}, {"i": {"x": [0.833], "y": [0.855]}, "o": {"x": [0.167], "y": [0.202]}, "t": 6, "s": [-11.862]}, {"i": {"x": [0.833], "y": [0.852]}, "o": {"x": [0.167], "y": [0.195]}, "t": 7, "s": [-13.527]}, {"i": {"x": [0.833], "y": [0.851]}, "o": {"x": [0.167], "y": [0.191]}, "t": 8, "s": [-14.768]}, {"i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.189]}, "t": 9, "s": [-15.727]}, {"i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.188]}, "t": 10, "s": [-16.484]}, {"i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.187]}, "t": 11, "s": [-17.09]}, {"i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.187]}, "t": 12, "s": [-17.577]}, {"i": {"x": [0.833], "y": [0.851]}, "o": {"x": [0.167], "y": [0.188]}, "t": 13, "s": [-17.967]}, {"i": {"x": [0.833], "y": [0.853]}, "o": {"x": [0.167], "y": [0.19]}, "t": 14, "s": [-18.278]}, {"i": {"x": [0.833], "y": [0.856]}, "o": {"x": [0.167], "y": [0.193]}, "t": 15, "s": [-18.521]}, {"i": {"x": [0.833], "y": [0.861]}, "o": {"x": [0.167], "y": [0.198]}, "t": 16, "s": [-18.706]}, {"i": {"x": [0.833], "y": [0.869]}, "o": {"x": [0.167], "y": [0.207]}, "t": 17, "s": [-18.841]}, {"i": {"x": [0.833], "y": [0.89]}, "o": {"x": [0.167], "y": [0.23]}, "t": 18, "s": [-18.932]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0.346]}, "t": 19, "s": [-18.984]}, {"i": {"x": [0.833], "y": [0.654]}, "o": {"x": [0.167], "y": [0]}, "t": 20, "s": [-19]}, {"i": {"x": [0.833], "y": [0.77]}, "o": {"x": [0.167], "y": [0.11]}, "t": 21, "s": [-18.984]}, {"i": {"x": [0.833], "y": [0.793]}, "o": {"x": [0.167], "y": [0.131]}, "t": 22, "s": [-18.932]}, {"i": {"x": [0.833], "y": [0.802]}, "o": {"x": [0.167], "y": [0.139]}, "t": 23, "s": [-18.841]}, {"i": {"x": [0.833], "y": [0.807]}, "o": {"x": [0.167], "y": [0.144]}, "t": 24, "s": [-18.706]}, {"i": {"x": [0.833], "y": [0.81]}, "o": {"x": [0.167], "y": [0.147]}, "t": 25, "s": [-18.521]}, {"i": {"x": [0.833], "y": [0.812]}, "o": {"x": [0.167], "y": [0.149]}, "t": 26, "s": [-18.278]}, {"i": {"x": [0.833], "y": [0.813]}, "o": {"x": [0.167], "y": [0.15]}, "t": 27, "s": [-17.967]}, {"i": {"x": [0.833], "y": [0.813]}, "o": {"x": [0.167], "y": [0.15]}, "t": 28, "s": [-17.577]}, {"i": {"x": [0.833], "y": [0.812]}, "o": {"x": [0.167], "y": [0.15]}, "t": 29, "s": [-17.09]}, {"i": {"x": [0.833], "y": [0.811]}, "o": {"x": [0.167], "y": [0.15]}, "t": 30, "s": [-16.484]}, {"i": {"x": [0.833], "y": [0.809]}, "o": {"x": [0.167], "y": [0.149]}, "t": 31, "s": [-15.727]}, {"i": {"x": [0.833], "y": [0.805]}, "o": {"x": [0.167], "y": [0.148]}, "t": 32, "s": [-14.768]}, {"i": {"x": [0.833], "y": [0.798]}, "o": {"x": [0.167], "y": [0.145]}, "t": 33, "s": [-13.527]}, {"i": {"x": [0.833], "y": [0.796]}, "o": {"x": [0.167], "y": [0.142]}, "t": 34, "s": [-11.862]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.141]}, "t": 35, "s": [-9.5]}, {"i": {"x": [0.833], "y": [0.874]}, "o": {"x": [0.167], "y": [0.166]}, "t": 36, "s": [-6.072]}, {"i": {"x": [0.833], "y": [0.884]}, "o": {"x": [0.167], "y": [0.246]}, "t": 37, "s": [-2.628]}, {"i": {"x": [0.833], "y": [0.896]}, "o": {"x": [0.167], "y": [0.294]}, "t": 38, "s": [-0.867]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0.421]}, "t": 39, "s": [-0.171]}, {"i": {"x": [0.833], "y": [0.579]}, "o": {"x": [0.167], "y": [0]}, "t": 40, "s": [0]}, {"i": {"x": [0.833], "y": [0.706]}, "o": {"x": [0.167], "y": [0.104]}, "t": 41, "s": [-0.171]}, {"i": {"x": [0.833], "y": [0.754]}, "o": {"x": [0.167], "y": [0.116]}, "t": 42, "s": [-0.867]}, {"i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.126]}, "t": 43, "s": [-2.628]}, {"i": {"x": [0.833], "y": [0.859]}, "o": {"x": [0.167], "y": [0.167]}, "t": 44, "s": [-6.072]}, {"i": {"x": [0.833], "y": [0.858]}, "o": {"x": [0.167], "y": [0.204]}, "t": 45, "s": [-9.5]}, {"i": {"x": [0.833], "y": [0.855]}, "o": {"x": [0.167], "y": [0.202]}, "t": 46, "s": [-11.862]}, {"i": {"x": [0.833], "y": [0.852]}, "o": {"x": [0.167], "y": [0.195]}, "t": 47, "s": [-13.527]}, {"i": {"x": [0.833], "y": [0.851]}, "o": {"x": [0.167], "y": [0.191]}, "t": 48, "s": [-14.768]}, {"i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.189]}, "t": 49, "s": [-15.727]}, {"i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.188]}, "t": 50, "s": [-16.484]}, {"i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.187]}, "t": 51, "s": [-17.09]}, {"i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.187]}, "t": 52, "s": [-17.577]}, {"i": {"x": [0.833], "y": [0.851]}, "o": {"x": [0.167], "y": [0.188]}, "t": 53, "s": [-17.967]}, {"i": {"x": [0.833], "y": [0.853]}, "o": {"x": [0.167], "y": [0.19]}, "t": 54, "s": [-18.278]}, {"i": {"x": [0.833], "y": [0.856]}, "o": {"x": [0.167], "y": [0.193]}, "t": 55, "s": [-18.521]}, {"i": {"x": [0.833], "y": [0.861]}, "o": {"x": [0.167], "y": [0.198]}, "t": 56, "s": [-18.706]}, {"i": {"x": [0.833], "y": [0.869]}, "o": {"x": [0.167], "y": [0.207]}, "t": 57, "s": [-18.841]}, {"i": {"x": [0.833], "y": [0.89]}, "o": {"x": [0.167], "y": [0.23]}, "t": 58, "s": [-18.932]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0.346]}, "t": 59, "s": [-18.984]}, {"i": {"x": [0.833], "y": [0.654]}, "o": {"x": [0.167], "y": [0]}, "t": 60, "s": [-19]}, {"i": {"x": [0.833], "y": [0.77]}, "o": {"x": [0.167], "y": [0.11]}, "t": 61, "s": [-18.984]}, {"i": {"x": [0.833], "y": [0.793]}, "o": {"x": [0.167], "y": [0.131]}, "t": 62, "s": [-18.932]}, {"i": {"x": [0.833], "y": [0.802]}, "o": {"x": [0.167], "y": [0.139]}, "t": 63, "s": [-18.841]}, {"i": {"x": [0.833], "y": [0.807]}, "o": {"x": [0.167], "y": [0.144]}, "t": 64, "s": [-18.706]}, {"i": {"x": [0.833], "y": [0.81]}, "o": {"x": [0.167], "y": [0.147]}, "t": 65, "s": [-18.521]}, {"i": {"x": [0.833], "y": [0.812]}, "o": {"x": [0.167], "y": [0.149]}, "t": 66, "s": [-18.278]}, {"i": {"x": [0.833], "y": [0.813]}, "o": {"x": [0.167], "y": [0.15]}, "t": 67, "s": [-17.967]}, {"i": {"x": [0.833], "y": [0.813]}, "o": {"x": [0.167], "y": [0.15]}, "t": 68, "s": [-17.577]}, {"i": {"x": [0.833], "y": [0.812]}, "o": {"x": [0.167], "y": [0.15]}, "t": 69, "s": [-17.09]}, {"i": {"x": [0.833], "y": [0.811]}, "o": {"x": [0.167], "y": [0.15]}, "t": 70, "s": [-16.484]}, {"i": {"x": [0.833], "y": [0.809]}, "o": {"x": [0.167], "y": [0.149]}, "t": 71, "s": [-15.727]}, {"i": {"x": [0.833], "y": [0.805]}, "o": {"x": [0.167], "y": [0.148]}, "t": 72, "s": [-14.768]}, {"i": {"x": [0.833], "y": [0.798]}, "o": {"x": [0.167], "y": [0.145]}, "t": 73, "s": [-13.527]}, {"i": {"x": [0.833], "y": [0.796]}, "o": {"x": [0.167], "y": [0.142]}, "t": 74, "s": [-11.862]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.141]}, "t": 75, "s": [-9.5]}, {"i": {"x": [0.833], "y": [0.874]}, "o": {"x": [0.167], "y": [0.166]}, "t": 76, "s": [-6.072]}, {"i": {"x": [0.833], "y": [0.884]}, "o": {"x": [0.167], "y": [0.246]}, "t": 77, "s": [-2.628]}, {"i": {"x": [0.833], "y": [0.896]}, "o": {"x": [0.167], "y": [0.294]}, "t": 78, "s": [-0.867]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0.421]}, "t": 79, "s": [-0.171]}, {"i": {"x": [0.833], "y": [0.579]}, "o": {"x": [0.167], "y": [0]}, "t": 80, "s": [0]}, {"i": {"x": [0.833], "y": [0.706]}, "o": {"x": [0.167], "y": [0.104]}, "t": 81, "s": [-0.171]}, {"i": {"x": [0.833], "y": [0.754]}, "o": {"x": [0.167], "y": [0.116]}, "t": 82, "s": [-0.867]}, {"i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.126]}, "t": 83, "s": [-2.628]}, {"i": {"x": [0.833], "y": [0.859]}, "o": {"x": [0.167], "y": [0.167]}, "t": 84, "s": [-6.072]}, {"i": {"x": [0.833], "y": [0.858]}, "o": {"x": [0.167], "y": [0.204]}, "t": 85, "s": [-9.5]}, {"i": {"x": [0.833], "y": [0.855]}, "o": {"x": [0.167], "y": [0.202]}, "t": 86, "s": [-11.862]}, {"i": {"x": [0.833], "y": [0.852]}, "o": {"x": [0.167], "y": [0.195]}, "t": 87, "s": [-13.527]}, {"i": {"x": [0.833], "y": [0.851]}, "o": {"x": [0.167], "y": [0.191]}, "t": 88, "s": [-14.768]}, {"i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.189]}, "t": 89, "s": [-15.727]}, {"i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.188]}, "t": 90, "s": [-16.484]}, {"i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.187]}, "t": 91, "s": [-17.09]}, {"i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.187]}, "t": 92, "s": [-17.577]}, {"i": {"x": [0.833], "y": [0.851]}, "o": {"x": [0.167], "y": [0.188]}, "t": 93, "s": [-17.967]}, {"i": {"x": [0.833], "y": [0.853]}, "o": {"x": [0.167], "y": [0.19]}, "t": 94, "s": [-18.278]}, {"i": {"x": [0.833], "y": [0.856]}, "o": {"x": [0.167], "y": [0.193]}, "t": 95, "s": [-18.521]}, {"i": {"x": [0.833], "y": [0.861]}, "o": {"x": [0.167], "y": [0.198]}, "t": 96, "s": [-18.706]}, {"i": {"x": [0.833], "y": [0.869]}, "o": {"x": [0.167], "y": [0.207]}, "t": 97, "s": [-18.841]}, {"i": {"x": [0.833], "y": [0.89]}, "o": {"x": [0.167], "y": [0.23]}, "t": 98, "s": [-18.932]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0.346]}, "t": 99, "s": [-18.984]}, {"i": {"x": [0.833], "y": [0.654]}, "o": {"x": [0.167], "y": [0]}, "t": 100, "s": [-19]}, {"i": {"x": [0.833], "y": [0.77]}, "o": {"x": [0.167], "y": [0.11]}, "t": 101, "s": [-18.984]}, {"i": {"x": [0.833], "y": [0.793]}, "o": {"x": [0.167], "y": [0.131]}, "t": 102, "s": [-18.932]}, {"i": {"x": [0.833], "y": [0.802]}, "o": {"x": [0.167], "y": [0.139]}, "t": 103, "s": [-18.841]}, {"i": {"x": [0.833], "y": [0.807]}, "o": {"x": [0.167], "y": [0.144]}, "t": 104, "s": [-18.706]}, {"i": {"x": [0.833], "y": [0.81]}, "o": {"x": [0.167], "y": [0.147]}, "t": 105, "s": [-18.521]}, {"i": {"x": [0.833], "y": [0.812]}, "o": {"x": [0.167], "y": [0.149]}, "t": 106, "s": [-18.278]}, {"i": {"x": [0.833], "y": [0.813]}, "o": {"x": [0.167], "y": [0.15]}, "t": 107, "s": [-17.967]}, {"i": {"x": [0.833], "y": [0.813]}, "o": {"x": [0.167], "y": [0.15]}, "t": 108, "s": [-17.577]}, {"i": {"x": [0.833], "y": [0.812]}, "o": {"x": [0.167], "y": [0.15]}, "t": 109, "s": [-17.09]}, {"i": {"x": [0.833], "y": [0.811]}, "o": {"x": [0.167], "y": [0.15]}, "t": 110, "s": [-16.484]}, {"i": {"x": [0.833], "y": [0.809]}, "o": {"x": [0.167], "y": [0.149]}, "t": 111, "s": [-15.727]}, {"i": {"x": [0.833], "y": [0.805]}, "o": {"x": [0.167], "y": [0.148]}, "t": 112, "s": [-14.768]}, {"i": {"x": [0.833], "y": [0.798]}, "o": {"x": [0.167], "y": [0.145]}, "t": 113, "s": [-13.527]}, {"i": {"x": [0.833], "y": [0.796]}, "o": {"x": [0.167], "y": [0.142]}, "t": 114, "s": [-11.862]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.141]}, "t": 115, "s": [-9.5]}, {"i": {"x": [0.833], "y": [0.874]}, "o": {"x": [0.167], "y": [0.166]}, "t": 116, "s": [-6.072]}, {"i": {"x": [0.833], "y": [0.884]}, "o": {"x": [0.167], "y": [0.246]}, "t": 117, "s": [-2.628]}, {"i": {"x": [0.833], "y": [0.896]}, "o": {"x": [0.167], "y": [0.294]}, "t": 118, "s": [-0.867]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0.421]}, "t": 119, "s": [-0.171]}, {"i": {"x": [0.833], "y": [0.579]}, "o": {"x": [0.167], "y": [0]}, "t": 120, "s": [0]}, {"i": {"x": [0.833], "y": [0.706]}, "o": {"x": [0.167], "y": [0.104]}, "t": 121, "s": [-0.171]}, {"i": {"x": [0.833], "y": [0.754]}, "o": {"x": [0.167], "y": [0.116]}, "t": 122, "s": [-0.867]}, {"i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.126]}, "t": 123, "s": [-2.628]}, {"i": {"x": [0.833], "y": [0.859]}, "o": {"x": [0.167], "y": [0.167]}, "t": 124, "s": [-6.072]}, {"i": {"x": [0.833], "y": [0.858]}, "o": {"x": [0.167], "y": [0.204]}, "t": 125, "s": [-9.5]}, {"i": {"x": [0.833], "y": [0.855]}, "o": {"x": [0.167], "y": [0.202]}, "t": 126, "s": [-11.862]}, {"i": {"x": [0.833], "y": [0.852]}, "o": {"x": [0.167], "y": [0.195]}, "t": 127, "s": [-13.527]}, {"i": {"x": [0.833], "y": [0.851]}, "o": {"x": [0.167], "y": [0.191]}, "t": 128, "s": [-14.768]}, {"i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.189]}, "t": 129, "s": [-15.727]}, {"i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.188]}, "t": 130, "s": [-16.484]}, {"i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.187]}, "t": 131, "s": [-17.09]}, {"i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.187]}, "t": 132, "s": [-17.577]}, {"i": {"x": [0.833], "y": [0.851]}, "o": {"x": [0.167], "y": [0.188]}, "t": 133, "s": [-17.967]}, {"i": {"x": [0.833], "y": [0.853]}, "o": {"x": [0.167], "y": [0.19]}, "t": 134, "s": [-18.278]}, {"i": {"x": [0.833], "y": [0.856]}, "o": {"x": [0.167], "y": [0.193]}, "t": 135, "s": [-18.521]}, {"i": {"x": [0.833], "y": [0.861]}, "o": {"x": [0.167], "y": [0.198]}, "t": 136, "s": [-18.706]}, {"i": {"x": [0.833], "y": [0.869]}, "o": {"x": [0.167], "y": [0.207]}, "t": 137, "s": [-18.841]}, {"i": {"x": [0.833], "y": [0.89]}, "o": {"x": [0.167], "y": [0.23]}, "t": 138, "s": [-18.932]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0.346]}, "t": 139, "s": [-18.984]}, {"i": {"x": [0.833], "y": [0.654]}, "o": {"x": [0.167], "y": [0]}, "t": 140, "s": [-19]}, {"i": {"x": [0.833], "y": [0.77]}, "o": {"x": [0.167], "y": [0.11]}, "t": 141, "s": [-18.984]}, {"i": {"x": [0.833], "y": [0.793]}, "o": {"x": [0.167], "y": [0.131]}, "t": 142, "s": [-18.932]}, {"i": {"x": [0.833], "y": [0.802]}, "o": {"x": [0.167], "y": [0.139]}, "t": 143, "s": [-18.841]}, {"i": {"x": [0.833], "y": [0.807]}, "o": {"x": [0.167], "y": [0.144]}, "t": 144, "s": [-18.706]}, {"i": {"x": [0.833], "y": [0.81]}, "o": {"x": [0.167], "y": [0.147]}, "t": 145, "s": [-18.521]}, {"i": {"x": [0.833], "y": [0.812]}, "o": {"x": [0.167], "y": [0.149]}, "t": 146, "s": [-18.278]}, {"i": {"x": [0.833], "y": [0.813]}, "o": {"x": [0.167], "y": [0.15]}, "t": 147, "s": [-17.967]}, {"i": {"x": [0.833], "y": [0.813]}, "o": {"x": [0.167], "y": [0.15]}, "t": 148, "s": [-17.577]}, {"i": {"x": [0.833], "y": [0.812]}, "o": {"x": [0.167], "y": [0.15]}, "t": 149, "s": [-17.09]}, {"i": {"x": [0.833], "y": [0.811]}, "o": {"x": [0.167], "y": [0.15]}, "t": 150, "s": [-16.484]}, {"i": {"x": [0.833], "y": [0.809]}, "o": {"x": [0.167], "y": [0.149]}, "t": 151, "s": [-15.727]}, {"i": {"x": [0.833], "y": [0.805]}, "o": {"x": [0.167], "y": [0.148]}, "t": 152, "s": [-14.768]}, {"i": {"x": [0.833], "y": [0.798]}, "o": {"x": [0.167], "y": [0.145]}, "t": 153, "s": [-13.527]}, {"i": {"x": [0.833], "y": [0.796]}, "o": {"x": [0.167], "y": [0.142]}, "t": 154, "s": [-11.862]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.141]}, "t": 155, "s": [-9.5]}, {"i": {"x": [0.833], "y": [0.874]}, "o": {"x": [0.167], "y": [0.166]}, "t": 156, "s": [-6.072]}, {"i": {"x": [0.833], "y": [0.884]}, "o": {"x": [0.167], "y": [0.246]}, "t": 157, "s": [-2.628]}, {"i": {"x": [0.833], "y": [0.896]}, "o": {"x": [0.167], "y": [0.294]}, "t": 158, "s": [-0.867]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0.421]}, "t": 159, "s": [-0.171]}, {"i": {"x": [0.833], "y": [0.579]}, "o": {"x": [0.167], "y": [0]}, "t": 160, "s": [0]}, {"i": {"x": [0.833], "y": [0.706]}, "o": {"x": [0.167], "y": [0.104]}, "t": 161, "s": [-0.171]}, {"i": {"x": [0.833], "y": [0.754]}, "o": {"x": [0.167], "y": [0.116]}, "t": 162, "s": [-0.867]}, {"i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.126]}, "t": 163, "s": [-2.628]}, {"i": {"x": [0.833], "y": [0.859]}, "o": {"x": [0.167], "y": [0.167]}, "t": 164, "s": [-6.072]}, {"i": {"x": [0.833], "y": [0.858]}, "o": {"x": [0.167], "y": [0.204]}, "t": 165, "s": [-9.5]}, {"i": {"x": [0.833], "y": [0.855]}, "o": {"x": [0.167], "y": [0.202]}, "t": 166, "s": [-11.862]}, {"i": {"x": [0.833], "y": [0.852]}, "o": {"x": [0.167], "y": [0.195]}, "t": 167, "s": [-13.527]}, {"i": {"x": [0.833], "y": [0.851]}, "o": {"x": [0.167], "y": [0.191]}, "t": 168, "s": [-14.768]}, {"i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.189]}, "t": 169, "s": [-15.727]}, {"i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.188]}, "t": 170, "s": [-16.484]}, {"i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.187]}, "t": 171, "s": [-17.09]}, {"i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.187]}, "t": 172, "s": [-17.577]}, {"i": {"x": [0.833], "y": [0.851]}, "o": {"x": [0.167], "y": [0.188]}, "t": 173, "s": [-17.967]}, {"i": {"x": [0.833], "y": [0.853]}, "o": {"x": [0.167], "y": [0.19]}, "t": 174, "s": [-18.278]}, {"i": {"x": [0.833], "y": [0.856]}, "o": {"x": [0.167], "y": [0.193]}, "t": 175, "s": [-18.521]}, {"i": {"x": [0.833], "y": [0.861]}, "o": {"x": [0.167], "y": [0.198]}, "t": 176, "s": [-18.706]}, {"i": {"x": [0.833], "y": [0.869]}, "o": {"x": [0.167], "y": [0.207]}, "t": 177, "s": [-18.841]}, {"i": {"x": [0.833], "y": [0.89]}, "o": {"x": [0.167], "y": [0.23]}, "t": 178, "s": [-18.932]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0.346]}, "t": 179, "s": [-18.984]}, {"i": {"x": [0.833], "y": [0.654]}, "o": {"x": [0.167], "y": [0]}, "t": 180, "s": [-19]}, {"i": {"x": [0.833], "y": [0.77]}, "o": {"x": [0.167], "y": [0.11]}, "t": 181, "s": [-18.984]}, {"i": {"x": [0.833], "y": [0.793]}, "o": {"x": [0.167], "y": [0.131]}, "t": 182, "s": [-18.932]}, {"i": {"x": [0.833], "y": [0.802]}, "o": {"x": [0.167], "y": [0.139]}, "t": 183, "s": [-18.841]}, {"i": {"x": [0.833], "y": [0.807]}, "o": {"x": [0.167], "y": [0.144]}, "t": 184, "s": [-18.706]}, {"i": {"x": [0.833], "y": [0.81]}, "o": {"x": [0.167], "y": [0.147]}, "t": 185, "s": [-18.521]}, {"i": {"x": [0.833], "y": [0.812]}, "o": {"x": [0.167], "y": [0.149]}, "t": 186, "s": [-18.278]}, {"i": {"x": [0.833], "y": [0.813]}, "o": {"x": [0.167], "y": [0.15]}, "t": 187, "s": [-17.967]}, {"i": {"x": [0.833], "y": [0.813]}, "o": {"x": [0.167], "y": [0.15]}, "t": 188, "s": [-17.577]}, {"i": {"x": [0.833], "y": [0.812]}, "o": {"x": [0.167], "y": [0.15]}, "t": 189, "s": [-17.09]}, {"i": {"x": [0.833], "y": [0.811]}, "o": {"x": [0.167], "y": [0.15]}, "t": 190, "s": [-16.484]}, {"i": {"x": [0.833], "y": [0.809]}, "o": {"x": [0.167], "y": [0.149]}, "t": 191, "s": [-15.727]}, {"i": {"x": [0.833], "y": [0.805]}, "o": {"x": [0.167], "y": [0.148]}, "t": 192, "s": [-14.768]}, {"i": {"x": [0.833], "y": [0.798]}, "o": {"x": [0.167], "y": [0.145]}, "t": 193, "s": [-13.527]}, {"i": {"x": [0.833], "y": [0.796]}, "o": {"x": [0.167], "y": [0.142]}, "t": 194, "s": [-11.862]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.141]}, "t": 195, "s": [-9.5]}, {"i": {"x": [0.833], "y": [0.874]}, "o": {"x": [0.167], "y": [0.166]}, "t": 196, "s": [-6.072]}, {"i": {"x": [0.833], "y": [0.884]}, "o": {"x": [0.167], "y": [0.246]}, "t": 197, "s": [-2.628]}, {"i": {"x": [0.833], "y": [0.896]}, "o": {"x": [0.167], "y": [0.294]}, "t": 198, "s": [-0.867]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0.421]}, "t": 199, "s": [-0.171]}, {"i": {"x": [0.833], "y": [0.579]}, "o": {"x": [0.167], "y": [0]}, "t": 200, "s": [0]}, {"i": {"x": [0.833], "y": [0.706]}, "o": {"x": [0.167], "y": [0.104]}, "t": 201, "s": [-0.171]}, {"i": {"x": [0.833], "y": [0.754]}, "o": {"x": [0.167], "y": [0.116]}, "t": 202, "s": [-0.867]}, {"i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.126]}, "t": 203, "s": [-2.628]}, {"i": {"x": [0.833], "y": [0.859]}, "o": {"x": [0.167], "y": [0.167]}, "t": 204, "s": [-6.072]}, {"i": {"x": [0.833], "y": [0.858]}, "o": {"x": [0.167], "y": [0.204]}, "t": 205, "s": [-9.5]}, {"i": {"x": [0.833], "y": [0.855]}, "o": {"x": [0.167], "y": [0.202]}, "t": 206, "s": [-11.862]}, {"i": {"x": [0.833], "y": [0.852]}, "o": {"x": [0.167], "y": [0.195]}, "t": 207, "s": [-13.527]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.191]}, "t": 208, "s": [-14.768]}, {"t": 209.000008512745, "s": [-15.727]}], "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 5", "np": 3, "cix": 2, "bm": 0, "ix": 5, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-0.775, 5.223], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [1.775, 2.873], [0, 0]], "v": [[3.856, 2.736], [1.252, -6.456], [1.645, -9.605], [-1.587, -12.469], [-3.856, -8.499], [-1.253, -6.122], [-0.546, 7.526], [2.427, 12.469]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0, 0.647000002394, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [44.954, 64.01], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 6", "np": 3, "cix": 2, "bm": 0, "ix": 6, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-1.139, 7.183]], "o": [[0, 0], [0, 0], [0, 0], [5.326, 5.765], [0, 0], [0, 0]], "v": [[6.934, -7.291], [1.089, -12.469], [-3.587, -4.285], [-8.302, -7.263], [5.103, 12.469], [8.302, -8.916]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.811999990426, 0.855000035903, 0.894000004787, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [42.278, 64.01], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 7", "np": 3, "cix": 2, "bm": 0, "ix": 7, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[2.292, 9.6], [8.582, -2.458], [3.774, -8.473], [-4.149, -4.19], [-0.835, -0.845], [-0.314, -0.288], [0, 0], [0, 0], [-2.31, -0.566], [-2.289, 2.697]], "o": [[-2.291, -9.6], [0, 0], [-3.774, 8.472], [1.747, 1.718], [0.302, 0.32], [0.058, 0.06], [0, 0], [2.818, 2.553], [3.842, 0.941], [2.289, -2.697]], "v": [[16.721, -6.27], [-2.1, -18.533], [-15.238, -8.653], [-8.38, 10.657], [-4.477, 14.547], [-3.563, 15.48], [-3.473, 15.572], [-3.464, 15.563], [4.82, 20.05], [15.397, 17.428]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.62400004069, 0.458999992819, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [34.003, 22.631], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.579]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [2]}, {"i": {"x": [0.833], "y": [0.706]}, "o": {"x": [0.167], "y": [0.104]}, "t": 1, "s": [1.829]}, {"i": {"x": [0.833], "y": [0.754]}, "o": {"x": [0.167], "y": [0.116]}, "t": 2, "s": [1.133]}, {"i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.126]}, "t": 3, "s": [-0.628]}, {"i": {"x": [0.833], "y": [0.859]}, "o": {"x": [0.167], "y": [0.167]}, "t": 4, "s": [-4.072]}, {"i": {"x": [0.833], "y": [0.858]}, "o": {"x": [0.167], "y": [0.204]}, "t": 5, "s": [-7.5]}, {"i": {"x": [0.833], "y": [0.855]}, "o": {"x": [0.167], "y": [0.202]}, "t": 6, "s": [-9.862]}, {"i": {"x": [0.833], "y": [0.852]}, "o": {"x": [0.167], "y": [0.195]}, "t": 7, "s": [-11.527]}, {"i": {"x": [0.833], "y": [0.851]}, "o": {"x": [0.167], "y": [0.191]}, "t": 8, "s": [-12.768]}, {"i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.189]}, "t": 9, "s": [-13.727]}, {"i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.188]}, "t": 10, "s": [-14.484]}, {"i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.187]}, "t": 11, "s": [-15.09]}, {"i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.187]}, "t": 12, "s": [-15.577]}, {"i": {"x": [0.833], "y": [0.851]}, "o": {"x": [0.167], "y": [0.188]}, "t": 13, "s": [-15.967]}, {"i": {"x": [0.833], "y": [0.853]}, "o": {"x": [0.167], "y": [0.19]}, "t": 14, "s": [-16.278]}, {"i": {"x": [0.833], "y": [0.856]}, "o": {"x": [0.167], "y": [0.193]}, "t": 15, "s": [-16.521]}, {"i": {"x": [0.833], "y": [0.861]}, "o": {"x": [0.167], "y": [0.198]}, "t": 16, "s": [-16.706]}, {"i": {"x": [0.833], "y": [0.869]}, "o": {"x": [0.167], "y": [0.207]}, "t": 17, "s": [-16.841]}, {"i": {"x": [0.833], "y": [0.89]}, "o": {"x": [0.167], "y": [0.23]}, "t": 18, "s": [-16.932]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0.346]}, "t": 19, "s": [-16.984]}, {"i": {"x": [0.833], "y": [0.654]}, "o": {"x": [0.167], "y": [0]}, "t": 20, "s": [-17]}, {"i": {"x": [0.833], "y": [0.77]}, "o": {"x": [0.167], "y": [0.11]}, "t": 21, "s": [-16.984]}, {"i": {"x": [0.833], "y": [0.793]}, "o": {"x": [0.167], "y": [0.131]}, "t": 22, "s": [-16.932]}, {"i": {"x": [0.833], "y": [0.802]}, "o": {"x": [0.167], "y": [0.139]}, "t": 23, "s": [-16.841]}, {"i": {"x": [0.833], "y": [0.807]}, "o": {"x": [0.167], "y": [0.144]}, "t": 24, "s": [-16.706]}, {"i": {"x": [0.833], "y": [0.81]}, "o": {"x": [0.167], "y": [0.147]}, "t": 25, "s": [-16.521]}, {"i": {"x": [0.833], "y": [0.812]}, "o": {"x": [0.167], "y": [0.149]}, "t": 26, "s": [-16.278]}, {"i": {"x": [0.833], "y": [0.813]}, "o": {"x": [0.167], "y": [0.15]}, "t": 27, "s": [-15.967]}, {"i": {"x": [0.833], "y": [0.813]}, "o": {"x": [0.167], "y": [0.15]}, "t": 28, "s": [-15.577]}, {"i": {"x": [0.833], "y": [0.812]}, "o": {"x": [0.167], "y": [0.15]}, "t": 29, "s": [-15.09]}, {"i": {"x": [0.833], "y": [0.811]}, "o": {"x": [0.167], "y": [0.15]}, "t": 30, "s": [-14.484]}, {"i": {"x": [0.833], "y": [0.809]}, "o": {"x": [0.167], "y": [0.149]}, "t": 31, "s": [-13.727]}, {"i": {"x": [0.833], "y": [0.805]}, "o": {"x": [0.167], "y": [0.148]}, "t": 32, "s": [-12.768]}, {"i": {"x": [0.833], "y": [0.798]}, "o": {"x": [0.167], "y": [0.145]}, "t": 33, "s": [-11.527]}, {"i": {"x": [0.833], "y": [0.796]}, "o": {"x": [0.167], "y": [0.142]}, "t": 34, "s": [-9.862]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.141]}, "t": 35, "s": [-7.5]}, {"i": {"x": [0.833], "y": [0.874]}, "o": {"x": [0.167], "y": [0.166]}, "t": 36, "s": [-4.072]}, {"i": {"x": [0.833], "y": [0.884]}, "o": {"x": [0.167], "y": [0.246]}, "t": 37, "s": [-0.628]}, {"i": {"x": [0.833], "y": [0.896]}, "o": {"x": [0.167], "y": [0.294]}, "t": 38, "s": [1.133]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0.421]}, "t": 39, "s": [1.829]}, {"i": {"x": [0.833], "y": [0.579]}, "o": {"x": [0.167], "y": [0]}, "t": 40, "s": [2]}, {"i": {"x": [0.833], "y": [0.706]}, "o": {"x": [0.167], "y": [0.104]}, "t": 41, "s": [1.829]}, {"i": {"x": [0.833], "y": [0.754]}, "o": {"x": [0.167], "y": [0.116]}, "t": 42, "s": [1.133]}, {"i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.126]}, "t": 43, "s": [-0.628]}, {"i": {"x": [0.833], "y": [0.859]}, "o": {"x": [0.167], "y": [0.167]}, "t": 44, "s": [-4.072]}, {"i": {"x": [0.833], "y": [0.858]}, "o": {"x": [0.167], "y": [0.204]}, "t": 45, "s": [-7.5]}, {"i": {"x": [0.833], "y": [0.855]}, "o": {"x": [0.167], "y": [0.202]}, "t": 46, "s": [-9.862]}, {"i": {"x": [0.833], "y": [0.852]}, "o": {"x": [0.167], "y": [0.195]}, "t": 47, "s": [-11.527]}, {"i": {"x": [0.833], "y": [0.851]}, "o": {"x": [0.167], "y": [0.191]}, "t": 48, "s": [-12.768]}, {"i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.189]}, "t": 49, "s": [-13.727]}, {"i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.188]}, "t": 50, "s": [-14.484]}, {"i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.187]}, "t": 51, "s": [-15.09]}, {"i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.187]}, "t": 52, "s": [-15.577]}, {"i": {"x": [0.833], "y": [0.851]}, "o": {"x": [0.167], "y": [0.188]}, "t": 53, "s": [-15.967]}, {"i": {"x": [0.833], "y": [0.853]}, "o": {"x": [0.167], "y": [0.19]}, "t": 54, "s": [-16.278]}, {"i": {"x": [0.833], "y": [0.856]}, "o": {"x": [0.167], "y": [0.193]}, "t": 55, "s": [-16.521]}, {"i": {"x": [0.833], "y": [0.861]}, "o": {"x": [0.167], "y": [0.198]}, "t": 56, "s": [-16.706]}, {"i": {"x": [0.833], "y": [0.869]}, "o": {"x": [0.167], "y": [0.207]}, "t": 57, "s": [-16.841]}, {"i": {"x": [0.833], "y": [0.89]}, "o": {"x": [0.167], "y": [0.23]}, "t": 58, "s": [-16.932]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0.346]}, "t": 59, "s": [-16.984]}, {"i": {"x": [0.833], "y": [0.654]}, "o": {"x": [0.167], "y": [0]}, "t": 60, "s": [-17]}, {"i": {"x": [0.833], "y": [0.77]}, "o": {"x": [0.167], "y": [0.11]}, "t": 61, "s": [-16.984]}, {"i": {"x": [0.833], "y": [0.793]}, "o": {"x": [0.167], "y": [0.131]}, "t": 62, "s": [-16.932]}, {"i": {"x": [0.833], "y": [0.802]}, "o": {"x": [0.167], "y": [0.139]}, "t": 63, "s": [-16.841]}, {"i": {"x": [0.833], "y": [0.807]}, "o": {"x": [0.167], "y": [0.144]}, "t": 64, "s": [-16.706]}, {"i": {"x": [0.833], "y": [0.81]}, "o": {"x": [0.167], "y": [0.147]}, "t": 65, "s": [-16.521]}, {"i": {"x": [0.833], "y": [0.812]}, "o": {"x": [0.167], "y": [0.149]}, "t": 66, "s": [-16.278]}, {"i": {"x": [0.833], "y": [0.813]}, "o": {"x": [0.167], "y": [0.15]}, "t": 67, "s": [-15.967]}, {"i": {"x": [0.833], "y": [0.813]}, "o": {"x": [0.167], "y": [0.15]}, "t": 68, "s": [-15.577]}, {"i": {"x": [0.833], "y": [0.812]}, "o": {"x": [0.167], "y": [0.15]}, "t": 69, "s": [-15.09]}, {"i": {"x": [0.833], "y": [0.811]}, "o": {"x": [0.167], "y": [0.15]}, "t": 70, "s": [-14.484]}, {"i": {"x": [0.833], "y": [0.809]}, "o": {"x": [0.167], "y": [0.149]}, "t": 71, "s": [-13.727]}, {"i": {"x": [0.833], "y": [0.805]}, "o": {"x": [0.167], "y": [0.148]}, "t": 72, "s": [-12.768]}, {"i": {"x": [0.833], "y": [0.798]}, "o": {"x": [0.167], "y": [0.145]}, "t": 73, "s": [-11.527]}, {"i": {"x": [0.833], "y": [0.796]}, "o": {"x": [0.167], "y": [0.142]}, "t": 74, "s": [-9.862]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.141]}, "t": 75, "s": [-7.5]}, {"i": {"x": [0.833], "y": [0.874]}, "o": {"x": [0.167], "y": [0.166]}, "t": 76, "s": [-4.072]}, {"i": {"x": [0.833], "y": [0.884]}, "o": {"x": [0.167], "y": [0.246]}, "t": 77, "s": [-0.628]}, {"i": {"x": [0.833], "y": [0.896]}, "o": {"x": [0.167], "y": [0.294]}, "t": 78, "s": [1.133]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0.421]}, "t": 79, "s": [1.829]}, {"i": {"x": [0.833], "y": [0.579]}, "o": {"x": [0.167], "y": [0]}, "t": 80, "s": [2]}, {"i": {"x": [0.833], "y": [0.706]}, "o": {"x": [0.167], "y": [0.104]}, "t": 81, "s": [1.829]}, {"i": {"x": [0.833], "y": [0.754]}, "o": {"x": [0.167], "y": [0.116]}, "t": 82, "s": [1.133]}, {"i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.126]}, "t": 83, "s": [-0.628]}, {"i": {"x": [0.833], "y": [0.859]}, "o": {"x": [0.167], "y": [0.167]}, "t": 84, "s": [-4.072]}, {"i": {"x": [0.833], "y": [0.858]}, "o": {"x": [0.167], "y": [0.204]}, "t": 85, "s": [-7.5]}, {"i": {"x": [0.833], "y": [0.855]}, "o": {"x": [0.167], "y": [0.202]}, "t": 86, "s": [-9.862]}, {"i": {"x": [0.833], "y": [0.852]}, "o": {"x": [0.167], "y": [0.195]}, "t": 87, "s": [-11.527]}, {"i": {"x": [0.833], "y": [0.851]}, "o": {"x": [0.167], "y": [0.191]}, "t": 88, "s": [-12.768]}, {"i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.189]}, "t": 89, "s": [-13.727]}, {"i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.188]}, "t": 90, "s": [-14.484]}, {"i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.187]}, "t": 91, "s": [-15.09]}, {"i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.187]}, "t": 92, "s": [-15.577]}, {"i": {"x": [0.833], "y": [0.851]}, "o": {"x": [0.167], "y": [0.188]}, "t": 93, "s": [-15.967]}, {"i": {"x": [0.833], "y": [0.853]}, "o": {"x": [0.167], "y": [0.19]}, "t": 94, "s": [-16.278]}, {"i": {"x": [0.833], "y": [0.856]}, "o": {"x": [0.167], "y": [0.193]}, "t": 95, "s": [-16.521]}, {"i": {"x": [0.833], "y": [0.861]}, "o": {"x": [0.167], "y": [0.198]}, "t": 96, "s": [-16.706]}, {"i": {"x": [0.833], "y": [0.869]}, "o": {"x": [0.167], "y": [0.207]}, "t": 97, "s": [-16.841]}, {"i": {"x": [0.833], "y": [0.89]}, "o": {"x": [0.167], "y": [0.23]}, "t": 98, "s": [-16.932]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0.346]}, "t": 99, "s": [-16.984]}, {"i": {"x": [0.833], "y": [0.654]}, "o": {"x": [0.167], "y": [0]}, "t": 100, "s": [-17]}, {"i": {"x": [0.833], "y": [0.77]}, "o": {"x": [0.167], "y": [0.11]}, "t": 101, "s": [-16.984]}, {"i": {"x": [0.833], "y": [0.793]}, "o": {"x": [0.167], "y": [0.131]}, "t": 102, "s": [-16.932]}, {"i": {"x": [0.833], "y": [0.802]}, "o": {"x": [0.167], "y": [0.139]}, "t": 103, "s": [-16.841]}, {"i": {"x": [0.833], "y": [0.807]}, "o": {"x": [0.167], "y": [0.144]}, "t": 104, "s": [-16.706]}, {"i": {"x": [0.833], "y": [0.81]}, "o": {"x": [0.167], "y": [0.147]}, "t": 105, "s": [-16.521]}, {"i": {"x": [0.833], "y": [0.812]}, "o": {"x": [0.167], "y": [0.149]}, "t": 106, "s": [-16.278]}, {"i": {"x": [0.833], "y": [0.813]}, "o": {"x": [0.167], "y": [0.15]}, "t": 107, "s": [-15.967]}, {"i": {"x": [0.833], "y": [0.813]}, "o": {"x": [0.167], "y": [0.15]}, "t": 108, "s": [-15.577]}, {"i": {"x": [0.833], "y": [0.812]}, "o": {"x": [0.167], "y": [0.15]}, "t": 109, "s": [-15.09]}, {"i": {"x": [0.833], "y": [0.811]}, "o": {"x": [0.167], "y": [0.15]}, "t": 110, "s": [-14.484]}, {"i": {"x": [0.833], "y": [0.809]}, "o": {"x": [0.167], "y": [0.149]}, "t": 111, "s": [-13.727]}, {"i": {"x": [0.833], "y": [0.805]}, "o": {"x": [0.167], "y": [0.148]}, "t": 112, "s": [-12.768]}, {"i": {"x": [0.833], "y": [0.798]}, "o": {"x": [0.167], "y": [0.145]}, "t": 113, "s": [-11.527]}, {"i": {"x": [0.833], "y": [0.796]}, "o": {"x": [0.167], "y": [0.142]}, "t": 114, "s": [-9.862]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.141]}, "t": 115, "s": [-7.5]}, {"i": {"x": [0.833], "y": [0.874]}, "o": {"x": [0.167], "y": [0.166]}, "t": 116, "s": [-4.072]}, {"i": {"x": [0.833], "y": [0.884]}, "o": {"x": [0.167], "y": [0.246]}, "t": 117, "s": [-0.628]}, {"i": {"x": [0.833], "y": [0.896]}, "o": {"x": [0.167], "y": [0.294]}, "t": 118, "s": [1.133]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0.421]}, "t": 119, "s": [1.829]}, {"i": {"x": [0.833], "y": [0.579]}, "o": {"x": [0.167], "y": [0]}, "t": 120, "s": [2]}, {"i": {"x": [0.833], "y": [0.706]}, "o": {"x": [0.167], "y": [0.104]}, "t": 121, "s": [1.829]}, {"i": {"x": [0.833], "y": [0.754]}, "o": {"x": [0.167], "y": [0.116]}, "t": 122, "s": [1.133]}, {"i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.126]}, "t": 123, "s": [-0.628]}, {"i": {"x": [0.833], "y": [0.859]}, "o": {"x": [0.167], "y": [0.167]}, "t": 124, "s": [-4.072]}, {"i": {"x": [0.833], "y": [0.858]}, "o": {"x": [0.167], "y": [0.204]}, "t": 125, "s": [-7.5]}, {"i": {"x": [0.833], "y": [0.855]}, "o": {"x": [0.167], "y": [0.202]}, "t": 126, "s": [-9.862]}, {"i": {"x": [0.833], "y": [0.852]}, "o": {"x": [0.167], "y": [0.195]}, "t": 127, "s": [-11.527]}, {"i": {"x": [0.833], "y": [0.851]}, "o": {"x": [0.167], "y": [0.191]}, "t": 128, "s": [-12.768]}, {"i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.189]}, "t": 129, "s": [-13.727]}, {"i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.188]}, "t": 130, "s": [-14.484]}, {"i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.187]}, "t": 131, "s": [-15.09]}, {"i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.187]}, "t": 132, "s": [-15.577]}, {"i": {"x": [0.833], "y": [0.851]}, "o": {"x": [0.167], "y": [0.188]}, "t": 133, "s": [-15.967]}, {"i": {"x": [0.833], "y": [0.853]}, "o": {"x": [0.167], "y": [0.19]}, "t": 134, "s": [-16.278]}, {"i": {"x": [0.833], "y": [0.856]}, "o": {"x": [0.167], "y": [0.193]}, "t": 135, "s": [-16.521]}, {"i": {"x": [0.833], "y": [0.861]}, "o": {"x": [0.167], "y": [0.198]}, "t": 136, "s": [-16.706]}, {"i": {"x": [0.833], "y": [0.869]}, "o": {"x": [0.167], "y": [0.207]}, "t": 137, "s": [-16.841]}, {"i": {"x": [0.833], "y": [0.89]}, "o": {"x": [0.167], "y": [0.23]}, "t": 138, "s": [-16.932]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0.346]}, "t": 139, "s": [-16.984]}, {"i": {"x": [0.833], "y": [0.654]}, "o": {"x": [0.167], "y": [0]}, "t": 140, "s": [-17]}, {"i": {"x": [0.833], "y": [0.77]}, "o": {"x": [0.167], "y": [0.11]}, "t": 141, "s": [-16.984]}, {"i": {"x": [0.833], "y": [0.793]}, "o": {"x": [0.167], "y": [0.131]}, "t": 142, "s": [-16.932]}, {"i": {"x": [0.833], "y": [0.802]}, "o": {"x": [0.167], "y": [0.139]}, "t": 143, "s": [-16.841]}, {"i": {"x": [0.833], "y": [0.807]}, "o": {"x": [0.167], "y": [0.144]}, "t": 144, "s": [-16.706]}, {"i": {"x": [0.833], "y": [0.81]}, "o": {"x": [0.167], "y": [0.147]}, "t": 145, "s": [-16.521]}, {"i": {"x": [0.833], "y": [0.812]}, "o": {"x": [0.167], "y": [0.149]}, "t": 146, "s": [-16.278]}, {"i": {"x": [0.833], "y": [0.813]}, "o": {"x": [0.167], "y": [0.15]}, "t": 147, "s": [-15.967]}, {"i": {"x": [0.833], "y": [0.813]}, "o": {"x": [0.167], "y": [0.15]}, "t": 148, "s": [-15.577]}, {"i": {"x": [0.833], "y": [0.812]}, "o": {"x": [0.167], "y": [0.15]}, "t": 149, "s": [-15.09]}, {"i": {"x": [0.833], "y": [0.811]}, "o": {"x": [0.167], "y": [0.15]}, "t": 150, "s": [-14.484]}, {"i": {"x": [0.833], "y": [0.809]}, "o": {"x": [0.167], "y": [0.149]}, "t": 151, "s": [-13.727]}, {"i": {"x": [0.833], "y": [0.805]}, "o": {"x": [0.167], "y": [0.148]}, "t": 152, "s": [-12.768]}, {"i": {"x": [0.833], "y": [0.798]}, "o": {"x": [0.167], "y": [0.145]}, "t": 153, "s": [-11.527]}, {"i": {"x": [0.833], "y": [0.796]}, "o": {"x": [0.167], "y": [0.142]}, "t": 154, "s": [-9.862]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.141]}, "t": 155, "s": [-7.5]}, {"i": {"x": [0.833], "y": [0.874]}, "o": {"x": [0.167], "y": [0.166]}, "t": 156, "s": [-4.072]}, {"i": {"x": [0.833], "y": [0.884]}, "o": {"x": [0.167], "y": [0.246]}, "t": 157, "s": [-0.628]}, {"i": {"x": [0.833], "y": [0.896]}, "o": {"x": [0.167], "y": [0.294]}, "t": 158, "s": [1.133]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0.421]}, "t": 159, "s": [1.829]}, {"i": {"x": [0.833], "y": [0.579]}, "o": {"x": [0.167], "y": [0]}, "t": 160, "s": [2]}, {"i": {"x": [0.833], "y": [0.706]}, "o": {"x": [0.167], "y": [0.104]}, "t": 161, "s": [1.829]}, {"i": {"x": [0.833], "y": [0.754]}, "o": {"x": [0.167], "y": [0.116]}, "t": 162, "s": [1.133]}, {"i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.126]}, "t": 163, "s": [-0.628]}, {"i": {"x": [0.833], "y": [0.859]}, "o": {"x": [0.167], "y": [0.167]}, "t": 164, "s": [-4.072]}, {"i": {"x": [0.833], "y": [0.858]}, "o": {"x": [0.167], "y": [0.204]}, "t": 165, "s": [-7.5]}, {"i": {"x": [0.833], "y": [0.855]}, "o": {"x": [0.167], "y": [0.202]}, "t": 166, "s": [-9.862]}, {"i": {"x": [0.833], "y": [0.852]}, "o": {"x": [0.167], "y": [0.195]}, "t": 167, "s": [-11.527]}, {"i": {"x": [0.833], "y": [0.851]}, "o": {"x": [0.167], "y": [0.191]}, "t": 168, "s": [-12.768]}, {"i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.189]}, "t": 169, "s": [-13.727]}, {"i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.188]}, "t": 170, "s": [-14.484]}, {"i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.187]}, "t": 171, "s": [-15.09]}, {"i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.187]}, "t": 172, "s": [-15.577]}, {"i": {"x": [0.833], "y": [0.851]}, "o": {"x": [0.167], "y": [0.188]}, "t": 173, "s": [-15.967]}, {"i": {"x": [0.833], "y": [0.853]}, "o": {"x": [0.167], "y": [0.19]}, "t": 174, "s": [-16.278]}, {"i": {"x": [0.833], "y": [0.856]}, "o": {"x": [0.167], "y": [0.193]}, "t": 175, "s": [-16.521]}, {"i": {"x": [0.833], "y": [0.861]}, "o": {"x": [0.167], "y": [0.198]}, "t": 176, "s": [-16.706]}, {"i": {"x": [0.833], "y": [0.869]}, "o": {"x": [0.167], "y": [0.207]}, "t": 177, "s": [-16.841]}, {"i": {"x": [0.833], "y": [0.89]}, "o": {"x": [0.167], "y": [0.23]}, "t": 178, "s": [-16.932]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0.346]}, "t": 179, "s": [-16.984]}, {"i": {"x": [0.833], "y": [0.654]}, "o": {"x": [0.167], "y": [0]}, "t": 180, "s": [-17]}, {"i": {"x": [0.833], "y": [0.77]}, "o": {"x": [0.167], "y": [0.11]}, "t": 181, "s": [-16.984]}, {"i": {"x": [0.833], "y": [0.793]}, "o": {"x": [0.167], "y": [0.131]}, "t": 182, "s": [-16.932]}, {"i": {"x": [0.833], "y": [0.802]}, "o": {"x": [0.167], "y": [0.139]}, "t": 183, "s": [-16.841]}, {"i": {"x": [0.833], "y": [0.807]}, "o": {"x": [0.167], "y": [0.144]}, "t": 184, "s": [-16.706]}, {"i": {"x": [0.833], "y": [0.81]}, "o": {"x": [0.167], "y": [0.147]}, "t": 185, "s": [-16.521]}, {"i": {"x": [0.833], "y": [0.812]}, "o": {"x": [0.167], "y": [0.149]}, "t": 186, "s": [-16.278]}, {"i": {"x": [0.833], "y": [0.813]}, "o": {"x": [0.167], "y": [0.15]}, "t": 187, "s": [-15.967]}, {"i": {"x": [0.833], "y": [0.813]}, "o": {"x": [0.167], "y": [0.15]}, "t": 188, "s": [-15.577]}, {"i": {"x": [0.833], "y": [0.812]}, "o": {"x": [0.167], "y": [0.15]}, "t": 189, "s": [-15.09]}, {"i": {"x": [0.833], "y": [0.811]}, "o": {"x": [0.167], "y": [0.15]}, "t": 190, "s": [-14.484]}, {"i": {"x": [0.833], "y": [0.809]}, "o": {"x": [0.167], "y": [0.149]}, "t": 191, "s": [-13.727]}, {"i": {"x": [0.833], "y": [0.805]}, "o": {"x": [0.167], "y": [0.148]}, "t": 192, "s": [-12.768]}, {"i": {"x": [0.833], "y": [0.798]}, "o": {"x": [0.167], "y": [0.145]}, "t": 193, "s": [-11.527]}, {"i": {"x": [0.833], "y": [0.796]}, "o": {"x": [0.167], "y": [0.142]}, "t": 194, "s": [-9.862]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.141]}, "t": 195, "s": [-7.5]}, {"i": {"x": [0.833], "y": [0.874]}, "o": {"x": [0.167], "y": [0.166]}, "t": 196, "s": [-4.072]}, {"i": {"x": [0.833], "y": [0.884]}, "o": {"x": [0.167], "y": [0.246]}, "t": 197, "s": [-0.628]}, {"i": {"x": [0.833], "y": [0.896]}, "o": {"x": [0.167], "y": [0.294]}, "t": 198, "s": [1.133]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0.421]}, "t": 199, "s": [1.829]}, {"i": {"x": [0.833], "y": [0.579]}, "o": {"x": [0.167], "y": [0]}, "t": 200, "s": [2]}, {"i": {"x": [0.833], "y": [0.706]}, "o": {"x": [0.167], "y": [0.104]}, "t": 201, "s": [1.829]}, {"i": {"x": [0.833], "y": [0.754]}, "o": {"x": [0.167], "y": [0.116]}, "t": 202, "s": [1.133]}, {"i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.126]}, "t": 203, "s": [-0.628]}, {"i": {"x": [0.833], "y": [0.859]}, "o": {"x": [0.167], "y": [0.167]}, "t": 204, "s": [-4.072]}, {"i": {"x": [0.833], "y": [0.858]}, "o": {"x": [0.167], "y": [0.204]}, "t": 205, "s": [-7.5]}, {"i": {"x": [0.833], "y": [0.855]}, "o": {"x": [0.167], "y": [0.202]}, "t": 206, "s": [-9.862]}, {"i": {"x": [0.833], "y": [0.852]}, "o": {"x": [0.167], "y": [0.195]}, "t": 207, "s": [-11.527]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.191]}, "t": 208, "s": [-12.768]}, {"t": 209.000008512745, "s": [-13.727]}], "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 8", "np": 3, "cix": 2, "bm": 0, "ix": 8, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-0.061, -0.308], [-0.166, -1.219], [3.171, -0.092], [1.975, 3.687], [0.618, 3.463]], "o": [[0, 0], [0.229, 1.137], [0.16, 1.169], [-3.171, 0.092], [0.111, -0.677], [0, 0]], "v": [[6.733, -1.279], [6.763, -0.775], [7.873, 4.483], [3.631, 7.842], [-6.955, 3.186], [-8.033, -7.934]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.525, 0.328999986836, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [37.179, 40.7], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 9", "np": 3, "cix": 2, "bm": 0, "ix": 9, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-1.5, -0.693], [0.528, -3.19], [0, 0], [5.326, 5.757], [1.862, 1.912]], "o": [[13.114, -1.205], [0.229, 2.639], [-1.027, 6.197], [0, 0], [-3.749, -4.052], [3.143, -2.682]], "v": [[-7.615, -16.538], [13.035, -14.794], [12.844, -5.48], [9.348, 17.743], [-4.07, -2.002], [-13.372, -11.783]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.882000014361, 0.910000011968, 0.948999980852, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [38.034, 58.736], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 10", "np": 3, "cix": 2, "bm": 0, "ix": 10, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[6.227, 7.887], [10.793, 7.472], [2.341, -0.295], [0.318, -0.885], [-3.563, -6.735], [-1.404, -4.905], [-8.197, 1.345], [0, 0]], "o": [[-6.227, -7.887], [-3.291, -2.279], [-1.447, 1.518], [-1.363, 3.792], [3.562, 6.736], [1.28, 4.464], [0, 0], [0, 0]], "v": [[-0.105, 11.4], [-7.992, -30.939], [-16.45, -33.498], [-19.287, -29.764], [-16.909, -8.877], [-11.87, 28.187], [8.863, 32.449], [20.65, 20.118]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0, 0.216000007181, 0.513999968884, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [33.77, 85.671], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 11", "np": 3, "cix": 2, "bm": 0, "ix": 11, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[15.381, -1.414], [1.363, -3.792], [-3.563, -6.735], [-1.405, -4.905], [-8.201, 2.009], [2.618, 9.31], [-0.311, 4.375], [2.38, 7.223], [0, 0]], "o": [[0, 0], [-1.363, 3.792], [3.562, 6.735], [1.405, 4.904], [8.201, -2.009], [-3.867, -13.749], [0.312, -4.375], [-0.502, -1.524], [0, 0]], "v": [[-8.727, -39.04], [-25.174, -23.307], [-22.796, -2.421], [-17.757, 34.643], [5.307, 38.444], [23.918, 20.259], [21.28, -11.715], [18.102, -34.208], [12.227, -37.861]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0, 0.275, 0.651000019148, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [39.657, 79.214], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 12", "np": 3, "cix": 2, "bm": 0, "ix": 12, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[2.292, 9.6], [8.582, -2.458], [3.774, -8.473], [-4.149, -4.19], [-0.835, -0.845], [-0.314, -0.288], [0, 0], [0, 0], [-2.31, -0.566], [-2.289, 2.697]], "o": [[-2.291, -9.6], [0, 0], [-3.774, 8.472], [1.747, 1.718], [0.302, 0.32], [0.058, 0.06], [0, 0], [2.818, 2.553], [3.842, 0.941], [2.289, -2.697]], "v": [[16.721, -6.27], [-2.1, -18.533], [-15.238, -8.653], [-8.38, 10.657], [-4.477, 14.547], [-3.563, 15.48], [-3.473, 15.572], [-3.464, 15.563], [4.82, 20.05], [15.397, 17.428]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.525, 0.328999986836, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [34.003, 22.631], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 13", "np": 3, "cix": 2, "bm": 0, "ix": 13, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-0.061, -0.308], [-0.166, -1.219], [3.171, -0.092], [1.975, 3.687], [0.618, 3.463]], "o": [[0, 0], [0.229, 1.137], [0.16, 1.169], [-3.171, 0.092], [0.111, -0.677], [0, 0]], "v": [[6.733, -1.279], [6.763, -0.775], [7.873, 4.483], [3.631, 7.842], [-6.955, 3.186], [-8.033, -7.934]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.882000014361, 0.419999994016, 0.250999989229, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [37.179, 40.7], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 14", "np": 3, "cix": 2, "bm": 0, "ix": 14, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [3.115, 0.295], [1.218, -5.299], [-2.81, -1.542], [-4.358, 0.023], [2.827, 4.656]], "o": [[0, 0], [-3.737, -0.291], [-1.217, 5.299], [2.81, 1.542], [4.358, -0.023], [-2.826, -4.654]], "v": [[6.973, -2.072], [-11.465, -15.051], [-21.083, -8.773], [-16.667, 2.1], [15.158, 15.319], [19.473, 6.89]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.882000014361, 0.910000011968, 0.948999980852, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [55.424, 48.368], "ix": 2}, "a": {"a": 0, "k": [-8, -9], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.579]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [2]}, {"i": {"x": [0.833], "y": [0.706]}, "o": {"x": [0.167], "y": [0.104]}, "t": 1, "s": [1.856]}, {"i": {"x": [0.833], "y": [0.754]}, "o": {"x": [0.167], "y": [0.116]}, "t": 2, "s": [1.27]}, {"i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.126]}, "t": 3, "s": [-0.213]}, {"i": {"x": [0.833], "y": [0.859]}, "o": {"x": [0.167], "y": [0.167]}, "t": 4, "s": [-3.114]}, {"i": {"x": [0.833], "y": [0.858]}, "o": {"x": [0.167], "y": [0.204]}, "t": 5, "s": [-6]}, {"i": {"x": [0.833], "y": [0.855]}, "o": {"x": [0.167], "y": [0.202]}, "t": 6, "s": [-7.989]}, {"i": {"x": [0.833], "y": [0.852]}, "o": {"x": [0.167], "y": [0.195]}, "t": 7, "s": [-9.391]}, {"i": {"x": [0.833], "y": [0.851]}, "o": {"x": [0.167], "y": [0.191]}, "t": 8, "s": [-10.436]}, {"i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.189]}, "t": 9, "s": [-11.244]}, {"i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.188]}, "t": 10, "s": [-11.881]}, {"i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.187]}, "t": 11, "s": [-12.391]}, {"i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.187]}, "t": 12, "s": [-12.801]}, {"i": {"x": [0.833], "y": [0.851]}, "o": {"x": [0.167], "y": [0.188]}, "t": 13, "s": [-13.13]}, {"i": {"x": [0.833], "y": [0.853]}, "o": {"x": [0.167], "y": [0.19]}, "t": 14, "s": [-13.392]}, {"i": {"x": [0.833], "y": [0.856]}, "o": {"x": [0.167], "y": [0.193]}, "t": 15, "s": [-13.596]}, {"i": {"x": [0.833], "y": [0.861]}, "o": {"x": [0.167], "y": [0.198]}, "t": 16, "s": [-13.753]}, {"i": {"x": [0.833], "y": [0.869]}, "o": {"x": [0.167], "y": [0.207]}, "t": 17, "s": [-13.866]}, {"i": {"x": [0.833], "y": [0.89]}, "o": {"x": [0.167], "y": [0.23]}, "t": 18, "s": [-13.943]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0.346]}, "t": 19, "s": [-13.986]}, {"i": {"x": [0.833], "y": [0.654]}, "o": {"x": [0.167], "y": [0]}, "t": 20, "s": [-14]}, {"i": {"x": [0.833], "y": [0.77]}, "o": {"x": [0.167], "y": [0.11]}, "t": 21, "s": [-13.986]}, {"i": {"x": [0.833], "y": [0.793]}, "o": {"x": [0.167], "y": [0.131]}, "t": 22, "s": [-13.943]}, {"i": {"x": [0.833], "y": [0.802]}, "o": {"x": [0.167], "y": [0.139]}, "t": 23, "s": [-13.866]}, {"i": {"x": [0.833], "y": [0.807]}, "o": {"x": [0.167], "y": [0.144]}, "t": 24, "s": [-13.753]}, {"i": {"x": [0.833], "y": [0.81]}, "o": {"x": [0.167], "y": [0.147]}, "t": 25, "s": [-13.596]}, {"i": {"x": [0.833], "y": [0.812]}, "o": {"x": [0.167], "y": [0.149]}, "t": 26, "s": [-13.392]}, {"i": {"x": [0.833], "y": [0.813]}, "o": {"x": [0.167], "y": [0.15]}, "t": 27, "s": [-13.13]}, {"i": {"x": [0.833], "y": [0.813]}, "o": {"x": [0.167], "y": [0.15]}, "t": 28, "s": [-12.801]}, {"i": {"x": [0.833], "y": [0.812]}, "o": {"x": [0.167], "y": [0.15]}, "t": 29, "s": [-12.391]}, {"i": {"x": [0.833], "y": [0.811]}, "o": {"x": [0.167], "y": [0.15]}, "t": 30, "s": [-11.881]}, {"i": {"x": [0.833], "y": [0.809]}, "o": {"x": [0.167], "y": [0.149]}, "t": 31, "s": [-11.244]}, {"i": {"x": [0.833], "y": [0.805]}, "o": {"x": [0.167], "y": [0.148]}, "t": 32, "s": [-10.436]}, {"i": {"x": [0.833], "y": [0.798]}, "o": {"x": [0.167], "y": [0.145]}, "t": 33, "s": [-9.391]}, {"i": {"x": [0.833], "y": [0.796]}, "o": {"x": [0.167], "y": [0.142]}, "t": 34, "s": [-7.989]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.141]}, "t": 35, "s": [-6]}, {"i": {"x": [0.833], "y": [0.874]}, "o": {"x": [0.167], "y": [0.166]}, "t": 36, "s": [-3.114]}, {"i": {"x": [0.833], "y": [0.884]}, "o": {"x": [0.167], "y": [0.246]}, "t": 37, "s": [-0.213]}, {"i": {"x": [0.833], "y": [0.896]}, "o": {"x": [0.167], "y": [0.294]}, "t": 38, "s": [1.27]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0.421]}, "t": 39, "s": [1.856]}, {"i": {"x": [0.833], "y": [0.579]}, "o": {"x": [0.167], "y": [0]}, "t": 40, "s": [2]}, {"i": {"x": [0.833], "y": [0.706]}, "o": {"x": [0.167], "y": [0.104]}, "t": 41, "s": [1.856]}, {"i": {"x": [0.833], "y": [0.754]}, "o": {"x": [0.167], "y": [0.116]}, "t": 42, "s": [1.27]}, {"i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.126]}, "t": 43, "s": [-0.213]}, {"i": {"x": [0.833], "y": [0.859]}, "o": {"x": [0.167], "y": [0.167]}, "t": 44, "s": [-3.114]}, {"i": {"x": [0.833], "y": [0.858]}, "o": {"x": [0.167], "y": [0.204]}, "t": 45, "s": [-6]}, {"i": {"x": [0.833], "y": [0.855]}, "o": {"x": [0.167], "y": [0.202]}, "t": 46, "s": [-7.989]}, {"i": {"x": [0.833], "y": [0.852]}, "o": {"x": [0.167], "y": [0.195]}, "t": 47, "s": [-9.391]}, {"i": {"x": [0.833], "y": [0.851]}, "o": {"x": [0.167], "y": [0.191]}, "t": 48, "s": [-10.436]}, {"i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.189]}, "t": 49, "s": [-11.244]}, {"i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.188]}, "t": 50, "s": [-11.881]}, {"i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.187]}, "t": 51, "s": [-12.391]}, {"i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.187]}, "t": 52, "s": [-12.801]}, {"i": {"x": [0.833], "y": [0.851]}, "o": {"x": [0.167], "y": [0.188]}, "t": 53, "s": [-13.13]}, {"i": {"x": [0.833], "y": [0.853]}, "o": {"x": [0.167], "y": [0.19]}, "t": 54, "s": [-13.392]}, {"i": {"x": [0.833], "y": [0.856]}, "o": {"x": [0.167], "y": [0.193]}, "t": 55, "s": [-13.596]}, {"i": {"x": [0.833], "y": [0.861]}, "o": {"x": [0.167], "y": [0.198]}, "t": 56, "s": [-13.753]}, {"i": {"x": [0.833], "y": [0.869]}, "o": {"x": [0.167], "y": [0.207]}, "t": 57, "s": [-13.866]}, {"i": {"x": [0.833], "y": [0.89]}, "o": {"x": [0.167], "y": [0.23]}, "t": 58, "s": [-13.943]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0.346]}, "t": 59, "s": [-13.986]}, {"i": {"x": [0.833], "y": [0.654]}, "o": {"x": [0.167], "y": [0]}, "t": 60, "s": [-14]}, {"i": {"x": [0.833], "y": [0.77]}, "o": {"x": [0.167], "y": [0.11]}, "t": 61, "s": [-13.986]}, {"i": {"x": [0.833], "y": [0.793]}, "o": {"x": [0.167], "y": [0.131]}, "t": 62, "s": [-13.943]}, {"i": {"x": [0.833], "y": [0.802]}, "o": {"x": [0.167], "y": [0.139]}, "t": 63, "s": [-13.866]}, {"i": {"x": [0.833], "y": [0.807]}, "o": {"x": [0.167], "y": [0.144]}, "t": 64, "s": [-13.753]}, {"i": {"x": [0.833], "y": [0.81]}, "o": {"x": [0.167], "y": [0.147]}, "t": 65, "s": [-13.596]}, {"i": {"x": [0.833], "y": [0.812]}, "o": {"x": [0.167], "y": [0.149]}, "t": 66, "s": [-13.392]}, {"i": {"x": [0.833], "y": [0.813]}, "o": {"x": [0.167], "y": [0.15]}, "t": 67, "s": [-13.13]}, {"i": {"x": [0.833], "y": [0.813]}, "o": {"x": [0.167], "y": [0.15]}, "t": 68, "s": [-12.801]}, {"i": {"x": [0.833], "y": [0.812]}, "o": {"x": [0.167], "y": [0.15]}, "t": 69, "s": [-12.391]}, {"i": {"x": [0.833], "y": [0.811]}, "o": {"x": [0.167], "y": [0.15]}, "t": 70, "s": [-11.881]}, {"i": {"x": [0.833], "y": [0.809]}, "o": {"x": [0.167], "y": [0.149]}, "t": 71, "s": [-11.244]}, {"i": {"x": [0.833], "y": [0.805]}, "o": {"x": [0.167], "y": [0.148]}, "t": 72, "s": [-10.436]}, {"i": {"x": [0.833], "y": [0.798]}, "o": {"x": [0.167], "y": [0.145]}, "t": 73, "s": [-9.391]}, {"i": {"x": [0.833], "y": [0.796]}, "o": {"x": [0.167], "y": [0.142]}, "t": 74, "s": [-7.989]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.141]}, "t": 75, "s": [-6]}, {"i": {"x": [0.833], "y": [0.874]}, "o": {"x": [0.167], "y": [0.166]}, "t": 76, "s": [-3.114]}, {"i": {"x": [0.833], "y": [0.884]}, "o": {"x": [0.167], "y": [0.246]}, "t": 77, "s": [-0.213]}, {"i": {"x": [0.833], "y": [0.896]}, "o": {"x": [0.167], "y": [0.294]}, "t": 78, "s": [1.27]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0.421]}, "t": 79, "s": [1.856]}, {"i": {"x": [0.833], "y": [0.579]}, "o": {"x": [0.167], "y": [0]}, "t": 80, "s": [2]}, {"i": {"x": [0.833], "y": [0.706]}, "o": {"x": [0.167], "y": [0.104]}, "t": 81, "s": [1.856]}, {"i": {"x": [0.833], "y": [0.754]}, "o": {"x": [0.167], "y": [0.116]}, "t": 82, "s": [1.27]}, {"i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.126]}, "t": 83, "s": [-0.213]}, {"i": {"x": [0.833], "y": [0.859]}, "o": {"x": [0.167], "y": [0.167]}, "t": 84, "s": [-3.114]}, {"i": {"x": [0.833], "y": [0.858]}, "o": {"x": [0.167], "y": [0.204]}, "t": 85, "s": [-6]}, {"i": {"x": [0.833], "y": [0.855]}, "o": {"x": [0.167], "y": [0.202]}, "t": 86, "s": [-7.989]}, {"i": {"x": [0.833], "y": [0.852]}, "o": {"x": [0.167], "y": [0.195]}, "t": 87, "s": [-9.391]}, {"i": {"x": [0.833], "y": [0.851]}, "o": {"x": [0.167], "y": [0.191]}, "t": 88, "s": [-10.436]}, {"i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.189]}, "t": 89, "s": [-11.244]}, {"i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.188]}, "t": 90, "s": [-11.881]}, {"i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.187]}, "t": 91, "s": [-12.391]}, {"i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.187]}, "t": 92, "s": [-12.801]}, {"i": {"x": [0.833], "y": [0.851]}, "o": {"x": [0.167], "y": [0.188]}, "t": 93, "s": [-13.13]}, {"i": {"x": [0.833], "y": [0.853]}, "o": {"x": [0.167], "y": [0.19]}, "t": 94, "s": [-13.392]}, {"i": {"x": [0.833], "y": [0.856]}, "o": {"x": [0.167], "y": [0.193]}, "t": 95, "s": [-13.596]}, {"i": {"x": [0.833], "y": [0.861]}, "o": {"x": [0.167], "y": [0.198]}, "t": 96, "s": [-13.753]}, {"i": {"x": [0.833], "y": [0.869]}, "o": {"x": [0.167], "y": [0.207]}, "t": 97, "s": [-13.866]}, {"i": {"x": [0.833], "y": [0.89]}, "o": {"x": [0.167], "y": [0.23]}, "t": 98, "s": [-13.943]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0.346]}, "t": 99, "s": [-13.986]}, {"i": {"x": [0.833], "y": [0.654]}, "o": {"x": [0.167], "y": [0]}, "t": 100, "s": [-14]}, {"i": {"x": [0.833], "y": [0.77]}, "o": {"x": [0.167], "y": [0.11]}, "t": 101, "s": [-13.986]}, {"i": {"x": [0.833], "y": [0.793]}, "o": {"x": [0.167], "y": [0.131]}, "t": 102, "s": [-13.943]}, {"i": {"x": [0.833], "y": [0.802]}, "o": {"x": [0.167], "y": [0.139]}, "t": 103, "s": [-13.866]}, {"i": {"x": [0.833], "y": [0.807]}, "o": {"x": [0.167], "y": [0.144]}, "t": 104, "s": [-13.753]}, {"i": {"x": [0.833], "y": [0.81]}, "o": {"x": [0.167], "y": [0.147]}, "t": 105, "s": [-13.596]}, {"i": {"x": [0.833], "y": [0.812]}, "o": {"x": [0.167], "y": [0.149]}, "t": 106, "s": [-13.392]}, {"i": {"x": [0.833], "y": [0.813]}, "o": {"x": [0.167], "y": [0.15]}, "t": 107, "s": [-13.13]}, {"i": {"x": [0.833], "y": [0.813]}, "o": {"x": [0.167], "y": [0.15]}, "t": 108, "s": [-12.801]}, {"i": {"x": [0.833], "y": [0.812]}, "o": {"x": [0.167], "y": [0.15]}, "t": 109, "s": [-12.391]}, {"i": {"x": [0.833], "y": [0.811]}, "o": {"x": [0.167], "y": [0.15]}, "t": 110, "s": [-11.881]}, {"i": {"x": [0.833], "y": [0.809]}, "o": {"x": [0.167], "y": [0.149]}, "t": 111, "s": [-11.244]}, {"i": {"x": [0.833], "y": [0.805]}, "o": {"x": [0.167], "y": [0.148]}, "t": 112, "s": [-10.436]}, {"i": {"x": [0.833], "y": [0.798]}, "o": {"x": [0.167], "y": [0.145]}, "t": 113, "s": [-9.391]}, {"i": {"x": [0.833], "y": [0.796]}, "o": {"x": [0.167], "y": [0.142]}, "t": 114, "s": [-7.989]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.141]}, "t": 115, "s": [-6]}, {"i": {"x": [0.833], "y": [0.874]}, "o": {"x": [0.167], "y": [0.166]}, "t": 116, "s": [-3.114]}, {"i": {"x": [0.833], "y": [0.884]}, "o": {"x": [0.167], "y": [0.246]}, "t": 117, "s": [-0.213]}, {"i": {"x": [0.833], "y": [0.896]}, "o": {"x": [0.167], "y": [0.294]}, "t": 118, "s": [1.27]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0.421]}, "t": 119, "s": [1.856]}, {"i": {"x": [0.833], "y": [0.579]}, "o": {"x": [0.167], "y": [0]}, "t": 120, "s": [2]}, {"i": {"x": [0.833], "y": [0.706]}, "o": {"x": [0.167], "y": [0.104]}, "t": 121, "s": [1.856]}, {"i": {"x": [0.833], "y": [0.754]}, "o": {"x": [0.167], "y": [0.116]}, "t": 122, "s": [1.27]}, {"i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.126]}, "t": 123, "s": [-0.213]}, {"i": {"x": [0.833], "y": [0.859]}, "o": {"x": [0.167], "y": [0.167]}, "t": 124, "s": [-3.114]}, {"i": {"x": [0.833], "y": [0.858]}, "o": {"x": [0.167], "y": [0.204]}, "t": 125, "s": [-6]}, {"i": {"x": [0.833], "y": [0.855]}, "o": {"x": [0.167], "y": [0.202]}, "t": 126, "s": [-7.989]}, {"i": {"x": [0.833], "y": [0.852]}, "o": {"x": [0.167], "y": [0.195]}, "t": 127, "s": [-9.391]}, {"i": {"x": [0.833], "y": [0.851]}, "o": {"x": [0.167], "y": [0.191]}, "t": 128, "s": [-10.436]}, {"i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.189]}, "t": 129, "s": [-11.244]}, {"i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.188]}, "t": 130, "s": [-11.881]}, {"i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.187]}, "t": 131, "s": [-12.391]}, {"i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.187]}, "t": 132, "s": [-12.801]}, {"i": {"x": [0.833], "y": [0.851]}, "o": {"x": [0.167], "y": [0.188]}, "t": 133, "s": [-13.13]}, {"i": {"x": [0.833], "y": [0.853]}, "o": {"x": [0.167], "y": [0.19]}, "t": 134, "s": [-13.392]}, {"i": {"x": [0.833], "y": [0.856]}, "o": {"x": [0.167], "y": [0.193]}, "t": 135, "s": [-13.596]}, {"i": {"x": [0.833], "y": [0.861]}, "o": {"x": [0.167], "y": [0.198]}, "t": 136, "s": [-13.753]}, {"i": {"x": [0.833], "y": [0.869]}, "o": {"x": [0.167], "y": [0.207]}, "t": 137, "s": [-13.866]}, {"i": {"x": [0.833], "y": [0.89]}, "o": {"x": [0.167], "y": [0.23]}, "t": 138, "s": [-13.943]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0.346]}, "t": 139, "s": [-13.986]}, {"i": {"x": [0.833], "y": [0.654]}, "o": {"x": [0.167], "y": [0]}, "t": 140, "s": [-14]}, {"i": {"x": [0.833], "y": [0.77]}, "o": {"x": [0.167], "y": [0.11]}, "t": 141, "s": [-13.986]}, {"i": {"x": [0.833], "y": [0.793]}, "o": {"x": [0.167], "y": [0.131]}, "t": 142, "s": [-13.943]}, {"i": {"x": [0.833], "y": [0.802]}, "o": {"x": [0.167], "y": [0.139]}, "t": 143, "s": [-13.866]}, {"i": {"x": [0.833], "y": [0.807]}, "o": {"x": [0.167], "y": [0.144]}, "t": 144, "s": [-13.753]}, {"i": {"x": [0.833], "y": [0.81]}, "o": {"x": [0.167], "y": [0.147]}, "t": 145, "s": [-13.596]}, {"i": {"x": [0.833], "y": [0.812]}, "o": {"x": [0.167], "y": [0.149]}, "t": 146, "s": [-13.392]}, {"i": {"x": [0.833], "y": [0.813]}, "o": {"x": [0.167], "y": [0.15]}, "t": 147, "s": [-13.13]}, {"i": {"x": [0.833], "y": [0.813]}, "o": {"x": [0.167], "y": [0.15]}, "t": 148, "s": [-12.801]}, {"i": {"x": [0.833], "y": [0.812]}, "o": {"x": [0.167], "y": [0.15]}, "t": 149, "s": [-12.391]}, {"i": {"x": [0.833], "y": [0.811]}, "o": {"x": [0.167], "y": [0.15]}, "t": 150, "s": [-11.881]}, {"i": {"x": [0.833], "y": [0.809]}, "o": {"x": [0.167], "y": [0.149]}, "t": 151, "s": [-11.244]}, {"i": {"x": [0.833], "y": [0.805]}, "o": {"x": [0.167], "y": [0.148]}, "t": 152, "s": [-10.436]}, {"i": {"x": [0.833], "y": [0.798]}, "o": {"x": [0.167], "y": [0.145]}, "t": 153, "s": [-9.391]}, {"i": {"x": [0.833], "y": [0.796]}, "o": {"x": [0.167], "y": [0.142]}, "t": 154, "s": [-7.989]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.141]}, "t": 155, "s": [-6]}, {"i": {"x": [0.833], "y": [0.874]}, "o": {"x": [0.167], "y": [0.166]}, "t": 156, "s": [-3.114]}, {"i": {"x": [0.833], "y": [0.884]}, "o": {"x": [0.167], "y": [0.246]}, "t": 157, "s": [-0.213]}, {"i": {"x": [0.833], "y": [0.896]}, "o": {"x": [0.167], "y": [0.294]}, "t": 158, "s": [1.27]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0.421]}, "t": 159, "s": [1.856]}, {"i": {"x": [0.833], "y": [0.579]}, "o": {"x": [0.167], "y": [0]}, "t": 160, "s": [2]}, {"i": {"x": [0.833], "y": [0.706]}, "o": {"x": [0.167], "y": [0.104]}, "t": 161, "s": [1.856]}, {"i": {"x": [0.833], "y": [0.754]}, "o": {"x": [0.167], "y": [0.116]}, "t": 162, "s": [1.27]}, {"i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.126]}, "t": 163, "s": [-0.213]}, {"i": {"x": [0.833], "y": [0.859]}, "o": {"x": [0.167], "y": [0.167]}, "t": 164, "s": [-3.114]}, {"i": {"x": [0.833], "y": [0.858]}, "o": {"x": [0.167], "y": [0.204]}, "t": 165, "s": [-6]}, {"i": {"x": [0.833], "y": [0.855]}, "o": {"x": [0.167], "y": [0.202]}, "t": 166, "s": [-7.989]}, {"i": {"x": [0.833], "y": [0.852]}, "o": {"x": [0.167], "y": [0.195]}, "t": 167, "s": [-9.391]}, {"i": {"x": [0.833], "y": [0.851]}, "o": {"x": [0.167], "y": [0.191]}, "t": 168, "s": [-10.436]}, {"i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.189]}, "t": 169, "s": [-11.244]}, {"i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.188]}, "t": 170, "s": [-11.881]}, {"i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.187]}, "t": 171, "s": [-12.391]}, {"i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.187]}, "t": 172, "s": [-12.801]}, {"i": {"x": [0.833], "y": [0.851]}, "o": {"x": [0.167], "y": [0.188]}, "t": 173, "s": [-13.13]}, {"i": {"x": [0.833], "y": [0.853]}, "o": {"x": [0.167], "y": [0.19]}, "t": 174, "s": [-13.392]}, {"i": {"x": [0.833], "y": [0.856]}, "o": {"x": [0.167], "y": [0.193]}, "t": 175, "s": [-13.596]}, {"i": {"x": [0.833], "y": [0.861]}, "o": {"x": [0.167], "y": [0.198]}, "t": 176, "s": [-13.753]}, {"i": {"x": [0.833], "y": [0.869]}, "o": {"x": [0.167], "y": [0.207]}, "t": 177, "s": [-13.866]}, {"i": {"x": [0.833], "y": [0.89]}, "o": {"x": [0.167], "y": [0.23]}, "t": 178, "s": [-13.943]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0.346]}, "t": 179, "s": [-13.986]}, {"i": {"x": [0.833], "y": [0.654]}, "o": {"x": [0.167], "y": [0]}, "t": 180, "s": [-14]}, {"i": {"x": [0.833], "y": [0.77]}, "o": {"x": [0.167], "y": [0.11]}, "t": 181, "s": [-13.986]}, {"i": {"x": [0.833], "y": [0.793]}, "o": {"x": [0.167], "y": [0.131]}, "t": 182, "s": [-13.943]}, {"i": {"x": [0.833], "y": [0.802]}, "o": {"x": [0.167], "y": [0.139]}, "t": 183, "s": [-13.866]}, {"i": {"x": [0.833], "y": [0.807]}, "o": {"x": [0.167], "y": [0.144]}, "t": 184, "s": [-13.753]}, {"i": {"x": [0.833], "y": [0.81]}, "o": {"x": [0.167], "y": [0.147]}, "t": 185, "s": [-13.596]}, {"i": {"x": [0.833], "y": [0.812]}, "o": {"x": [0.167], "y": [0.149]}, "t": 186, "s": [-13.392]}, {"i": {"x": [0.833], "y": [0.813]}, "o": {"x": [0.167], "y": [0.15]}, "t": 187, "s": [-13.13]}, {"i": {"x": [0.833], "y": [0.813]}, "o": {"x": [0.167], "y": [0.15]}, "t": 188, "s": [-12.801]}, {"i": {"x": [0.833], "y": [0.812]}, "o": {"x": [0.167], "y": [0.15]}, "t": 189, "s": [-12.391]}, {"i": {"x": [0.833], "y": [0.811]}, "o": {"x": [0.167], "y": [0.15]}, "t": 190, "s": [-11.881]}, {"i": {"x": [0.833], "y": [0.809]}, "o": {"x": [0.167], "y": [0.149]}, "t": 191, "s": [-11.244]}, {"i": {"x": [0.833], "y": [0.805]}, "o": {"x": [0.167], "y": [0.148]}, "t": 192, "s": [-10.436]}, {"i": {"x": [0.833], "y": [0.798]}, "o": {"x": [0.167], "y": [0.145]}, "t": 193, "s": [-9.391]}, {"i": {"x": [0.833], "y": [0.796]}, "o": {"x": [0.167], "y": [0.142]}, "t": 194, "s": [-7.989]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.141]}, "t": 195, "s": [-6]}, {"i": {"x": [0.833], "y": [0.874]}, "o": {"x": [0.167], "y": [0.166]}, "t": 196, "s": [-3.114]}, {"i": {"x": [0.833], "y": [0.884]}, "o": {"x": [0.167], "y": [0.246]}, "t": 197, "s": [-0.213]}, {"i": {"x": [0.833], "y": [0.896]}, "o": {"x": [0.167], "y": [0.294]}, "t": 198, "s": [1.27]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0.421]}, "t": 199, "s": [1.856]}, {"i": {"x": [0.833], "y": [0.579]}, "o": {"x": [0.167], "y": [0]}, "t": 200, "s": [2]}, {"i": {"x": [0.833], "y": [0.706]}, "o": {"x": [0.167], "y": [0.104]}, "t": 201, "s": [1.856]}, {"i": {"x": [0.833], "y": [0.754]}, "o": {"x": [0.167], "y": [0.116]}, "t": 202, "s": [1.27]}, {"i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.126]}, "t": 203, "s": [-0.213]}, {"i": {"x": [0.833], "y": [0.859]}, "o": {"x": [0.167], "y": [0.167]}, "t": 204, "s": [-3.114]}, {"i": {"x": [0.833], "y": [0.858]}, "o": {"x": [0.167], "y": [0.204]}, "t": 205, "s": [-6]}, {"i": {"x": [0.833], "y": [0.855]}, "o": {"x": [0.167], "y": [0.202]}, "t": 206, "s": [-7.989]}, {"i": {"x": [0.833], "y": [0.852]}, "o": {"x": [0.167], "y": [0.195]}, "t": 207, "s": [-9.391]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.191]}, "t": 208, "s": [-10.436]}, {"t": 209.000008512745, "s": [-11.244]}], "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 15", "np": 3, "cix": 2, "bm": 0, "ix": 15, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[26.151, -13.698], [2.491, -5.603], [-13.076, -17.435], [-5.604, 2.491], [-2.491, 8.095], [6.85, 3.113], [3.735, 11.208], [4.359, 3.113]], "o": [[0, 0], [-2.49, 5.604], [13.076, 17.433], [5.603, -2.49], [2.49, -8.095], [-6.849, -3.114], [-3.736, -11.207], [-4.358, -3.113]], "v": [[-35.49, -39.85], [-47.322, -31.755], [-36.113, 33.624], [6.85, 51.057], [47.322, 24.284], [30.51, 6.85], [15.566, -11.83], [5.604, -45.453]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.226999993418, 0.681999954523, 0.936999990426, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [50.061, 106.411], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 16", "np": 3, "cix": 2, "bm": 0, "ix": 16, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[4.572, 0], [0.015, 2.648], [-4.572, 0], [-0.014, -2.648]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.301999978458, 0.647000002394, 0.862999949736, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [44.193, 151.017], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 17", "np": 2, "cix": 2, "bm": 0, "ix": 17, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[2.331, -11.7], [2.257, 14.348], [-2.331, 11.699], [-2.257, -14.348]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.481999984442, 0.681999954523, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [41.878, 165.365], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 18", "np": 2, "cix": 2, "bm": 0, "ix": 18, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[2.315, -14.348], [2.241, 11.701], [-2.315, 14.348], [-2.242, -11.699]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.779999976065, 0.847000002394, 0.948999980852, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [46.45, 165.365], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 19", "np": 2, "cix": 2, "bm": 0, "ix": 19, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-11.674, -6.74], [11.598, -6.74], [11.674, 6.74], [-11.598, 6.74]], "o": [[11.674, 6.74], [-11.598, 6.74], [-11.674, -6.74], [11.598, -6.74]], "v": [[20.989, -12.205], [21.139, 12.205], [-21.016, 12.205], [-21.139, -12.205]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.885999971278, 0.929000016755, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [43.463, 176.846], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 20", "np": 3, "cix": 2, "bm": 0, "ix": 20, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.037, -0.289], [5.333, -2.299], [6.302, 0.081], [5.8, 2.854], [-0.017, 6.144], [-3.224, -3.493], [-5.878, -1.214], [-8.712, 8.83], [-0.013, 4.725]], "o": [[-0.565, 6.281], [-5.809, 2.503], [-6.432, -0.083], [-5.061, -2.49], [-0.012, 4.474], [4.151, 4.496], [11.469, 2.368], [3.505, -3.551], [-0.012, 0.291]], "v": [[29.733, -10.041], [17.923, 2.805], [-0.575, 6.236], [-19.374, 2.069], [-29.79, -11.082], [-26.264, 0.582], [-9.986, 8.714], [25.81, 1.108], [29.807, -10.912]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.677999997606, 0.804000016755, 0.987999949736, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [43.447, 187.851], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 21", "np": 3, "cix": 2, "bm": 0, "ix": 21, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 94.0000038286985, "st": 0, "bm": 0}, {"ddd": 0, "ind": 27, "ty": 4, "nm": "Layer 30 Outlines", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [237.464, 342.742, 0], "ix": 2}, "a": {"a": 0, "k": [160.46, 99.074, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[21.132, 12.201], [17.648, -2.475], [0, 0], [6.886, 3.976], [0, 0], [6.841, -3.975], [0, 0], [19.27, -11.199], [-16.222, -12.248], [0, 0], [-6.885, -3.976], [0, 0], [-6.841, 3.975], [0, 0], [-13.839, -7.99], [-20.996, 12.201]], "o": [[-13.839, -7.989], [0, 0], [6.84, -3.976], [0, 0], [-6.886, -3.975], [0, 0], [-21.195, -9.359], [-19.27, 11.199], [0, 0], [-6.84, 3.976], [0, 0], [6.885, 3.974], [0, 0], [-4.222, 10.234], [21.132, 12.201], [20.996, -12.201]], "v": [[143.626, 45.735], [92.725, 37.464], [155.354, 1.068], [155.272, -13.328], [11.92, -96.092], [-12.933, -96.092], [-61.919, -67.624], [-132.716, -64.872], [-137.26, -23.841], [-155.354, -13.327], [-155.273, 1.07], [-11.92, 83.835], [12.933, 83.834], [53.16, 60.457], [67.589, 89.923], [143.899, 89.923]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.528999956916, 0.681999954523, 0.866999966491, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [160.46, 99.074], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 94.0000038286985, "st": 0, "bm": 0}], "markers": []}