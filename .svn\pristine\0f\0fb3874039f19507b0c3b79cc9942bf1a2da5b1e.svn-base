<?xml version="1.0" encoding="utf-8" ?>
<Action mainAreaLayout="../../../layout/mainLayoutLanding">
  <Include name="Header_Intro"></Include>

  <Component type="Vanilla"><![CDATA[{
    templateInEvent: "html/landingPage.dot",
    css: "styles/landingPage.css",
    fullScreen: true,
    content: {
      title: "!{}",
      subtitle: "!{}",
      image: "!{LandingPage_Img0}",
      firstIsLogo: false,
      images: ["!{LandingPage_Img1}","!{LandingPage_Img2}","!{LandingPage_Img3}", "!{LandingPage_Img4}",
               "!{LandingPage_Img5}","!{LandingPage_Img6}","!{LandingPage_Img7}","!{LandingPage_Img8}" ]
    }
  }]]></Component>

  <!-- Reset all participant's votes when landing -->
  <Component type="myCode_reset" customJS="true"><![CDATA[{
      clearAll: true,
      resetVotes: false
  }]]></Component> 


  <Voting autoNext="false">

    <Score type="Vote" result="Score_SIM_MTUs_Offset" response="!{Score_MTUs_Offset}"/> 
    <Score type="Vote" result="Score_SIM_MTUs_Multiplier" response="!{Score_MTUs_Multiplier}"/> 

    <Score type="Vote" result="Score_SIM_Roleplay_Multiplier" response="!{Score_Roleplay_Multiplier}"/> 

    <Score type="Vote" result="Score_SIM_Init_MTUs_1" response="!{KPI_MTUs_1_Init}"/> 
    <Score type="Vote" result="Score_SIM_Init_MTUs_2" response="!{KPI_MTUs_2_Init}"/> 
    <Score type="Vote" result="Score_SIM_Init_MTUs_3" response="!{KPI_MTUs_3_Init}"/> 
    <Score type="Vote" result="Score_SIM_Init_MTUs_4" response="!{KPI_MTUs_4_Init}"/> 
    <Score type="Vote" result="Score_SIM_Init_MTUs_5" response="!{KPI_MTUs_5_Init}"/> 
    <Score type="Vote" result="Score_SIM_Init_MTUs_6" response="!{KPI_MTUs_6_Init}"/> 
    <Score type="Vote" result="Score_SIM_Init_MTUs_7" response="!{KPI_MTUs_7_Init}"/> 
    <Score type="Vote" result="Score_SIM_Init_MTUs_8" response="!{KPI_MTUs_8_Init}"/> 

    <Score type="Vote" result="Score_SIM_Init_KPI1" response="!{KPI_Metric1_Init}"/> 
    <Score type="Vote" result="Score_SIM_Init_KPI2" response="!{KPI_Metric2_Init}"/> 
    <Score type="Vote" result="Score_SIM_Init_KPI3" response="!{KPI_Metric3_Init}"/> 
    <Score type="Vote" result="Score_SIM_Init_KPI4" response="!{KPI_Metric4_Init}"/> 

    <Score type="TimeStamp" result="Q_LOGIN_DATE_R4" keepFirst="true"/> 
    
  </Voting>


</Action>