@charset "UTF-8";

@import "../materialize-src/sass/components/color-variables";
@import "mixins";

.wizlet.wizletHCPieChartModel {


    .card {
        .card-content {
            //background-color: rgba(color("client-colors", "white"), 0.8);
            
            padding: 6px 12px;
            .row {
                margin: 0;
            }
        }
    }

    .highcharts-container {
        &>svg {
            width: 100% !important;
        }

        .highcharts-background {
            fill: transparent;
        }

        .highcharts-data-label {
            
            @media #{$large-and-up} {  font-size: 120%;  }
            
            span {
                color: color("client-colors", "font2");
            }
            tspan {
                fill: color("client-colors", "font2");
            }

        }

        .highcharts-data-labels .highcharts-label span {
            text-align: center;
            top: -2px !important;

            img {
                display: block;
                margin-bottom: -8px;
                padding: 5px;
            }
        }

        @for $i from 1 through 12 {
            .highcharts-color-#{$i - 1} {
                fill: color("client-colors", "chart#{$i}");
                //stroke: color("client-colors", "chart#{$i}");
                &.highcharts-legend-item > .highcharts-point {
                    stroke: color("client-colors", "chart#{$i}");
                }
            }
        } 

        
        .highcharts-data-label-connector {
            fill: none;
        }
        

        .highcharts-tooltip {
            table {
                tr, td {
                    padding: 0;
                }
            }
        }
        
    }

    .bach-content-pieChart--container {
        
        &.labels {
            &.medium {
                .highcharts-data-label {                
                    @media #{$medium-and-up} {  font-size: 125%;  }
                }
            }
            &.small {
                .highcharts-data-label {                
                    @media #{$medium-and-up} {  font-size: 100%;  }
                }
            }
        }

        &.color {
            @for $i from 1 through 4 {
                &.categoria#{$i} {
                    .highcharts-color-0, 
                    .highcharts-color-0 .highcharts-point {
                        fill: color("client-colors", "categoria#{$i}");
                        stroke: color("client-colors", "categoria#{$i}");
                    }                
                }
            }  
        }
        
        &.color {
            &.utilizacion {
                .highcharts-color-5, 
                .highcharts-color-5 .highcharts-point {
                    fill: color("client-colors", "white");
                    stroke: color("client-colors", "white");
                }         
                .highcharts-color-5.highcharts-legend-item .highcharts-point {
                    stroke: color("client-colors", "dark-grey");
                }                       
            }  
        }

        &.withIcons {            
            .highcharts-data-labels {
                margin-top: -1rem;
            }
        }

        &.noclickablelegend {
            .highcharts-legend {
                pointer-events: none;
            }
        }
    }

}