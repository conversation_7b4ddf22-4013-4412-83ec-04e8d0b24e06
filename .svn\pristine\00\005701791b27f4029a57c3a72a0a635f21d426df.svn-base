<?xml version="1.0" encoding="utf-8" ?>
<Action>

  <Aggregator doneText="">

    <ParticipantFilter>
      <Question>Filter_Agg</Question>
    </ParticipantFilter>


    <!-- ****************************************************** -->
    <!-- *********************** VOTES  *********************** -->
    <!-- ****************************************************** -->

    <!-- KPI1 -->
    <Score result="Score_SIM_R1_Scenario1_KPI1" type="Choice"> 
      <Question name="Q_SIM_R1_Scenario1">
          <Choice value="1" Response="!{SIM_R1_Scenario1_Opt1_KPI1}"></Choice>
          <Choice value="2" Response="!{SIM_R1_Scenario1_Opt2_KPI1}"></Choice>
          <Choice value="3" Response="!{SIM_R1_Scenario1_Opt3_KPI1}"></Choice>
          <Choice value="4" Response="!{SIM_R1_Scenario1_Opt4_KPI1}"></Choice>
      </Question>
    </Score> 
    
    <!-- KPI2 -->
    <Score result="Score_SIM_R1_Scenario1_KPI2" type="Choice"> 
      <Question name="Q_SIM_R1_Scenario1">
          <Choice value="1" Response="!{SIM_R1_Scenario1_Opt1_KPI2}"></Choice>
          <Choice value="2" Response="!{SIM_R1_Scenario1_Opt2_KPI2}"></Choice>
          <Choice value="3" Response="!{SIM_R1_Scenario1_Opt3_KPI2}"></Choice>
          <Choice value="4" Response="!{SIM_R1_Scenario1_Opt4_KPI2}"></Choice>
      </Question>
    </Score> 

    <!-- KPI3 -->
    <Score result="Score_SIM_R1_Scenario1_KPI3" type="Choice"> 
      <Question name="Q_SIM_R1_Scenario1">
          <Choice value="1" Response="!{SIM_R1_Scenario1_Opt1_KPI3}"></Choice>
          <Choice value="2" Response="!{SIM_R1_Scenario1_Opt2_KPI3}"></Choice>
          <Choice value="3" Response="!{SIM_R1_Scenario1_Opt3_KPI3}"></Choice>
          <Choice value="4" Response="!{SIM_R1_Scenario1_Opt4_KPI3}"></Choice>
      </Question>
    </Score>

    <!-- KPI4 -->
    <Score result="Score_SIM_R1_Scenario1_KPI4" type="Choice"> 
      <Question name="Q_SIM_R1_Scenario1">
          <Choice value="1" Response="!{SIM_R1_Scenario1_Opt1_KPI4}"></Choice>
          <Choice value="2" Response="!{SIM_R1_Scenario1_Opt2_KPI4}"></Choice>
          <Choice value="3" Response="!{SIM_R1_Scenario1_Opt3_KPI4}"></Choice>
          <Choice value="4" Response="!{SIM_R1_Scenario1_Opt4_KPI4}"></Choice>
      </Question>
    </Score>

    <!-- MTUs_1 -->
    <Score result="Score_SIM_R1_Scenario1_MTUs_1" type="Choice"> 
      <Question name="Q_SIM_R1_Scenario1">
          <Choice value="1" Response="!{SIM_R1_Scenario1_Opt1_MTUs_1}"></Choice>
          <Choice value="2" Response="!{SIM_R1_Scenario1_Opt2_MTUs_1}"></Choice>
          <Choice value="3" Response="!{SIM_R1_Scenario1_Opt3_MTUs_1}"></Choice>
          <Choice value="4" Response="!{SIM_R1_Scenario1_Opt4_MTUs_1}"></Choice>
      </Question>
    </Score>

    <!-- MTUs_2 -->
    <Score result="Score_SIM_R1_Scenario1_MTUs_2" type="Choice"> 
      <Question name="Q_SIM_R1_Scenario1">
          <Choice value="1" Response="!{SIM_R1_Scenario1_Opt1_MTUs_2}"></Choice>
          <Choice value="2" Response="!{SIM_R1_Scenario1_Opt2_MTUs_2}"></Choice>
          <Choice value="3" Response="!{SIM_R1_Scenario1_Opt3_MTUs_2}"></Choice>
          <Choice value="4" Response="!{SIM_R1_Scenario1_Opt4_MTUs_2}"></Choice>
      </Question>
    </Score>

    <!-- MTUs_3 -->
    <Score result="Score_SIM_R1_Scenario1_MTUs_3" type="Choice"> 
      <Question name="Q_SIM_R1_Scenario1">
          <Choice value="1" Response="!{SIM_R1_Scenario1_Opt1_MTUs_3}"></Choice>
          <Choice value="2" Response="!{SIM_R1_Scenario1_Opt2_MTUs_3}"></Choice>
          <Choice value="3" Response="!{SIM_R1_Scenario1_Opt3_MTUs_3}"></Choice>
          <Choice value="4" Response="!{SIM_R1_Scenario1_Opt4_MTUs_3}"></Choice>
      </Question>
    </Score>

    <!-- MTUs_4 -->
    <Score result="Score_SIM_R1_Scenario1_MTUs_4" type="Choice"> 
      <Question name="Q_SIM_R1_Scenario1">
          <Choice value="1" Response="!{SIM_R1_Scenario1_Opt1_MTUs_4}"></Choice>
          <Choice value="2" Response="!{SIM_R1_Scenario1_Opt2_MTUs_4}"></Choice>
          <Choice value="3" Response="!{SIM_R1_Scenario1_Opt3_MTUs_4}"></Choice>
          <Choice value="4" Response="!{SIM_R1_Scenario1_Opt4_MTUs_4}"></Choice>
      </Question>
    </Score>

    <!-- MTUs_5 -->
    <Score result="Score_SIM_R1_Scenario1_MTUs_5" type="Choice"> 
      <Question name="Q_SIM_R1_Scenario1">
          <Choice value="1" Response="!{SIM_R1_Scenario1_Opt1_MTUs_5}"></Choice>
          <Choice value="2" Response="!{SIM_R1_Scenario1_Opt2_MTUs_5}"></Choice>
          <Choice value="3" Response="!{SIM_R1_Scenario1_Opt3_MTUs_5}"></Choice>
          <Choice value="4" Response="!{SIM_R1_Scenario1_Opt4_MTUs_5}"></Choice>
      </Question>
    </Score>

    <!-- MTUs_6 -->
    <Score result="Score_SIM_R1_Scenario1_MTUs_6" type="Choice"> 
      <Question name="Q_SIM_R1_Scenario1">
          <Choice value="1" Response="!{SIM_R1_Scenario1_Opt1_MTUs_6}"></Choice>
          <Choice value="2" Response="!{SIM_R1_Scenario1_Opt2_MTUs_6}"></Choice>
          <Choice value="3" Response="!{SIM_R1_Scenario1_Opt3_MTUs_6}"></Choice>
          <Choice value="4" Response="!{SIM_R1_Scenario1_Opt4_MTUs_6}"></Choice>
      </Question>
    </Score>

    <!-- MTUs_7 -->
    <Score result="Score_SIM_R1_Scenario1_MTUs_7" type="Choice"> 
      <Question name="Q_SIM_R1_Scenario1">
          <Choice value="1" Response="!{SIM_R1_Scenario1_Opt1_MTUs_7}"></Choice>
          <Choice value="2" Response="!{SIM_R1_Scenario1_Opt2_MTUs_7}"></Choice>
          <Choice value="3" Response="!{SIM_R1_Scenario1_Opt3_MTUs_7}"></Choice>
          <Choice value="4" Response="!{SIM_R1_Scenario1_Opt4_MTUs_7}"></Choice>
      </Question>
    </Score>
    
    <!-- MTUs_8 -->
    <Score result="Score_SIM_R1_Scenario1_MTUs_8" type="Choice"> 
      <Question name="Q_SIM_R1_Scenario1">
          <Choice value="1" Response="!{SIM_R1_Scenario1_Opt1_MTUs_8}"></Choice>
          <Choice value="2" Response="!{SIM_R1_Scenario1_Opt2_MTUs_8}"></Choice>
          <Choice value="3" Response="!{SIM_R1_Scenario1_Opt3_MTUs_8}"></Choice>
          <Choice value="4" Response="!{SIM_R1_Scenario1_Opt4_MTUs_8}"></Choice>
      </Question>
    </Score>

    <!-- KPI1 -->
    <Score result="Score_SIM_R2_Scenario1_KPI1" type="Choice"> 
      <Question name="Q_SIM_R2_Scenario1">
          <Choice value="1" Response="!{SIM_R2_Scenario1_Opt1_KPI1}"></Choice>
          <Choice value="2" Response="!{SIM_R2_Scenario1_Opt2_KPI1}"></Choice>
          <Choice value="3" Response="!{SIM_R2_Scenario1_Opt3_KPI1}"></Choice>
          <Choice value="4" Response="!{SIM_R2_Scenario1_Opt4_KPI1}"></Choice>
      </Question>
    </Score> 
    
    <!-- KPI2 -->
    <Score result="Score_SIM_R2_Scenario1_KPI2" type="Choice"> 
      <Question name="Q_SIM_R2_Scenario1">
          <Choice value="1" Response="!{SIM_R2_Scenario1_Opt1_KPI2}"></Choice>
          <Choice value="2" Response="!{SIM_R2_Scenario1_Opt2_KPI2}"></Choice>
          <Choice value="3" Response="!{SIM_R2_Scenario1_Opt3_KPI2}"></Choice>
          <Choice value="4" Response="!{SIM_R2_Scenario1_Opt4_KPI2}"></Choice>
      </Question>
    </Score> 

    <!-- KPI3 -->
    <Score result="Score_SIM_R2_Scenario1_KPI3" type="Choice"> 
      <Question name="Q_SIM_R2_Scenario1">
          <Choice value="1" Response="!{SIM_R2_Scenario1_Opt1_KPI3}"></Choice>
          <Choice value="2" Response="!{SIM_R2_Scenario1_Opt2_KPI3}"></Choice>
          <Choice value="3" Response="!{SIM_R2_Scenario1_Opt3_KPI3}"></Choice>
          <Choice value="4" Response="!{SIM_R2_Scenario1_Opt4_KPI3}"></Choice>
      </Question>
    </Score>

    <!-- KPI4 -->
    <Score result="Score_SIM_R2_Scenario1_KPI4" type="Choice"> 
      <Question name="Q_SIM_R2_Scenario1">
          <Choice value="1" Response="!{SIM_R2_Scenario1_Opt1_KPI4}"></Choice>
          <Choice value="2" Response="!{SIM_R2_Scenario1_Opt2_KPI4}"></Choice>
          <Choice value="3" Response="!{SIM_R2_Scenario1_Opt3_KPI4}"></Choice>
          <Choice value="4" Response="!{SIM_R2_Scenario1_Opt4_KPI4}"></Choice>
      </Question>
    </Score>

    <!-- MTUs_1 -->
    <Score result="Score_SIM_R2_Scenario1_MTUs_1" type="Choice"> 
      <Question name="Q_SIM_R2_Scenario1">
          <Choice value="1" Response="!{SIM_R2_Scenario1_Opt1_MTUs_1}"></Choice>
          <Choice value="2" Response="!{SIM_R2_Scenario1_Opt2_MTUs_1}"></Choice>
          <Choice value="3" Response="!{SIM_R2_Scenario1_Opt3_MTUs_1}"></Choice>
          <Choice value="4" Response="!{SIM_R2_Scenario1_Opt4_MTUs_1}"></Choice>
      </Question>
    </Score>

    <!-- MTUs_2 -->
    <Score result="Score_SIM_R2_Scenario1_MTUs_2" type="Choice"> 
      <Question name="Q_SIM_R2_Scenario1">
          <Choice value="1" Response="!{SIM_R2_Scenario1_Opt1_MTUs_2}"></Choice>
          <Choice value="2" Response="!{SIM_R2_Scenario1_Opt2_MTUs_2}"></Choice>
          <Choice value="3" Response="!{SIM_R2_Scenario1_Opt3_MTUs_2}"></Choice>
          <Choice value="4" Response="!{SIM_R2_Scenario1_Opt4_MTUs_2}"></Choice>
      </Question>
    </Score>

    <!-- MTUs_3 -->
    <Score result="Score_SIM_R2_Scenario1_MTUs_3" type="Choice"> 
      <Question name="Q_SIM_R2_Scenario1">
          <Choice value="1" Response="!{SIM_R2_Scenario1_Opt1_MTUs_3}"></Choice>
          <Choice value="2" Response="!{SIM_R2_Scenario1_Opt2_MTUs_3}"></Choice>
          <Choice value="3" Response="!{SIM_R2_Scenario1_Opt3_MTUs_3}"></Choice>
          <Choice value="4" Response="!{SIM_R2_Scenario1_Opt4_MTUs_3}"></Choice>
      </Question>
    </Score>

    <!-- MTUs_4 -->
    <Score result="Score_SIM_R2_Scenario1_MTUs_4" type="Choice"> 
      <Question name="Q_SIM_R2_Scenario1">
          <Choice value="1" Response="!{SIM_R2_Scenario1_Opt1_MTUs_4}"></Choice>
          <Choice value="2" Response="!{SIM_R2_Scenario1_Opt2_MTUs_4}"></Choice>
          <Choice value="3" Response="!{SIM_R2_Scenario1_Opt3_MTUs_4}"></Choice>
          <Choice value="4" Response="!{SIM_R2_Scenario1_Opt4_MTUs_4}"></Choice>
      </Question>
    </Score>

    <!-- MTUs_5 -->
    <Score result="Score_SIM_R2_Scenario1_MTUs_5" type="Choice"> 
      <Question name="Q_SIM_R2_Scenario1">
          <Choice value="1" Response="!{SIM_R2_Scenario1_Opt1_MTUs_5}"></Choice>
          <Choice value="2" Response="!{SIM_R2_Scenario1_Opt2_MTUs_5}"></Choice>
          <Choice value="3" Response="!{SIM_R2_Scenario1_Opt3_MTUs_5}"></Choice>
          <Choice value="4" Response="!{SIM_R2_Scenario1_Opt4_MTUs_5}"></Choice>
      </Question>
    </Score>

    <!-- MTUs_6 -->
    <Score result="Score_SIM_R2_Scenario1_MTUs_6" type="Choice"> 
      <Question name="Q_SIM_R2_Scenario1">
          <Choice value="1" Response="!{SIM_R2_Scenario1_Opt1_MTUs_6}"></Choice>
          <Choice value="2" Response="!{SIM_R2_Scenario1_Opt2_MTUs_6}"></Choice>
          <Choice value="3" Response="!{SIM_R2_Scenario1_Opt3_MTUs_6}"></Choice>
          <Choice value="4" Response="!{SIM_R2_Scenario1_Opt4_MTUs_6}"></Choice>
      </Question>
    </Score>

    <!-- MTUs_7 -->
    <Score result="Score_SIM_R2_Scenario1_MTUs_7" type="Choice"> 
      <Question name="Q_SIM_R2_Scenario1">
          <Choice value="1" Response="!{SIM_R2_Scenario1_Opt1_MTUs_7}"></Choice>
          <Choice value="2" Response="!{SIM_R2_Scenario1_Opt2_MTUs_7}"></Choice>
          <Choice value="3" Response="!{SIM_R2_Scenario1_Opt3_MTUs_7}"></Choice>
          <Choice value="4" Response="!{SIM_R2_Scenario1_Opt4_MTUs_7}"></Choice>
      </Question>
    </Score>
    
    <!-- MTUs_8 -->
    <Score result="Score_SIM_R2_Scenario1_MTUs_8" type="Choice"> 
      <Question name="Q_SIM_R2_Scenario1">
          <Choice value="1" Response="!{SIM_R2_Scenario1_Opt1_MTUs_8}"></Choice>
          <Choice value="2" Response="!{SIM_R2_Scenario1_Opt2_MTUs_8}"></Choice>
          <Choice value="3" Response="!{SIM_R2_Scenario1_Opt3_MTUs_8}"></Choice>
          <Choice value="4" Response="!{SIM_R2_Scenario1_Opt4_MTUs_8}"></Choice>
      </Question>
    </Score>

    <!-- KPI1 -->
    <Score result="Score_SIM_R3_Scenario1_KPI1" type="Choice"> 
      <Question name="Q_SIM_R3_Scenario1">
          <Choice value="1" Response="!{SIM_R3_Scenario1_Opt1_KPI1}"></Choice>
          <Choice value="2" Response="!{SIM_R3_Scenario1_Opt2_KPI1}"></Choice>
          <Choice value="3" Response="!{SIM_R3_Scenario1_Opt3_KPI1}"></Choice>
          <Choice value="4" Response="!{SIM_R3_Scenario1_Opt4_KPI1}"></Choice>
      </Question>
    </Score> 
    
    <!-- KPI2 -->
    <Score result="Score_SIM_R3_Scenario1_KPI2" type="Choice"> 
      <Question name="Q_SIM_R3_Scenario1">
          <Choice value="1" Response="!{SIM_R3_Scenario1_Opt1_KPI2}"></Choice>
          <Choice value="2" Response="!{SIM_R3_Scenario1_Opt2_KPI2}"></Choice>
          <Choice value="3" Response="!{SIM_R3_Scenario1_Opt3_KPI2}"></Choice>
          <Choice value="4" Response="!{SIM_R3_Scenario1_Opt4_KPI2}"></Choice>
      </Question>
    </Score> 

    <!-- KPI3 -->
    <Score result="Score_SIM_R3_Scenario1_KPI3" type="Choice"> 
      <Question name="Q_SIM_R3_Scenario1">
          <Choice value="1" Response="!{SIM_R3_Scenario1_Opt1_KPI3}"></Choice>
          <Choice value="2" Response="!{SIM_R3_Scenario1_Opt2_KPI3}"></Choice>
          <Choice value="3" Response="!{SIM_R3_Scenario1_Opt3_KPI3}"></Choice>
          <Choice value="4" Response="!{SIM_R3_Scenario1_Opt4_KPI3}"></Choice>
      </Question>
    </Score>

    <!-- KPI4 -->
    <Score result="Score_SIM_R3_Scenario1_KPI4" type="Choice"> 
      <Question name="Q_SIM_R3_Scenario1">
          <Choice value="1" Response="!{SIM_R3_Scenario1_Opt1_KPI4}"></Choice>
          <Choice value="2" Response="!{SIM_R3_Scenario1_Opt2_KPI4}"></Choice>
          <Choice value="3" Response="!{SIM_R3_Scenario1_Opt3_KPI4}"></Choice>
          <Choice value="4" Response="!{SIM_R3_Scenario1_Opt4_KPI4}"></Choice>
      </Question>
    </Score>

    <!-- MTUs_1 -->
    <Score result="Score_SIM_R3_Scenario1_MTUs_1" type="Choice"> 
      <Question name="Q_SIM_R3_Scenario1">
          <Choice value="1" Response="!{SIM_R3_Scenario1_Opt1_MTUs_1}"></Choice>
          <Choice value="2" Response="!{SIM_R3_Scenario1_Opt2_MTUs_1}"></Choice>
          <Choice value="3" Response="!{SIM_R3_Scenario1_Opt3_MTUs_1}"></Choice>
          <Choice value="4" Response="!{SIM_R3_Scenario1_Opt4_MTUs_1}"></Choice>
      </Question>
    </Score>

    <!-- MTUs_2 -->
    <Score result="Score_SIM_R3_Scenario1_MTUs_2" type="Choice"> 
      <Question name="Q_SIM_R3_Scenario1">
          <Choice value="1" Response="!{SIM_R3_Scenario1_Opt1_MTUs_2}"></Choice>
          <Choice value="2" Response="!{SIM_R3_Scenario1_Opt2_MTUs_2}"></Choice>
          <Choice value="3" Response="!{SIM_R3_Scenario1_Opt3_MTUs_2}"></Choice>
          <Choice value="4" Response="!{SIM_R3_Scenario1_Opt4_MTUs_2}"></Choice>
      </Question>
    </Score>

    <!-- MTUs_3 -->
    <Score result="Score_SIM_R3_Scenario1_MTUs_3" type="Choice"> 
      <Question name="Q_SIM_R3_Scenario1">
          <Choice value="1" Response="!{SIM_R3_Scenario1_Opt1_MTUs_3}"></Choice>
          <Choice value="2" Response="!{SIM_R3_Scenario1_Opt2_MTUs_3}"></Choice>
          <Choice value="3" Response="!{SIM_R3_Scenario1_Opt3_MTUs_3}"></Choice>
          <Choice value="4" Response="!{SIM_R3_Scenario1_Opt4_MTUs_3}"></Choice>
      </Question>
    </Score>

    <!-- MTUs_4 -->
    <Score result="Score_SIM_R3_Scenario1_MTUs_4" type="Choice"> 
      <Question name="Q_SIM_R3_Scenario1">
          <Choice value="1" Response="!{SIM_R3_Scenario1_Opt1_MTUs_4}"></Choice>
          <Choice value="2" Response="!{SIM_R3_Scenario1_Opt2_MTUs_4}"></Choice>
          <Choice value="3" Response="!{SIM_R3_Scenario1_Opt3_MTUs_4}"></Choice>
          <Choice value="4" Response="!{SIM_R3_Scenario1_Opt4_MTUs_4}"></Choice>
      </Question>
    </Score>

    <!-- MTUs_5 -->
    <Score result="Score_SIM_R3_Scenario1_MTUs_5" type="Choice"> 
      <Question name="Q_SIM_R3_Scenario1">
          <Choice value="1" Response="!{SIM_R3_Scenario1_Opt1_MTUs_5}"></Choice>
          <Choice value="2" Response="!{SIM_R3_Scenario1_Opt2_MTUs_5}"></Choice>
          <Choice value="3" Response="!{SIM_R3_Scenario1_Opt3_MTUs_5}"></Choice>
          <Choice value="4" Response="!{SIM_R3_Scenario1_Opt4_MTUs_5}"></Choice>
      </Question>
    </Score>

    <!-- MTUs_6 -->
    <Score result="Score_SIM_R3_Scenario1_MTUs_6" type="Choice"> 
      <Question name="Q_SIM_R3_Scenario1">
          <Choice value="1" Response="!{SIM_R3_Scenario1_Opt1_MTUs_6}"></Choice>
          <Choice value="2" Response="!{SIM_R3_Scenario1_Opt2_MTUs_6}"></Choice>
          <Choice value="3" Response="!{SIM_R3_Scenario1_Opt3_MTUs_6}"></Choice>
          <Choice value="4" Response="!{SIM_R3_Scenario1_Opt4_MTUs_6}"></Choice>
      </Question>
    </Score>

    <!-- MTUs_7 -->
    <Score result="Score_SIM_R3_Scenario1_MTUs_7" type="Choice"> 
      <Question name="Q_SIM_R3_Scenario1">
          <Choice value="1" Response="!{SIM_R3_Scenario1_Opt1_MTUs_7}"></Choice>
          <Choice value="2" Response="!{SIM_R3_Scenario1_Opt2_MTUs_7}"></Choice>
          <Choice value="3" Response="!{SIM_R3_Scenario1_Opt3_MTUs_7}"></Choice>
          <Choice value="4" Response="!{SIM_R3_Scenario1_Opt4_MTUs_7}"></Choice>
      </Question>
    </Score>
    
    <!-- MTUs_8 -->
    <Score result="Score_SIM_R3_Scenario1_MTUs_8" type="Choice"> 
      <Question name="Q_SIM_R3_Scenario1">
          <Choice value="1" Response="!{SIM_R3_Scenario1_Opt1_MTUs_8}"></Choice>
          <Choice value="2" Response="!{SIM_R3_Scenario1_Opt2_MTUs_8}"></Choice>
          <Choice value="3" Response="!{SIM_R3_Scenario1_Opt3_MTUs_8}"></Choice>
          <Choice value="4" Response="!{SIM_R3_Scenario1_Opt4_MTUs_8}"></Choice>
      </Question>
    </Score>

    <!-- KPI1 -->
    <Score result="Score_SIM_R4_Scenario1_KPI1" type="Choice"> 
      <Question name="Q_SIM_R4_Scenario1">
          <Choice value="1" Response="!{SIM_R4_Scenario1_Opt1_KPI1}"></Choice>
          <Choice value="2" Response="!{SIM_R4_Scenario1_Opt2_KPI1}"></Choice>
          <Choice value="3" Response="!{SIM_R4_Scenario1_Opt3_KPI1}"></Choice>
          <Choice value="4" Response="!{SIM_R4_Scenario1_Opt4_KPI1}"></Choice>
      </Question>
    </Score> 
    
    <!-- KPI2 -->
    <Score result="Score_SIM_R4_Scenario1_KPI2" type="Choice"> 
      <Question name="Q_SIM_R4_Scenario1">
          <Choice value="1" Response="!{SIM_R4_Scenario1_Opt1_KPI2}"></Choice>
          <Choice value="2" Response="!{SIM_R4_Scenario1_Opt2_KPI2}"></Choice>
          <Choice value="3" Response="!{SIM_R4_Scenario1_Opt3_KPI2}"></Choice>
          <Choice value="4" Response="!{SIM_R4_Scenario1_Opt4_KPI2}"></Choice>
      </Question>
    </Score> 

    <!-- KPI3 -->
    <Score result="Score_SIM_R4_Scenario1_KPI3" type="Choice"> 
      <Question name="Q_SIM_R4_Scenario1">
          <Choice value="1" Response="!{SIM_R4_Scenario1_Opt1_KPI3}"></Choice>
          <Choice value="2" Response="!{SIM_R4_Scenario1_Opt2_KPI3}"></Choice>
          <Choice value="3" Response="!{SIM_R4_Scenario1_Opt3_KPI3}"></Choice>
          <Choice value="4" Response="!{SIM_R4_Scenario1_Opt4_KPI3}"></Choice>
      </Question>
    </Score>

    <!-- KPI4 -->
    <Score result="Score_SIM_R4_Scenario1_KPI4" type="Choice"> 
      <Question name="Q_SIM_R4_Scenario1">
          <Choice value="1" Response="!{SIM_R4_Scenario1_Opt1_KPI4}"></Choice>
          <Choice value="2" Response="!{SIM_R4_Scenario1_Opt2_KPI4}"></Choice>
          <Choice value="3" Response="!{SIM_R4_Scenario1_Opt3_KPI4}"></Choice>
          <Choice value="4" Response="!{SIM_R4_Scenario1_Opt4_KPI4}"></Choice>
      </Question>
    </Score>

    <!-- MTUs_1 -->
    <Score result="Score_SIM_R4_Scenario1_MTUs_1" type="Choice"> 
      <Question name="Q_SIM_R4_Scenario1">
          <Choice value="1" Response="!{SIM_R4_Scenario1_Opt1_MTUs_1}"></Choice>
          <Choice value="2" Response="!{SIM_R4_Scenario1_Opt2_MTUs_1}"></Choice>
          <Choice value="3" Response="!{SIM_R4_Scenario1_Opt3_MTUs_1}"></Choice>
          <Choice value="4" Response="!{SIM_R4_Scenario1_Opt4_MTUs_1}"></Choice>
      </Question>
    </Score>

    <!-- MTUs_2 -->
    <Score result="Score_SIM_R4_Scenario1_MTUs_2" type="Choice"> 
      <Question name="Q_SIM_R4_Scenario1">
          <Choice value="1" Response="!{SIM_R4_Scenario1_Opt1_MTUs_2}"></Choice>
          <Choice value="2" Response="!{SIM_R4_Scenario1_Opt2_MTUs_2}"></Choice>
          <Choice value="3" Response="!{SIM_R4_Scenario1_Opt3_MTUs_2}"></Choice>
          <Choice value="4" Response="!{SIM_R4_Scenario1_Opt4_MTUs_2}"></Choice>
      </Question>
    </Score>

    <!-- MTUs_3 -->
    <Score result="Score_SIM_R4_Scenario1_MTUs_3" type="Choice"> 
      <Question name="Q_SIM_R4_Scenario1">
          <Choice value="1" Response="!{SIM_R4_Scenario1_Opt1_MTUs_3}"></Choice>
          <Choice value="2" Response="!{SIM_R4_Scenario1_Opt2_MTUs_3}"></Choice>
          <Choice value="3" Response="!{SIM_R4_Scenario1_Opt3_MTUs_3}"></Choice>
          <Choice value="4" Response="!{SIM_R4_Scenario1_Opt4_MTUs_3}"></Choice>
      </Question>
    </Score>

    <!-- MTUs_4 -->
    <Score result="Score_SIM_R4_Scenario1_MTUs_4" type="Choice"> 
      <Question name="Q_SIM_R4_Scenario1">
          <Choice value="1" Response="!{SIM_R4_Scenario1_Opt1_MTUs_4}"></Choice>
          <Choice value="2" Response="!{SIM_R4_Scenario1_Opt2_MTUs_4}"></Choice>
          <Choice value="3" Response="!{SIM_R4_Scenario1_Opt3_MTUs_4}"></Choice>
          <Choice value="4" Response="!{SIM_R4_Scenario1_Opt4_MTUs_4}"></Choice>
      </Question>
    </Score>

    <!-- MTUs_5 -->
    <Score result="Score_SIM_R4_Scenario1_MTUs_5" type="Choice"> 
      <Question name="Q_SIM_R4_Scenario1">
          <Choice value="1" Response="!{SIM_R4_Scenario1_Opt1_MTUs_5}"></Choice>
          <Choice value="2" Response="!{SIM_R4_Scenario1_Opt2_MTUs_5}"></Choice>
          <Choice value="3" Response="!{SIM_R4_Scenario1_Opt3_MTUs_5}"></Choice>
          <Choice value="4" Response="!{SIM_R4_Scenario1_Opt4_MTUs_5}"></Choice>
      </Question>
    </Score>

    <!-- MTUs_6 -->
    <Score result="Score_SIM_R4_Scenario1_MTUs_6" type="Choice"> 
      <Question name="Q_SIM_R4_Scenario1">
          <Choice value="1" Response="!{SIM_R4_Scenario1_Opt1_MTUs_6}"></Choice>
          <Choice value="2" Response="!{SIM_R4_Scenario1_Opt2_MTUs_6}"></Choice>
          <Choice value="3" Response="!{SIM_R4_Scenario1_Opt3_MTUs_6}"></Choice>
          <Choice value="4" Response="!{SIM_R4_Scenario1_Opt4_MTUs_6}"></Choice>
      </Question>
    </Score>

    <!-- MTUs_7 -->
    <Score result="Score_SIM_R4_Scenario1_MTUs_7" type="Choice"> 
      <Question name="Q_SIM_R4_Scenario1">
          <Choice value="1" Response="!{SIM_R4_Scenario1_Opt1_MTUs_7}"></Choice>
          <Choice value="2" Response="!{SIM_R4_Scenario1_Opt2_MTUs_7}"></Choice>
          <Choice value="3" Response="!{SIM_R4_Scenario1_Opt3_MTUs_7}"></Choice>
          <Choice value="4" Response="!{SIM_R4_Scenario1_Opt4_MTUs_7}"></Choice>
      </Question>
    </Score>
    
    <!-- MTUs_8 -->
    <Score result="Score_SIM_R4_Scenario1_MTUs_8" type="Choice"> 
      <Question name="Q_SIM_R4_Scenario1">
          <Choice value="1" Response="!{SIM_R4_Scenario1_Opt1_MTUs_8}"></Choice>
          <Choice value="2" Response="!{SIM_R4_Scenario1_Opt2_MTUs_8}"></Choice>
          <Choice value="3" Response="!{SIM_R4_Scenario1_Opt3_MTUs_8}"></Choice>
          <Choice value="4" Response="!{SIM_R4_Scenario1_Opt4_MTUs_8}"></Choice>
      </Question>
    </Score>


    <!-- ******************************************************* -->
    <!-- *********************** TOTALS  *********************** -->
    <!-- ******************************************************* -->

    <!-- MTUs R1, each negative MTU result will impact KPI1 by -0.01% -->

    <Total result="Score_SIM_Total_R1_MTUs_1" method="sum">
      <Question validate="false">Score_SIM_Init_MTUs_1</Question>
      <Question validate="false">Score_SIM_R1_Scenario1_MTUs_1</Question>
    </Total>
    <Score type="Range" result="Score_SIM_Total_R1_MTUs_1_Rng">
      <Question>Score_SIM_Total_R1_MTUs_1</Question>
      <Boundary>0</Boundary>
    </Score>
    <Total result="Score_SIM_Total_R1_MTUs_2" method="sum">
      <Question validate="false">Score_SIM_Init_MTUs_2</Question>
      <Question validate="false">Score_SIM_R1_Scenario1_MTUs_2</Question>
    </Total>
    <Score type="Range" result="Score_SIM_Total_R1_MTUs_2_Rng">
      <Question>Score_SIM_Total_R1_MTUs_2</Question>
      <Boundary>0</Boundary>
    </Score>
    <Total result="Score_SIM_Total_R1_MTUs_3" method="sum">
      <Question validate="false">Score_SIM_Init_MTUs_3</Question>
      <Question validate="false">Score_SIM_R1_Scenario1_MTUs_3</Question>
    </Total>
    <Score type="Range" result="Score_SIM_Total_R1_MTUs_3_Rng">
      <Question>Score_SIM_Total_R1_MTUs_3</Question>
      <Boundary>0</Boundary>
    </Score>
    <Total result="Score_SIM_Total_R1_MTUs_4" method="sum">
      <Question validate="false">Score_SIM_Init_MTUs_4</Question>
      <Question validate="false">Score_SIM_R1_Scenario1_MTUs_4</Question>
    </Total>
    <Score type="Range" result="Score_SIM_Total_R1_MTUs_4_Rng">
      <Question>Score_SIM_Total_R1_MTUs_4</Question>
      <Boundary>0</Boundary>
    </Score>
    <Total result="Score_SIM_Total_R1_MTUs_5" method="sum">
      <Question validate="false">Score_SIM_Init_MTUs_5</Question>
      <Question validate="false">Score_SIM_R1_Scenario1_MTUs_5</Question>
    </Total>
    <Score type="Range" result="Score_SIM_Total_R1_MTUs_5_Rng">
      <Question>Score_SIM_Total_R1_MTUs_5</Question>
      <Boundary>0</Boundary>
    </Score>
    <Total result="Score_SIM_Total_R1_MTUs_6" method="sum">
      <Question validate="false">Score_SIM_Init_MTUs_6</Question>
      <Question validate="false">Score_SIM_R1_Scenario1_MTUs_6</Question>
    </Total>
    <Score type="Range" result="Score_SIM_Total_R1_MTUs_6_Rng">
      <Question>Score_SIM_Total_R1_MTUs_6</Question>
      <Boundary>0</Boundary>
    </Score>
    <Total result="Score_SIM_Total_R1_MTUs_7" method="sum">
      <Question validate="false">Score_SIM_Init_MTUs_7</Question>
      <Question validate="false">Score_SIM_R1_Scenario1_MTUs_7</Question>
    </Total>
    <Score type="Range" result="Score_SIM_Total_R1_MTUs_7_Rng">
      <Question>Score_SIM_Total_R1_MTUs_7</Question>
      <Boundary>0</Boundary>
    </Score>
    <Total result="Score_SIM_Total_R1_MTUs_8" method="sum">
      <Question validate="false">Score_SIM_Init_MTUs_8</Question>
      <Question validate="false">Score_SIM_R1_Scenario1_MTUs_8</Question>
    </Total>
    <Score type="Range" result="Score_SIM_Total_R1_MTUs_8_Rng">
      <Question>Score_SIM_Total_R1_MTUs_8</Question>
      <Boundary>0</Boundary>
    </Score>
    <Total result="Score_SIM_Total_R1_KPI1_MTUs_Impact_Pre" method="sum">
      <Question validate="false">Score_SIM_MTUs_Offset</Question>
      <Question validate="false">Score_SIM_Total_R1_MTUs_1_Rng</Question>
      <Question validate="false">Score_SIM_Total_R1_MTUs_2_Rng</Question>
      <Question validate="false">Score_SIM_Total_R1_MTUs_3_Rng</Question>
      <Question validate="false">Score_SIM_Total_R1_MTUs_4_Rng</Question>
      <Question validate="false">Score_SIM_Total_R1_MTUs_5_Rng</Question>
      <Question validate="false">Score_SIM_Total_R1_MTUs_6_Rng</Question>
      <Question validate="false">Score_SIM_Total_R1_MTUs_7_Rng</Question>
      <Question validate="false">Score_SIM_Total_R1_MTUs_8_Rng</Question>
    </Total>
    <Total result="Score_SIM_Total_R1_KPI1_MTUs_Impact" method="multiply">
      <Question validate="false">Score_SIM_MTUs_Multiplier</Question>
      <Question validate="false">Score_SIM_Total_R1_KPI1_MTUs_Impact_Pre</Question>
    </Total>


    <!-- TOTAL R1 Base KPIs -->
    
    <Total result="Score_SIM_Total_R1_KPI1_Base" method="sum">
      <Question validate="false">Score_SIM_Init_KPI1</Question>
      <Question validate="false">Score_SIM_R1_Scenario1_KPI1</Question>
    </Total>
    <Total result="Score_SIM_Total_R1_KPI1_Impacts" method="multiply">
      <Question validate="false">Score_SIM_Total_R1_KPI1_Base</Question>
      <Question validate="false">Score_SIM_Total_R1_KPI1_MTUs_Impact</Question>
    </Total>
    <Total result="Score_SIM_Total_R1_KPI1" method="sum">
      <Question validate="false">Score_SIM_Total_R1_KPI1_Base</Question>
      <Question validate="false">Score_SIM_Total_R1_KPI1_Impacts</Question>
    </Total>
    <Total result="Score_SIM_Total_R1_KPI2" method="sum">
      <Question validate="false">Score_SIM_Init_KPI2</Question>
      <Question validate="false">Score_SIM_R1_Scenario1_KPI2</Question>
    </Total>
    <Total result="Score_SIM_Total_R1_KPI3" method="sum">
      <Question validate="false">Score_SIM_Init_KPI3</Question>
      <Question validate="false">Score_SIM_R1_Scenario1_KPI3</Question>
    </Total>
    <Total result="Score_SIM_Total_R1_KPI4" method="sum">
      <Question validate="false">Score_SIM_Init_KPI4</Question>
      <Question validate="false">Score_SIM_R1_Scenario1_KPI4</Question>
    </Total>

    <!-- Roleplay R1 -->

    <Total result="Score_SIM_R1_Roleplay_Impact" method="multiply">  
      <Question validate="false">Score_SIM_Roleplay_Multiplier</Question>
      <Question validate="false">Score_SIM_R1_Roleplay</Question>
    </Total>

    <!-- Final R1 -->
    
    <Total result="Score_SIM_Total_R1" method="sum">
      <Question validate="false">Score_SIM_Total_R1_KPI1</Question>
      <Question validate="false">Score_SIM_Total_R1_KPI2</Question>
      <Question validate="false">Score_SIM_Total_R1_KPI3</Question>
      <Question validate="false">Score_SIM_Total_R1_KPI4</Question>
      <Question validate="false">Score_SIM_R1_Roleplay_Impact</Question>
    </Total>


    <!-- MTUs R2, each negative MTU result will impact KPI1 by -0.01% -->

    <Total result="Score_SIM_Total_R2_MTUs_1" method="sum">
      <Question validate="false">Score_SIM_Total_R1_MTUs_1</Question>
      <Question validate="false">Score_SIM_R2_Scenario1_MTUs_1</Question>
    </Total>
    <Score type="Range" result="Score_SIM_Total_R2_MTUs_1_Rng">
      <Question>Score_SIM_Total_R2_MTUs_1</Question>
      <Boundary>0</Boundary>
    </Score>
    <Total result="Score_SIM_Total_R2_MTUs_2" method="sum">
      <Question validate="false">Score_SIM_Total_R1_MTUs_2</Question>
      <Question validate="false">Score_SIM_R2_Scenario1_MTUs_2</Question>
    </Total>
    <Score type="Range" result="Score_SIM_Total_R2_MTUs_2_Rng">
      <Question>Score_SIM_Total_R2_MTUs_2</Question>
      <Boundary>0</Boundary>
    </Score>
    <Total result="Score_SIM_Total_R2_MTUs_3" method="sum">
      <Question validate="false">Score_SIM_Total_R1_MTUs_3</Question>
      <Question validate="false">Score_SIM_R2_Scenario1_MTUs_3</Question>
    </Total>
    <Score type="Range" result="Score_SIM_Total_R2_MTUs_3_Rng">
      <Question>Score_SIM_Total_R2_MTUs_3</Question>
      <Boundary>0</Boundary>
    </Score>
    <Total result="Score_SIM_Total_R2_MTUs_4" method="sum">
      <Question validate="false">Score_SIM_Total_R1_MTUs_4</Question>
      <Question validate="false">Score_SIM_R2_Scenario1_MTUs_4</Question>
    </Total>
    <Score type="Range" result="Score_SIM_Total_R2_MTUs_4_Rng">
      <Question>Score_SIM_Total_R2_MTUs_4</Question>
      <Boundary>0</Boundary>
    </Score>
    <Total result="Score_SIM_Total_R2_MTUs_5" method="sum">
      <Question validate="false">Score_SIM_Total_R1_MTUs_5</Question>
      <Question validate="false">Score_SIM_R2_Scenario1_MTUs_5</Question>
    </Total>
    <Score type="Range" result="Score_SIM_Total_R2_MTUs_5_Rng">
      <Question>Score_SIM_Total_R2_MTUs_5</Question>
      <Boundary>0</Boundary>
    </Score>
    <Total result="Score_SIM_Total_R2_MTUs_6" method="sum">
      <Question validate="false">Score_SIM_Total_R1_MTUs_6</Question>
      <Question validate="false">Score_SIM_R2_Scenario1_MTUs_6</Question>
    </Total>
    <Score type="Range" result="Score_SIM_Total_R2_MTUs_6_Rng">
      <Question>Score_SIM_Total_R2_MTUs_6</Question>
      <Boundary>0</Boundary>
    </Score>
    <Total result="Score_SIM_Total_R2_MTUs_7" method="sum">
      <Question validate="false">Score_SIM_Total_R1_MTUs_7</Question>
      <Question validate="false">Score_SIM_R2_Scenario1_MTUs_7</Question>
    </Total>
    <Score type="Range" result="Score_SIM_Total_R2_MTUs_7_Rng">
      <Question>Score_SIM_Total_R2_MTUs_7</Question>
      <Boundary>0</Boundary>
    </Score>
    <Total result="Score_SIM_Total_R2_MTUs_8" method="sum">
      <Question validate="false">Score_SIM_Total_R1_MTUs_8</Question>
      <Question validate="false">Score_SIM_R2_Scenario1_MTUs_8</Question>
    </Total>
    <Score type="Range" result="Score_SIM_Total_R2_MTUs_8_Rng">
      <Question>Score_SIM_Total_R2_MTUs_8</Question>
      <Boundary>0</Boundary>
    </Score>
    <Total result="Score_SIM_Total_R2_KPI1_MTUs_Impact_Pre" method="sum">
      <Question validate="false">Score_SIM_MTUs_Offset</Question>
      <Question validate="false">Score_SIM_Total_R2_MTUs_1_Rng</Question>
      <Question validate="false">Score_SIM_Total_R2_MTUs_2_Rng</Question>
      <Question validate="false">Score_SIM_Total_R2_MTUs_3_Rng</Question>
      <Question validate="false">Score_SIM_Total_R2_MTUs_4_Rng</Question>
      <Question validate="false">Score_SIM_Total_R2_MTUs_5_Rng</Question>
      <Question validate="false">Score_SIM_Total_R2_MTUs_6_Rng</Question>
      <Question validate="false">Score_SIM_Total_R2_MTUs_7_Rng</Question>
      <Question validate="false">Score_SIM_Total_R2_MTUs_8_Rng</Question>
    </Total>
    <Total result="Score_SIM_Total_R2_KPI1_MTUs_Impact" method="multiply">
      <Question validate="false">Score_SIM_MTUs_Multiplier</Question>
      <Question validate="false">Score_SIM_Total_R2_KPI1_MTUs_Impact_Pre</Question>
    </Total>


    <!-- TOTAL R2 Base KPIs -->
    
    <Total result="Score_SIM_Total_R2_KPI1_Base" method="sum">
      <Question validate="false">Score_SIM_Total_R1_KPI1</Question>
      <Question validate="false">Score_SIM_R2_Scenario1_KPI1</Question>
    </Total>
    <Total result="Score_SIM_Total_R2_KPI1_Impacts" method="multiply">
      <Question validate="false">Score_SIM_Total_R2_KPI1_Base</Question>
      <Question validate="false">Score_SIM_Total_R2_KPI1_MTUs_Impact</Question>
    </Total>
    <Total result="Score_SIM_Total_R2_KPI1" method="sum">
      <Question validate="false">Score_SIM_Total_R2_KPI1_Base</Question>
      <Question validate="false">Score_SIM_Total_R2_KPI1_Impacts</Question>
    </Total>
    <Total result="Score_SIM_Total_R2_KPI2" method="sum">
      <Question validate="false">Score_SIM_Total_R1_KPI2</Question>
      <Question validate="false">Score_SIM_R2_Scenario1_KPI2</Question>
    </Total>
    <Total result="Score_SIM_Total_R2_KPI3" method="sum">
      <Question validate="false">Score_SIM_Total_R1_KPI3</Question>
      <Question validate="false">Score_SIM_R2_Scenario1_KPI3</Question>
    </Total>
    <Total result="Score_SIM_Total_R2_KPI4" method="sum">
      <Question validate="false">Score_SIM_Total_R1_KPI4</Question>
      <Question validate="false">Score_SIM_R2_Scenario1_KPI4</Question>
    </Total>


    <!-- Roleplay R2 -->

    <Total result="Score_SIM_R2_Roleplay_Impact" method="multiply">  
      <Question validate="false">Score_SIM_Roleplay_Multiplier</Question>
      <Question validate="false">Score_SIM_R2_Roleplay</Question>
    </Total>


    <!-- Final R2 -->
    
    <Total result="Score_SIM_Total_R2" method="sum">
      <Question validate="false">Score_SIM_R1_Roleplay_Impact</Question>
      <Question validate="false">Score_SIM_Total_R2_KPI1</Question>
      <Question validate="false">Score_SIM_Total_R2_KPI2</Question>
      <Question validate="false">Score_SIM_Total_R2_KPI3</Question>
      <Question validate="false">Score_SIM_Total_R2_KPI4</Question>
      <Question validate="false">Score_SIM_R2_Roleplay_Impact</Question>
    </Total>

    <!-- MTUs R3, each negative MTU result will impact KPI1 by -0.01% -->

    <Total result="Score_SIM_Total_R3_MTUs_1" method="sum">
      <Question validate="false">Score_SIM_Total_R2_MTUs_1</Question>
      <Question validate="false">Score_SIM_R3_Scenario1_MTUs_1</Question>
    </Total>
    <Score type="Range" result="Score_SIM_Total_R3_MTUs_1_Rng">
      <Question>Score_SIM_Total_R3_MTUs_1</Question>
      <Boundary>0</Boundary>
    </Score>
    <Total result="Score_SIM_Total_R3_MTUs_2" method="sum">
      <Question validate="false">Score_SIM_Total_R2_MTUs_2</Question>
      <Question validate="false">Score_SIM_R3_Scenario1_MTUs_2</Question>
    </Total>
    <Score type="Range" result="Score_SIM_Total_R3_MTUs_2_Rng">
      <Question>Score_SIM_Total_R3_MTUs_2</Question>
      <Boundary>0</Boundary>
    </Score>
    <Total result="Score_SIM_Total_R3_MTUs_3" method="sum">
      <Question validate="false">Score_SIM_Total_R2_MTUs_3</Question>
      <Question validate="false">Score_SIM_R3_Scenario1_MTUs_3</Question>
    </Total>
    <Score type="Range" result="Score_SIM_Total_R3_MTUs_3_Rng">
      <Question>Score_SIM_Total_R3_MTUs_3</Question>
      <Boundary>0</Boundary>
    </Score>
    <Total result="Score_SIM_Total_R3_MTUs_4" method="sum">
      <Question validate="false">Score_SIM_Total_R2_MTUs_4</Question>
      <Question validate="false">Score_SIM_R3_Scenario1_MTUs_4</Question>
    </Total>
    <Score type="Range" result="Score_SIM_Total_R3_MTUs_4_Rng">
      <Question>Score_SIM_Total_R3_MTUs_4</Question>
      <Boundary>0</Boundary>
    </Score>
    <Total result="Score_SIM_Total_R3_MTUs_5" method="sum">
      <Question validate="false">Score_SIM_Total_R2_MTUs_5</Question>
      <Question validate="false">Score_SIM_R3_Scenario1_MTUs_5</Question>
    </Total>
    <Score type="Range" result="Score_SIM_Total_R3_MTUs_5_Rng">
      <Question>Score_SIM_Total_R3_MTUs_5</Question>
      <Boundary>0</Boundary>
    </Score>
    <Total result="Score_SIM_Total_R3_MTUs_6" method="sum">
      <Question validate="false">Score_SIM_Total_R2_MTUs_6</Question>
      <Question validate="false">Score_SIM_R3_Scenario1_MTUs_6</Question>
    </Total>
    <Score type="Range" result="Score_SIM_Total_R3_MTUs_6_Rng">
      <Question>Score_SIM_Total_R3_MTUs_6</Question>
      <Boundary>0</Boundary>
    </Score>
    <Total result="Score_SIM_Total_R3_MTUs_7" method="sum">
      <Question validate="false">Score_SIM_Total_R2_MTUs_7</Question>
      <Question validate="false">Score_SIM_R3_Scenario1_MTUs_7</Question>
    </Total>
    <Score type="Range" result="Score_SIM_Total_R3_MTUs_7_Rng">
      <Question>Score_SIM_Total_R3_MTUs_7</Question>
      <Boundary>0</Boundary>
    </Score>
    <Total result="Score_SIM_Total_R3_MTUs_8" method="sum">
      <Question validate="false">Score_SIM_Total_R2_MTUs_8</Question>
      <Question validate="false">Score_SIM_R3_Scenario1_MTUs_8</Question>
    </Total>
    <Score type="Range" result="Score_SIM_Total_R3_MTUs_8_Rng">
      <Question>Score_SIM_Total_R3_MTUs_8</Question>
      <Boundary>0</Boundary>
    </Score>
    <Total result="Score_SIM_Total_R3_KPI1_MTUs_Impact_Pre" method="sum">
      <Question validate="false">Score_SIM_MTUs_Offset</Question>
      <Question validate="false">Score_SIM_Total_R3_MTUs_1_Rng</Question>
      <Question validate="false">Score_SIM_Total_R3_MTUs_2_Rng</Question>
      <Question validate="false">Score_SIM_Total_R3_MTUs_3_Rng</Question>
      <Question validate="false">Score_SIM_Total_R3_MTUs_4_Rng</Question>
      <Question validate="false">Score_SIM_Total_R3_MTUs_5_Rng</Question>
      <Question validate="false">Score_SIM_Total_R3_MTUs_6_Rng</Question>
      <Question validate="false">Score_SIM_Total_R3_MTUs_7_Rng</Question>
      <Question validate="false">Score_SIM_Total_R3_MTUs_8_Rng</Question>
    </Total>
    <Total result="Score_SIM_Total_R3_KPI1_MTUs_Impact" method="multiply">
      <Question validate="false">Score_SIM_MTUs_Multiplier</Question>
      <Question validate="false">Score_SIM_Total_R3_KPI1_MTUs_Impact_Pre</Question>
    </Total>


    <!-- TOTAL R3 Base KPIs -->
    
    <Total result="Score_SIM_Total_R3_KPI1_Base" method="sum">
      <Question validate="false">Score_SIM_Total_R2_KPI1</Question>
      <Question validate="false">Score_SIM_R3_Scenario1_KPI1</Question>
    </Total>
    <Total result="Score_SIM_Total_R3_KPI1_Impacts" method="multiply">
      <Question validate="false">Score_SIM_Total_R3_KPI1_Base</Question>
      <Question validate="false">Score_SIM_Total_R3_KPI1_MTUs_Impact</Question>
    </Total>
    <Total result="Score_SIM_Total_R3_KPI1" method="sum">
      <Question validate="false">Score_SIM_Total_R3_KPI1_Base</Question>
      <Question validate="false">Score_SIM_Total_R3_KPI1_Impacts</Question>
    </Total>
    <Total result="Score_SIM_Total_R3_KPI2" method="sum">
      <Question validate="false">Score_SIM_Total_R2_KPI2</Question>
      <Question validate="false">Score_SIM_R3_Scenario1_KPI2</Question>
    </Total>
    <Total result="Score_SIM_Total_R3_KPI3" method="sum">
      <Question validate="false">Score_SIM_Total_R2_KPI3</Question>
      <Question validate="false">Score_SIM_R3_Scenario1_KPI3</Question>
    </Total>
    <Total result="Score_SIM_Total_R3_KPI4" method="sum">
      <Question validate="false">Score_SIM_Total_R2_KPI4</Question>
      <Question validate="false">Score_SIM_R3_Scenario1_KPI4</Question>
    </Total>


    <!-- Roleplay R3 -->

    <Total result="Score_SIM_R3_Roleplay_Impact" method="multiply">  
      <Question validate="false">Score_SIM_Roleplay_Multiplier</Question>
      <Question validate="false">Score_SIM_R3_Roleplay</Question>
    </Total>


    <!-- Final R3 -->
    
    <Total result="Score_SIM_Total_R3" method="sum">
      <Question validate="false">Score_SIM_R2_Roleplay_Impact</Question>
      <Question validate="false">Score_SIM_Total_R3_KPI1</Question>
      <Question validate="false">Score_SIM_Total_R3_KPI2</Question>
      <Question validate="false">Score_SIM_Total_R3_KPI3</Question>
      <Question validate="false">Score_SIM_Total_R3_KPI4</Question>
      <Question validate="false">Score_SIM_R3_Roleplay_Impact</Question>
    </Total>


    <!-- MTUs R4, each negative MTU result will impact KPI1 by -0.01% -->

    <Total result="Score_SIM_Total_R4_MTUs_1" method="sum">
      <Question validate="false">Score_SIM_Total_R3_MTUs_1</Question>
      <Question validate="false">Score_SIM_R4_Scenario1_MTUs_1</Question>
    </Total>
    <Score type="Range" result="Score_SIM_Total_R4_MTUs_1_Rng">
      <Question>Score_SIM_Total_R4_MTUs_1</Question>
      <Boundary>0</Boundary>
    </Score>
    <Total result="Score_SIM_Total_R4_MTUs_2" method="sum">
      <Question validate="false">Score_SIM_Total_R3_MTUs_2</Question>
      <Question validate="false">Score_SIM_R4_Scenario1_MTUs_2</Question>
    </Total>
    <Score type="Range" result="Score_SIM_Total_R4_MTUs_2_Rng">
      <Question>Score_SIM_Total_R4_MTUs_2</Question>
      <Boundary>0</Boundary>
    </Score>
    <Total result="Score_SIM_Total_R4_MTUs_3" method="sum">
      <Question validate="false">Score_SIM_Total_R3_MTUs_3</Question>
      <Question validate="false">Score_SIM_R4_Scenario1_MTUs_3</Question>
    </Total>
    <Score type="Range" result="Score_SIM_Total_R4_MTUs_3_Rng">
      <Question>Score_SIM_Total_R4_MTUs_3</Question>
      <Boundary>0</Boundary>
    </Score>
    <Total result="Score_SIM_Total_R4_MTUs_4" method="sum">
      <Question validate="false">Score_SIM_Total_R3_MTUs_4</Question>
      <Question validate="false">Score_SIM_R4_Scenario1_MTUs_4</Question>
    </Total>
    <Score type="Range" result="Score_SIM_Total_R4_MTUs_4_Rng">
      <Question>Score_SIM_Total_R4_MTUs_4</Question>
      <Boundary>0</Boundary>
    </Score>
    <Total result="Score_SIM_Total_R4_MTUs_5" method="sum">
      <Question validate="false">Score_SIM_Total_R3_MTUs_5</Question>
      <Question validate="false">Score_SIM_R4_Scenario1_MTUs_5</Question>
    </Total>
    <Score type="Range" result="Score_SIM_Total_R4_MTUs_5_Rng">
      <Question>Score_SIM_Total_R4_MTUs_5</Question>
      <Boundary>0</Boundary>
    </Score>
    <Total result="Score_SIM_Total_R4_MTUs_6" method="sum">
      <Question validate="false">Score_SIM_Total_R3_MTUs_6</Question>
      <Question validate="false">Score_SIM_R4_Scenario1_MTUs_6</Question>
    </Total>
    <Score type="Range" result="Score_SIM_Total_R4_MTUs_6_Rng">
      <Question>Score_SIM_Total_R4_MTUs_6</Question>
      <Boundary>0</Boundary>
    </Score>
    <Total result="Score_SIM_Total_R4_MTUs_7" method="sum">
      <Question validate="false">Score_SIM_Total_R3_MTUs_7</Question>
      <Question validate="false">Score_SIM_R4_Scenario1_MTUs_7</Question>
    </Total>
    <Score type="Range" result="Score_SIM_Total_R4_MTUs_7_Rng">
      <Question>Score_SIM_Total_R4_MTUs_7</Question>
      <Boundary>0</Boundary>
    </Score>
    <Total result="Score_SIM_Total_R4_MTUs_8" method="sum">
      <Question validate="false">Score_SIM_Total_R3_MTUs_8</Question>
      <Question validate="false">Score_SIM_R4_Scenario1_MTUs_8</Question>
    </Total>
    <Score type="Range" result="Score_SIM_Total_R4_MTUs_8_Rng">
      <Question>Score_SIM_Total_R4_MTUs_8</Question>
      <Boundary>0</Boundary>
    </Score>
    <Total result="Score_SIM_Total_R4_KPI1_MTUs_Impact_Pre" method="sum">
      <Question validate="false">Score_SIM_MTUs_Offset</Question>
      <Question validate="false">Score_SIM_Total_R4_MTUs_1_Rng</Question>
      <Question validate="false">Score_SIM_Total_R4_MTUs_2_Rng</Question>
      <Question validate="false">Score_SIM_Total_R4_MTUs_3_Rng</Question>
      <Question validate="false">Score_SIM_Total_R4_MTUs_4_Rng</Question>
      <Question validate="false">Score_SIM_Total_R4_MTUs_5_Rng</Question>
      <Question validate="false">Score_SIM_Total_R4_MTUs_6_Rng</Question>
      <Question validate="false">Score_SIM_Total_R4_MTUs_7_Rng</Question>
      <Question validate="false">Score_SIM_Total_R4_MTUs_8_Rng</Question>
    </Total>
    <Total result="Score_SIM_Total_R4_KPI1_MTUs_Impact" method="multiply">
      <Question validate="false">Score_SIM_MTUs_Multiplier</Question>
      <Question validate="false">Score_SIM_Total_R4_KPI1_MTUs_Impact_Pre</Question>
    </Total>


    <!-- TOTAL R4 Base KPIs -->
    
    <Total result="Score_SIM_Total_R4_KPI1_Base" method="sum">
      <Question validate="false">Score_SIM_Total_R3_KPI1</Question>
      <Question validate="false">Score_SIM_R4_Scenario1_KPI1</Question>
    </Total>
    <Total result="Score_SIM_Total_R4_KPI1_Impacts" method="multiply">
      <Question validate="false">Score_SIM_Total_R4_KPI1_Base</Question>
      <Question validate="false">Score_SIM_Total_R4_KPI1_MTUs_Impact</Question>
    </Total>
    <Total result="Score_SIM_Total_R4_KPI1" method="sum">
      <Question validate="false">Score_SIM_Total_R4_KPI1_Base</Question>
      <Question validate="false">Score_SIM_Total_R4_KPI1_Impacts</Question>
    </Total>
    <Total result="Score_SIM_Total_R4_KPI2" method="sum">
      <Question validate="false">Score_SIM_Total_R3_KPI2</Question>
      <Question validate="false">Score_SIM_R4_Scenario1_KPI2</Question>
    </Total>
    <Total result="Score_SIM_Total_R4_KPI3" method="sum">
      <Question validate="false">Score_SIM_Total_R3_KPI3</Question>
      <Question validate="false">Score_SIM_R4_Scenario1_KPI3</Question>
    </Total>
    <Total result="Score_SIM_Total_R4_KPI4" method="sum">
      <Question validate="false">Score_SIM_Total_R3_KPI4</Question>
      <Question validate="false">Score_SIM_R4_Scenario1_KPI4</Question>
    </Total>


    <!-- Roleplay R4 -->

    <Total result="Score_SIM_R4_Roleplay_Impact" method="multiply">  
      <Question validate="false">Score_SIM_Roleplay_Multiplier</Question>
      <Question validate="false">Score_SIM_R4_Roleplay</Question>
    </Total>


    <!-- Final R4 -->
    
    <Total result="Score_SIM_Total_R4" method="sum">
      <Question validate="false">Score_SIM_R3_Roleplay_Impact</Question>
      <Question validate="false">Score_SIM_Total_R4_KPI1</Question>
      <Question validate="false">Score_SIM_Total_R4_KPI2</Question>
      <Question validate="false">Score_SIM_Total_R4_KPI3</Question>
      <Question validate="false">Score_SIM_Total_R4_KPI4</Question>
      <Question validate="false">Score_SIM_R4_Roleplay_Impact</Question>
    </Total>

  </Aggregator>

</Action>