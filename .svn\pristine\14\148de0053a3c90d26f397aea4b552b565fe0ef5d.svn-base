@charset "UTF-8";

@import "../materialize-src/sass/components/color-variables";
// @import "../materialize-src/sass/components/variables";
@import "mixins";


@include header-help();

.wizlet.wizletTabs {

    .tabs-holder {
        width: 104%;
        margin-left: -2%;
        margin-bottom: 5px;

        ul.tabs {
            @include scroll-x-hidden();
            @include border-radius (20px);

            li.tab {
                width: auto;
                border-color: rgba(color("client-colors", "border"), 0.2);
                border-style: solid;
                border-width: 0 1px 0 1px;
                &:first-child { border-left-width: 0; }
                &.last { border-right-width: 0; }

                a {
                    color: color("client-colors", "font2");
                    background-color: color("client-colors", "tab3");
                    position: relative;
                    // padding: 0 5px;
                    padding: 0;
                    font-size: 1rem;
                    &:hover {
                        background-color: color("client-colors", "tab2");
                        color: color("client-colors", "font2");
                    }
                    &.active, &:focus.active {
                        // background-color: rgba(color("client-colors", "primary"), 0.2);
                        background-color: color("client-colors", "tab1");
                        color: color("client-colors", "font2");
                    }
                    
                    @media #{$medium-and-up} { 
                        // padding: 0 5px;
                        font-size: 1.2rem;
                    }
                    @media #{$large-and-up} { 
                        // padding: 0 10px;
                        font-size: 1.3rem;
                    }
                    @media #{$extra-large-and-up} { 
                        font-size: 1.4rem;
                    }
                    @media #{$mega-large-and-up} { 
                        font-size: 1.5rem;
                    }

                    i.check-icon {
                        position: absolute;
                        right: 20px;
                    }
                }
            }

            li.indicator {
                display: none;
            }
        }


        @media #{$small-and-down} { 
        
            &.vertical-mode {
                width: 10%;
                left: 0;
                position: absolute;
                margin: 0;

                ul.tabs {
                    display: block;
                    height: auto;
                    background-color: transparent;
                    box-shadow: none;
                    overflow-x: hidden;
                    overflow-y: auto;

                    >li.tab {
                        display: block;
                        border: none;
                        padding: 5px 0;
                        $tabheight: 40px;
                        height: $tabheight;

                        &:first-child { 
                            border-top-width: 0; 
                            padding-top: 0;
                        }
                        &.last { 
                            border-bottom-width: 0; 
                            padding-bottom: 0;
                        }

                        >a {
                            border-radius: 0 10px 10px 0;
                            font-size: $tabheight/2;
                            line-height: $tabheight - 5;              
                        }
                    }
                }
            }
        }

        &.small {
            ul.tabs li.tab a {
                @media #{$medium-and-up} { 
                    font-size: 1rem;
                }
                @media #{$large-and-up} { 
                    font-size: 1.1rem;
                }
                @media #{$extra-large-and-up} { 
                    font-size: 1.2rem;
                }
                @media #{$mega-large-and-up} { 
                    font-size: 1.3rem;
                }
            }

        }
    }
}

.tabs-content {

    &.carousel.carousel-slider {
        height: auto !important;
        
        .carousel-item {

            min-height: auto;
            &.active{
                position: relative;
            }
            
            @include scroll-x-hidden();
            @include scroll-y-hidden();
        }
        
    }

    // .container {
    //     margin: 0;
    //     width: auto;
    // }
    
} 


.modal-content .wizlet.wizletTabs .tabs-holder {
        width: initial;
        margin-left: 0;
}