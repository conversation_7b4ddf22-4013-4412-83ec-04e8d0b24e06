<?xml version="1.0" encoding="utf-8" ?>
<Action>


  <Component type="Vanilla">
    <![CDATA[{
      templateInEvent: "html/card.dot",
      css: "styles/card.css",
      orientation: "vertical",
      header: "!{}",
      subheader: "!{}",
      instructions: "!{}",
      valign: false,
      animate: "fadeIn",
      content: {
        title: "!{GD_SIM_R1_Finish_Part1}",
        body: "!{GD_SIM_R1_Finish_Modal_Part1}"
      }      
    }]]>
  </Component>


  
  <!-- Navigation component
       parameters:
        - aligned:
        - animate: kind of animation
        - id: to be located and shown when is hidden
        - isHidden: attribute to hide
        - buttons: array of buttons (previous, next or target)
   -->
  <Component type="Navigation" customJS="true">
    <![CDATA[{
      templateInEvent: "html/navigation.dot",
      css: "styles/navigation.css",
      aligned: "right",
      animate: "",
      id: "navButton",
      isHidden: false, showDelay: "",
      hasModals: true,
      buttons: [
        {
          type: "target",
          pulse: false,
          gdActionTrack: "GD",
          gdActionSection: "SIM_R1_Finish_BackDirector_AGG",
          targetSection: "R1_DebriefPage_FAC",
          label: "!{GD_SIM_R1_Finish_Part1}",
          icon: "cancel"
        }
      ],
      
      isFollower: "Follower",
      scope: ["Follower"]
    }]]>
  </Component>



</Action>